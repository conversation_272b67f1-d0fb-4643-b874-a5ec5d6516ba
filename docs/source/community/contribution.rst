.. _contribution:

============
Contribution
============

This document serves as the definitive guide for contributing to the codebase. We welcome your patches and contributions to the project and appreciate your support.

To ensure a smooth process, please follow a few simple guidelines.


Getting Started
---------------

Please make sure to read and observe our `Code of Conduct <https://github.com/vllm-project/aibrix/blob/main/CODE_OF_CONDUCT.md>`_.


Make your first contribution
----------------------------

Find something to start with
^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Help is always welcome! For example, documentation can always use improvement.
There’s always code that can be clarified and variables or functions that can be renamed or commented.
There’s always a need for more test coverage. You get the idea - if you ever see something you think should be fixed, you should own it.
Here is how you get started.

Good Start issues
^^^^^^^^^^^^^^^^^

To find AIBrix issues that make good entry points:

- Start with issues labeled `good first issue <https://github.com/vllm-project/aibrix/labels/good%20first%20issue>`_.
- For issues that require deeper knowledge of one or more technical aspects, look at issues labeled `help wanted <https://github.com/vllm-project/aibrix/labels/help%20wanted>`_.

Joining the community
---------------------

Follow these instructions if you want to:

Become an official member of the AIBrix GitHub Org
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Before requesting to join the AIBrix GitHub Org, we kindly ask that you make a few initial contributions to demonstrate your intent to continue contributing to AIBrix.

There are a number of ways to contribute to AIBrix:

- Submit PRs (codes or docs)
- File issues reporting bugs or providing feedback
- Answer questions on Slack or GitHub issues

When you are ready to join, please send an issue asking adding yourself as a member in the issue.


PR process
----------

Enrich your PR information
^^^^^^^^^^^^^^^^^^^^^^^^^^

1. **Provide Clear Descriptions**: Ensure your PR includes a concise yet informative description. Summarize the problem being solved, the approach taken, and any notable changes or additions to the codebase. This helps reviewers quickly understand the purpose of the PR.

2. **Reference Related Issues**: Link to any related issues, discussion threads, or feature requests in the description. Use keywords like Fixes #IssueNumber to automatically close an issue when the PR is merged.

3. **Include Context for Major Changes**: If the PR involves significant changes or refactors, include a brief rationale for why these changes are necessary and how they improve the project.

4. **Document New Features**: If the PR introduces a new feature, ensure that documentation (e.g., README updates, code comments) is included or planned as part of the PR.

5. **Provide Test Coverage**: If applicable, describe the tests you've added to cover the changes in the PR. This helps reviewers assess if the code is robust and if potential edge cases are handled.

6. **Use Clear Commit Messages**: Each commit message should be clear and focused on one change or improvement. Commit titles should describe the change in 50 characters or less, and more details can be added in the commit body.


Code Review
^^^^^^^^^^^

1. **Be Open to Feedback**: Reviewers may suggest improvements, point out bugs, or ask for clarifications. Be open to suggestions, even if you don’t agree initially. Use this as an opportunity to strengthen your code.

2. **Respond Promptly**: Acknowledge feedback with comments explaining how you’ll address the concerns or why a change may not be necessary. Keeping communication clear and respectful helps maintain a productive review process.

3. **Update Your PR Gradually**: Apply changes based on reviewer feedback as soon as possible. Avoid force-pushing updates unless necessary, as this can disrupt the review flow and history tracking.

4. **Clarify Unclear Feedback**: If you’re unsure about a comment, don’t hesitate to ask for clarification before making changes. Miscommunication can lead to unnecessary rework.

5. **Use Suggestions Appropriately**: If the reviewer uses GitHub’s suggestion feature, accept or modify the suggestions as needed, while ensuring the PR remains aligned with the original goal.

6. **Summarize Major Revisions**: If the PR undergoes significant revisions after the initial review, summarize the new changes to give reviewers a clearer understanding of what’s been updated.

