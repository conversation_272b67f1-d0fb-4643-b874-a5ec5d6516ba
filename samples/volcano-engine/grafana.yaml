allowVolumeExpansion: true
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: ebs-essd
parameters:
  ChargeType: PostPaid
  type: ESSD_PL0
  zone: cn-beijing-c
provisioner: ebs.csi.volcengine.com
reclaimPolicy: Delete
volumeBindingMode: Immediate

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: kube-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: ebs-essd
  volumeMode: Filesystem

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-dashboard
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      securityContext:
        fsGroup: 472
        supplementalGroups:
          - 0
      containers:
        - name: grafana
          image: aibrix-cn-beijing.cr.volces.com/aibrix/grafana:latest
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: "250m"
              memory: "750Mi"
            limits:
              cpu: "1"
              memory: "2Gi"
          volumeMounts:
            - name: grafana-pv
              mountPath: /var/lib/grafana
      volumes:
        - name: grafana-pv
          persistentVolumeClaim:
            claimName: grafana-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: kube-system
  labels:
    app: grafana
spec:
  selector:
    app: grafana
  ports:
    - protocol: TCP
      port: 3000
      targetPort: 3000
      name: http
  type: ClusterIP

