apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    adapter.model.aibrix.ai/enabled: "true"
    model.aibrix.ai/name: deepseek-coder-7b
    model.aibrix.ai/port: "8000"
  name: deepseek-coder-7b-v100
  namespace: default
spec:
  replicas: 0
  selector:
    matchLabels:
      adapter.model.aibrix.ai/enabled: "true"
      app: deepseek-coder-7b-v100
      model.aibrix.ai/name: deepseek-coder-7b
  template:
    metadata:
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: "8000"
        prometheus.io/scrape: "true"
      labels:
        adapter.model.aibrix.ai/enabled: "true"
        app: deepseek-coder-7b-v100
        model.aibrix.ai/name: deepseek-coder-7b
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: machine.cluster.vke.volcengine.com/gpu-name
                operator: In
                values:
                - Tesla-V100
      containers:
      - command:
        - python3
        - -m
        - vllm.entrypoints.openai.api_server
        - --host
        - 0.0.0.0
        - --port
        - "8000"
        - --uvicorn-log-level
        - warning
        - --model
        - /models/deepseek-coder-6.7b-instruct
        - --served-model-name
        - deepseek-coder-7b
        - --trust-remote-code
        - --max-model-len
        - "10240"
        - --api-key
        - sk-kFJ12nKsFVfVmGpj3QzX65s4RbN2xJqWzPYCjYu7wT3BlbLi
        - --dtype
        - half
        image: aibrix-container-registry-cn-beijing.cr.volces.com/aibrix/vllm-openai:v0.6.2-distributed
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - |
                while true; do
                  RUNNING=$(curl -s http://localhost:8000/metrics | grep 'vllm:num_requests_running' | grep -v '#' | awk '{print $2}')
                  WAITING=$(curl -s http://localhost:8000/metrics | grep 'vllm:num_requests_waiting' | grep -v '#' | awk '{print $2}')
                  if [ "$RUNNING" = "0.0" ] && [ "$WAITING" = "0.0" ]; then
                    echo "Terminating: No active or waiting requests, safe to terminate" >> /proc/1/fd/1
                    exit 0
                  else
                    echo "Terminating: Running: $RUNNING, Waiting: $WAITING" >> /proc/1/fd/1
                    sleep 5
                  fi
                done
        name: llm-engine
        ports:
        - containerPort: 8000
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 90
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            nvidia.com/gpu: "1"
          requests:
            nvidia.com/gpu: "1"
        volumeMounts:
        - mountPath: /models
          name: model-hostpath
        - mountPath: /dev/shm
          name: dshm
      - command:
        - aibrix_runtime
        - --port
        - "8080"
        env:
        - name: INFERENCE_ENGINE
          value: vllm
        - name: INFERENCE_ENGINE_ENDPOINT
          value: http://localhost:8000
        image: aibrix-container-registry-cn-beijing.cr.volces.com/aibrix/runtime:v0.3.0
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 3
          periodSeconds: 2
        name: aibrix-runtime
        ports:
        - containerPort: 8080
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      initContainers:
      - command:
        - aibrix_download
        - --model-uri
        - tos://aibrix-artifact-testing/models/deepseek-ai/deepseek-coder-6.7b-instruct/
        - --local-dir
        - /models/
        env:
        - name: DOWNLOADER_MODEL_NAME
          value: deepseek-coder-6.7b-instruct
        - name: DOWNLOADER_NUM_THREADS
          value: "16"
        - name: DOWNLOADER_ALLOW_FILE_SUFFIX
          value: json, safetensors
        - name: DOWNLOADER_TOS_VERSION
          value: v1
        - name: TOS_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              key: TOS_ACCESS_KEY
              name: tos-credential
        - name: TOS_SECRET_KEY
          valueFrom:
            secretKeyRef:
              key: TOS_SECRET_KEY
              name: tos-credential
        - name: TOS_ENDPOINT
          value: tos-cn-beijing.ivolces.com
        - name: TOS_REGION
          value: cn-beijing
        image: aibrix-container-registry-cn-beijing.cr.volces.com/aibrix/runtime:v0.3.0
        name: init-model
        volumeMounts:
        - mountPath: /models
          name: model-hostpath
      serviceAccountName: default-app-sa
      terminationGracePeriodSeconds: 300
      volumes:
      - emptyDir:
          medium: Memory
          sizeLimit: 4Gi
        name: dshm
      - hostPath:
          path: /root/models
          type: DirectoryOrCreate
        name: model-hostpath