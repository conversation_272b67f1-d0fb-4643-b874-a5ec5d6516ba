# There is a python upstream issue and we switch 3.11 to 3.11.11, check aibrix/issues/959 for more details.
ARG PYTHON_VERSION=3.11.11
ARG BASE_IMAGE=python:${PYTHON_VERSION}-slim-bookworm

# Builder stage
FROM ${BASE_IMAGE} AS builder

WORKDIR /app

ARG POETRY_VERSION=1.8.3

# Install dependencies
RUN apt-get update \
    && apt-get install -y python3-dev build-essential \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*
    
# Install Poetry
RUN python3 -m pip install poetry==${POETRY_VERSION}

# Copy the runtime source
COPY python/aibrix/poetry.lock python/aibrix/pyproject.toml python/aibrix/ /app/

# Install dependencies and build package
RUN poetry config virtualenvs.create false \
    && poetry install --no-interaction --no-dev \
    && poetry build -f wheel

# Final stage
FROM ${BASE_IMAGE}

WORKDIR /app

COPY --from=builder /app/dist/*.whl ./

# Install build dependencies and clean up in one step (avoiding creating another new layer)
RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc python3-dev mawk \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && pip install --no-cache-dir ./*.whl \
    && rm -f ./*.whl \
    && apt-get purge -y gcc python3-dev \
    && apt-get autoremove -y

# Set entrypoint for Runtime
ENTRYPOINT ["aibrix_runtime"]
