/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package utils

import (
	"testing"

	"github.com/pkoukk/tiktoken-go"
	tiktoken_loader "github.com/pkoukk/tiktoken-go-loader"
	"github.com/stretchr/testify/assert"
)

func TestTokenizeInputText(t *testing.T) {
	inputStr := "Hello World, 你好世界"
	tokens, err := TokenizeInputText(inputStr)
	assert.Equal(t, nil, err)

	outputStr, err := DetokenizeText(tokens)
	assert.NoError(t, err)
	assert.Equal(t, inputStr, outputStr)

	tiktoken.SetBpeLoader(tiktoken_loader.NewOfflineLoader())
	tke, _ := tiktoken.GetEncoding(encoding)
	outputStr = tke.Decode(tokens)
	assert.Equal(t, inputStr, outputStr)
}
