/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"
	json "encoding/json"
	"fmt"

	v1alpha1 "github.com/vllm-project/aibrix/api/model/v1alpha1"
	modelv1alpha1 "github.com/vllm-project/aibrix/pkg/client/applyconfiguration/model/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeModelAdapters implements ModelAdapterInterface
type FakeModelAdapters struct {
	Fake *FakeModelV1alpha1
	ns   string
}

var modeladaptersResource = v1alpha1.SchemeGroupVersion.WithResource("modeladapters")

var modeladaptersKind = v1alpha1.SchemeGroupVersion.WithKind("ModelAdapter")

// Get takes name of the modelAdapter, and returns the corresponding modelAdapter object, and an error if there is any.
func (c *FakeModelAdapters) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.ModelAdapter, err error) {
	emptyResult := &v1alpha1.ModelAdapter{}
	obj, err := c.Fake.
		Invokes(testing.NewGetActionWithOptions(modeladaptersResource, c.ns, name, options), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.ModelAdapter), err
}

// List takes label and field selectors, and returns the list of ModelAdapters that match those selectors.
func (c *FakeModelAdapters) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.ModelAdapterList, err error) {
	emptyResult := &v1alpha1.ModelAdapterList{}
	obj, err := c.Fake.
		Invokes(testing.NewListActionWithOptions(modeladaptersResource, modeladaptersKind, c.ns, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha1.ModelAdapterList{ListMeta: obj.(*v1alpha1.ModelAdapterList).ListMeta}
	for _, item := range obj.(*v1alpha1.ModelAdapterList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested modelAdapters.
func (c *FakeModelAdapters) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchActionWithOptions(modeladaptersResource, c.ns, opts))

}

// Create takes the representation of a modelAdapter and creates it.  Returns the server's representation of the modelAdapter, and an error, if there is any.
func (c *FakeModelAdapters) Create(ctx context.Context, modelAdapter *v1alpha1.ModelAdapter, opts v1.CreateOptions) (result *v1alpha1.ModelAdapter, err error) {
	emptyResult := &v1alpha1.ModelAdapter{}
	obj, err := c.Fake.
		Invokes(testing.NewCreateActionWithOptions(modeladaptersResource, c.ns, modelAdapter, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.ModelAdapter), err
}

// Update takes the representation of a modelAdapter and updates it. Returns the server's representation of the modelAdapter, and an error, if there is any.
func (c *FakeModelAdapters) Update(ctx context.Context, modelAdapter *v1alpha1.ModelAdapter, opts v1.UpdateOptions) (result *v1alpha1.ModelAdapter, err error) {
	emptyResult := &v1alpha1.ModelAdapter{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateActionWithOptions(modeladaptersResource, c.ns, modelAdapter, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.ModelAdapter), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeModelAdapters) UpdateStatus(ctx context.Context, modelAdapter *v1alpha1.ModelAdapter, opts v1.UpdateOptions) (result *v1alpha1.ModelAdapter, err error) {
	emptyResult := &v1alpha1.ModelAdapter{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceActionWithOptions(modeladaptersResource, "status", c.ns, modelAdapter, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.ModelAdapter), err
}

// Delete takes name of the modelAdapter and deletes it. Returns an error if one occurs.
func (c *FakeModelAdapters) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(modeladaptersResource, c.ns, name, opts), &v1alpha1.ModelAdapter{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeModelAdapters) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionActionWithOptions(modeladaptersResource, c.ns, opts, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha1.ModelAdapterList{})
	return err
}

// Patch applies the patch and returns the patched modelAdapter.
func (c *FakeModelAdapters) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.ModelAdapter, err error) {
	emptyResult := &v1alpha1.ModelAdapter{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(modeladaptersResource, c.ns, name, pt, data, opts, subresources...), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.ModelAdapter), err
}

// Apply takes the given apply declarative configuration, applies it and returns the applied modelAdapter.
func (c *FakeModelAdapters) Apply(ctx context.Context, modelAdapter *modelv1alpha1.ModelAdapterApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.ModelAdapter, err error) {
	if modelAdapter == nil {
		return nil, fmt.Errorf("modelAdapter provided to Apply must not be nil")
	}
	data, err := json.Marshal(modelAdapter)
	if err != nil {
		return nil, err
	}
	name := modelAdapter.Name
	if name == nil {
		return nil, fmt.Errorf("modelAdapter.Name must be provided to Apply")
	}
	emptyResult := &v1alpha1.ModelAdapter{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(modeladaptersResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions()), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.ModelAdapter), err
}

// ApplyStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
func (c *FakeModelAdapters) ApplyStatus(ctx context.Context, modelAdapter *modelv1alpha1.ModelAdapterApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.ModelAdapter, err error) {
	if modelAdapter == nil {
		return nil, fmt.Errorf("modelAdapter provided to Apply must not be nil")
	}
	data, err := json.Marshal(modelAdapter)
	if err != nil {
		return nil, err
	}
	name := modelAdapter.Name
	if name == nil {
		return nil, fmt.Errorf("modelAdapter.Name must be provided to Apply")
	}
	emptyResult := &v1alpha1.ModelAdapter{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(modeladaptersResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions(), "status"), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.ModelAdapter), err
}
