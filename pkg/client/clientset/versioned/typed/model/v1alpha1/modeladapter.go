/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"

	v1alpha1 "github.com/vllm-project/aibrix/api/model/v1alpha1"
	modelv1alpha1 "github.com/vllm-project/aibrix/pkg/client/applyconfiguration/model/v1alpha1"
	scheme "github.com/vllm-project/aibrix/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// ModelAdaptersGetter has a method to return a ModelAdapterInterface.
// A group's client should implement this interface.
type ModelAdaptersGetter interface {
	ModelAdapters(namespace string) ModelAdapterInterface
}

// ModelAdapterInterface has methods to work with ModelAdapter resources.
type ModelAdapterInterface interface {
	Create(ctx context.Context, modelAdapter *v1alpha1.ModelAdapter, opts v1.CreateOptions) (*v1alpha1.ModelAdapter, error)
	Update(ctx context.Context, modelAdapter *v1alpha1.ModelAdapter, opts v1.UpdateOptions) (*v1alpha1.ModelAdapter, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, modelAdapter *v1alpha1.ModelAdapter, opts v1.UpdateOptions) (*v1alpha1.ModelAdapter, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.ModelAdapter, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.ModelAdapterList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.ModelAdapter, err error)
	Apply(ctx context.Context, modelAdapter *modelv1alpha1.ModelAdapterApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.ModelAdapter, err error)
	// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
	ApplyStatus(ctx context.Context, modelAdapter *modelv1alpha1.ModelAdapterApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.ModelAdapter, err error)
	ModelAdapterExpansion
}

// modelAdapters implements ModelAdapterInterface
type modelAdapters struct {
	*gentype.ClientWithListAndApply[*v1alpha1.ModelAdapter, *v1alpha1.ModelAdapterList, *modelv1alpha1.ModelAdapterApplyConfiguration]
}

// newModelAdapters returns a ModelAdapters
func newModelAdapters(c *ModelV1alpha1Client, namespace string) *modelAdapters {
	return &modelAdapters{
		gentype.NewClientWithListAndApply[*v1alpha1.ModelAdapter, *v1alpha1.ModelAdapterList, *modelv1alpha1.ModelAdapterApplyConfiguration](
			"modeladapters",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *v1alpha1.ModelAdapter { return &v1alpha1.ModelAdapter{} },
			func() *v1alpha1.ModelAdapterList { return &v1alpha1.ModelAdapterList{} }),
	}
}
