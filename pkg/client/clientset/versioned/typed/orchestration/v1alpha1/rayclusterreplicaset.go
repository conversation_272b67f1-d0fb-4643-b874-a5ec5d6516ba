/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"

	v1alpha1 "github.com/vllm-project/aibrix/api/orchestration/v1alpha1"
	orchestrationv1alpha1 "github.com/vllm-project/aibrix/pkg/client/applyconfiguration/orchestration/v1alpha1"
	scheme "github.com/vllm-project/aibrix/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// RayClusterReplicaSetsGetter has a method to return a RayClusterReplicaSetInterface.
// A group's client should implement this interface.
type RayClusterReplicaSetsGetter interface {
	RayClusterReplicaSets(namespace string) RayClusterReplicaSetInterface
}

// RayClusterReplicaSetInterface has methods to work with RayClusterReplicaSet resources.
type RayClusterReplicaSetInterface interface {
	Create(ctx context.Context, rayClusterReplicaSet *v1alpha1.RayClusterReplicaSet, opts v1.CreateOptions) (*v1alpha1.RayClusterReplicaSet, error)
	Update(ctx context.Context, rayClusterReplicaSet *v1alpha1.RayClusterReplicaSet, opts v1.UpdateOptions) (*v1alpha1.RayClusterReplicaSet, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, rayClusterReplicaSet *v1alpha1.RayClusterReplicaSet, opts v1.UpdateOptions) (*v1alpha1.RayClusterReplicaSet, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.RayClusterReplicaSet, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.RayClusterReplicaSetList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.RayClusterReplicaSet, err error)
	Apply(ctx context.Context, rayClusterReplicaSet *orchestrationv1alpha1.RayClusterReplicaSetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterReplicaSet, err error)
	// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
	ApplyStatus(ctx context.Context, rayClusterReplicaSet *orchestrationv1alpha1.RayClusterReplicaSetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterReplicaSet, err error)
	RayClusterReplicaSetExpansion
}

// rayClusterReplicaSets implements RayClusterReplicaSetInterface
type rayClusterReplicaSets struct {
	*gentype.ClientWithListAndApply[*v1alpha1.RayClusterReplicaSet, *v1alpha1.RayClusterReplicaSetList, *orchestrationv1alpha1.RayClusterReplicaSetApplyConfiguration]
}

// newRayClusterReplicaSets returns a RayClusterReplicaSets
func newRayClusterReplicaSets(c *OrchestrationV1alpha1Client, namespace string) *rayClusterReplicaSets {
	return &rayClusterReplicaSets{
		gentype.NewClientWithListAndApply[*v1alpha1.RayClusterReplicaSet, *v1alpha1.RayClusterReplicaSetList, *orchestrationv1alpha1.RayClusterReplicaSetApplyConfiguration](
			"rayclusterreplicasets",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *v1alpha1.RayClusterReplicaSet { return &v1alpha1.RayClusterReplicaSet{} },
			func() *v1alpha1.RayClusterReplicaSetList { return &v1alpha1.RayClusterReplicaSetList{} }),
	}
}
