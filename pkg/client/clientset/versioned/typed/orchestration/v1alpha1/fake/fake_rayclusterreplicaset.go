/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"
	json "encoding/json"
	"fmt"

	v1alpha1 "github.com/vllm-project/aibrix/api/orchestration/v1alpha1"
	orchestrationv1alpha1 "github.com/vllm-project/aibrix/pkg/client/applyconfiguration/orchestration/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeRayClusterReplicaSets implements RayClusterReplicaSetInterface
type FakeRayClusterReplicaSets struct {
	Fake *FakeOrchestrationV1alpha1
	ns   string
}

var rayclusterreplicasetsResource = v1alpha1.SchemeGroupVersion.WithResource("rayclusterreplicasets")

var rayclusterreplicasetsKind = v1alpha1.SchemeGroupVersion.WithKind("RayClusterReplicaSet")

// Get takes name of the rayClusterReplicaSet, and returns the corresponding rayClusterReplicaSet object, and an error if there is any.
func (c *FakeRayClusterReplicaSets) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.RayClusterReplicaSet, err error) {
	emptyResult := &v1alpha1.RayClusterReplicaSet{}
	obj, err := c.Fake.
		Invokes(testing.NewGetActionWithOptions(rayclusterreplicasetsResource, c.ns, name, options), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterReplicaSet), err
}

// List takes label and field selectors, and returns the list of RayClusterReplicaSets that match those selectors.
func (c *FakeRayClusterReplicaSets) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.RayClusterReplicaSetList, err error) {
	emptyResult := &v1alpha1.RayClusterReplicaSetList{}
	obj, err := c.Fake.
		Invokes(testing.NewListActionWithOptions(rayclusterreplicasetsResource, rayclusterreplicasetsKind, c.ns, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha1.RayClusterReplicaSetList{ListMeta: obj.(*v1alpha1.RayClusterReplicaSetList).ListMeta}
	for _, item := range obj.(*v1alpha1.RayClusterReplicaSetList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested rayClusterReplicaSets.
func (c *FakeRayClusterReplicaSets) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchActionWithOptions(rayclusterreplicasetsResource, c.ns, opts))

}

// Create takes the representation of a rayClusterReplicaSet and creates it.  Returns the server's representation of the rayClusterReplicaSet, and an error, if there is any.
func (c *FakeRayClusterReplicaSets) Create(ctx context.Context, rayClusterReplicaSet *v1alpha1.RayClusterReplicaSet, opts v1.CreateOptions) (result *v1alpha1.RayClusterReplicaSet, err error) {
	emptyResult := &v1alpha1.RayClusterReplicaSet{}
	obj, err := c.Fake.
		Invokes(testing.NewCreateActionWithOptions(rayclusterreplicasetsResource, c.ns, rayClusterReplicaSet, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterReplicaSet), err
}

// Update takes the representation of a rayClusterReplicaSet and updates it. Returns the server's representation of the rayClusterReplicaSet, and an error, if there is any.
func (c *FakeRayClusterReplicaSets) Update(ctx context.Context, rayClusterReplicaSet *v1alpha1.RayClusterReplicaSet, opts v1.UpdateOptions) (result *v1alpha1.RayClusterReplicaSet, err error) {
	emptyResult := &v1alpha1.RayClusterReplicaSet{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateActionWithOptions(rayclusterreplicasetsResource, c.ns, rayClusterReplicaSet, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterReplicaSet), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeRayClusterReplicaSets) UpdateStatus(ctx context.Context, rayClusterReplicaSet *v1alpha1.RayClusterReplicaSet, opts v1.UpdateOptions) (result *v1alpha1.RayClusterReplicaSet, err error) {
	emptyResult := &v1alpha1.RayClusterReplicaSet{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceActionWithOptions(rayclusterreplicasetsResource, "status", c.ns, rayClusterReplicaSet, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterReplicaSet), err
}

// Delete takes name of the rayClusterReplicaSet and deletes it. Returns an error if one occurs.
func (c *FakeRayClusterReplicaSets) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(rayclusterreplicasetsResource, c.ns, name, opts), &v1alpha1.RayClusterReplicaSet{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeRayClusterReplicaSets) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionActionWithOptions(rayclusterreplicasetsResource, c.ns, opts, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha1.RayClusterReplicaSetList{})
	return err
}

// Patch applies the patch and returns the patched rayClusterReplicaSet.
func (c *FakeRayClusterReplicaSets) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.RayClusterReplicaSet, err error) {
	emptyResult := &v1alpha1.RayClusterReplicaSet{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(rayclusterreplicasetsResource, c.ns, name, pt, data, opts, subresources...), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterReplicaSet), err
}

// Apply takes the given apply declarative configuration, applies it and returns the applied rayClusterReplicaSet.
func (c *FakeRayClusterReplicaSets) Apply(ctx context.Context, rayClusterReplicaSet *orchestrationv1alpha1.RayClusterReplicaSetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterReplicaSet, err error) {
	if rayClusterReplicaSet == nil {
		return nil, fmt.Errorf("rayClusterReplicaSet provided to Apply must not be nil")
	}
	data, err := json.Marshal(rayClusterReplicaSet)
	if err != nil {
		return nil, err
	}
	name := rayClusterReplicaSet.Name
	if name == nil {
		return nil, fmt.Errorf("rayClusterReplicaSet.Name must be provided to Apply")
	}
	emptyResult := &v1alpha1.RayClusterReplicaSet{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(rayclusterreplicasetsResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions()), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterReplicaSet), err
}

// ApplyStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
func (c *FakeRayClusterReplicaSets) ApplyStatus(ctx context.Context, rayClusterReplicaSet *orchestrationv1alpha1.RayClusterReplicaSetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterReplicaSet, err error) {
	if rayClusterReplicaSet == nil {
		return nil, fmt.Errorf("rayClusterReplicaSet provided to Apply must not be nil")
	}
	data, err := json.Marshal(rayClusterReplicaSet)
	if err != nil {
		return nil, err
	}
	name := rayClusterReplicaSet.Name
	if name == nil {
		return nil, fmt.Errorf("rayClusterReplicaSet.Name must be provided to Apply")
	}
	emptyResult := &v1alpha1.RayClusterReplicaSet{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(rayclusterreplicasetsResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions(), "status"), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterReplicaSet), err
}
