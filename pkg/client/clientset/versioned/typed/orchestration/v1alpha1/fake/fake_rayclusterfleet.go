/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"
	json "encoding/json"
	"fmt"

	v1alpha1 "github.com/vllm-project/aibrix/api/orchestration/v1alpha1"
	orchestrationv1alpha1 "github.com/vllm-project/aibrix/pkg/client/applyconfiguration/orchestration/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeRayClusterFleets implements RayClusterFleetInterface
type FakeRayClusterFleets struct {
	Fake *FakeOrchestrationV1alpha1
	ns   string
}

var rayclusterfleetsResource = v1alpha1.SchemeGroupVersion.WithResource("rayclusterfleets")

var rayclusterfleetsKind = v1alpha1.SchemeGroupVersion.WithKind("RayClusterFleet")

// Get takes name of the rayClusterFleet, and returns the corresponding rayClusterFleet object, and an error if there is any.
func (c *FakeRayClusterFleets) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.RayClusterFleet, err error) {
	emptyResult := &v1alpha1.RayClusterFleet{}
	obj, err := c.Fake.
		Invokes(testing.NewGetActionWithOptions(rayclusterfleetsResource, c.ns, name, options), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterFleet), err
}

// List takes label and field selectors, and returns the list of RayClusterFleets that match those selectors.
func (c *FakeRayClusterFleets) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.RayClusterFleetList, err error) {
	emptyResult := &v1alpha1.RayClusterFleetList{}
	obj, err := c.Fake.
		Invokes(testing.NewListActionWithOptions(rayclusterfleetsResource, rayclusterfleetsKind, c.ns, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha1.RayClusterFleetList{ListMeta: obj.(*v1alpha1.RayClusterFleetList).ListMeta}
	for _, item := range obj.(*v1alpha1.RayClusterFleetList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested rayClusterFleets.
func (c *FakeRayClusterFleets) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchActionWithOptions(rayclusterfleetsResource, c.ns, opts))

}

// Create takes the representation of a rayClusterFleet and creates it.  Returns the server's representation of the rayClusterFleet, and an error, if there is any.
func (c *FakeRayClusterFleets) Create(ctx context.Context, rayClusterFleet *v1alpha1.RayClusterFleet, opts v1.CreateOptions) (result *v1alpha1.RayClusterFleet, err error) {
	emptyResult := &v1alpha1.RayClusterFleet{}
	obj, err := c.Fake.
		Invokes(testing.NewCreateActionWithOptions(rayclusterfleetsResource, c.ns, rayClusterFleet, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterFleet), err
}

// Update takes the representation of a rayClusterFleet and updates it. Returns the server's representation of the rayClusterFleet, and an error, if there is any.
func (c *FakeRayClusterFleets) Update(ctx context.Context, rayClusterFleet *v1alpha1.RayClusterFleet, opts v1.UpdateOptions) (result *v1alpha1.RayClusterFleet, err error) {
	emptyResult := &v1alpha1.RayClusterFleet{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateActionWithOptions(rayclusterfleetsResource, c.ns, rayClusterFleet, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterFleet), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeRayClusterFleets) UpdateStatus(ctx context.Context, rayClusterFleet *v1alpha1.RayClusterFleet, opts v1.UpdateOptions) (result *v1alpha1.RayClusterFleet, err error) {
	emptyResult := &v1alpha1.RayClusterFleet{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceActionWithOptions(rayclusterfleetsResource, "status", c.ns, rayClusterFleet, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterFleet), err
}

// Delete takes name of the rayClusterFleet and deletes it. Returns an error if one occurs.
func (c *FakeRayClusterFleets) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(rayclusterfleetsResource, c.ns, name, opts), &v1alpha1.RayClusterFleet{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeRayClusterFleets) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionActionWithOptions(rayclusterfleetsResource, c.ns, opts, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha1.RayClusterFleetList{})
	return err
}

// Patch applies the patch and returns the patched rayClusterFleet.
func (c *FakeRayClusterFleets) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.RayClusterFleet, err error) {
	emptyResult := &v1alpha1.RayClusterFleet{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(rayclusterfleetsResource, c.ns, name, pt, data, opts, subresources...), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterFleet), err
}

// Apply takes the given apply declarative configuration, applies it and returns the applied rayClusterFleet.
func (c *FakeRayClusterFleets) Apply(ctx context.Context, rayClusterFleet *orchestrationv1alpha1.RayClusterFleetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterFleet, err error) {
	if rayClusterFleet == nil {
		return nil, fmt.Errorf("rayClusterFleet provided to Apply must not be nil")
	}
	data, err := json.Marshal(rayClusterFleet)
	if err != nil {
		return nil, err
	}
	name := rayClusterFleet.Name
	if name == nil {
		return nil, fmt.Errorf("rayClusterFleet.Name must be provided to Apply")
	}
	emptyResult := &v1alpha1.RayClusterFleet{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(rayclusterfleetsResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions()), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterFleet), err
}

// ApplyStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
func (c *FakeRayClusterFleets) ApplyStatus(ctx context.Context, rayClusterFleet *orchestrationv1alpha1.RayClusterFleetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterFleet, err error) {
	if rayClusterFleet == nil {
		return nil, fmt.Errorf("rayClusterFleet provided to Apply must not be nil")
	}
	data, err := json.Marshal(rayClusterFleet)
	if err != nil {
		return nil, err
	}
	name := rayClusterFleet.Name
	if name == nil {
		return nil, fmt.Errorf("rayClusterFleet.Name must be provided to Apply")
	}
	emptyResult := &v1alpha1.RayClusterFleet{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(rayclusterfleetsResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions(), "status"), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1alpha1.RayClusterFleet), err
}
