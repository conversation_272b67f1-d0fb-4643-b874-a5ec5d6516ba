/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"

	v1alpha1 "github.com/vllm-project/aibrix/api/orchestration/v1alpha1"
	orchestrationv1alpha1 "github.com/vllm-project/aibrix/pkg/client/applyconfiguration/orchestration/v1alpha1"
	scheme "github.com/vllm-project/aibrix/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// RayClusterFleetsGetter has a method to return a RayClusterFleetInterface.
// A group's client should implement this interface.
type RayClusterFleetsGetter interface {
	RayClusterFleets(namespace string) RayClusterFleetInterface
}

// RayClusterFleetInterface has methods to work with RayClusterFleet resources.
type RayClusterFleetInterface interface {
	Create(ctx context.Context, rayClusterFleet *v1alpha1.RayClusterFleet, opts v1.CreateOptions) (*v1alpha1.RayClusterFleet, error)
	Update(ctx context.Context, rayClusterFleet *v1alpha1.RayClusterFleet, opts v1.UpdateOptions) (*v1alpha1.RayClusterFleet, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, rayClusterFleet *v1alpha1.RayClusterFleet, opts v1.UpdateOptions) (*v1alpha1.RayClusterFleet, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.RayClusterFleet, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.RayClusterFleetList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.RayClusterFleet, err error)
	Apply(ctx context.Context, rayClusterFleet *orchestrationv1alpha1.RayClusterFleetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterFleet, err error)
	// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
	ApplyStatus(ctx context.Context, rayClusterFleet *orchestrationv1alpha1.RayClusterFleetApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha1.RayClusterFleet, err error)
	RayClusterFleetExpansion
}

// rayClusterFleets implements RayClusterFleetInterface
type rayClusterFleets struct {
	*gentype.ClientWithListAndApply[*v1alpha1.RayClusterFleet, *v1alpha1.RayClusterFleetList, *orchestrationv1alpha1.RayClusterFleetApplyConfiguration]
}

// newRayClusterFleets returns a RayClusterFleets
func newRayClusterFleets(c *OrchestrationV1alpha1Client, namespace string) *rayClusterFleets {
	return &rayClusterFleets{
		gentype.NewClientWithListAndApply[*v1alpha1.RayClusterFleet, *v1alpha1.RayClusterFleetList, *orchestrationv1alpha1.RayClusterFleetApplyConfiguration](
			"rayclusterfleets",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *v1alpha1.RayClusterFleet { return &v1alpha1.RayClusterFleet{} },
			func() *v1alpha1.RayClusterFleetList { return &v1alpha1.RayClusterFleetList{} }),
	}
}
