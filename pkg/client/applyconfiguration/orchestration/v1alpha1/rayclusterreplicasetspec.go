/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// RayClusterReplicaSetSpecApplyConfiguration represents a declarative configuration of the RayClusterReplicaSetSpec type for use
// with apply.
type RayClusterReplicaSetSpecApplyConfiguration struct {
	Replicas        *int32                                    `json:"replicas,omitempty"`
	MinReadySeconds *int32                                    `json:"minReadySeconds,omitempty"`
	Selector        *v1.LabelSelectorApplyConfiguration       `json:"selector,omitempty"`
	Template        *RayClusterTemplateSpecApplyConfiguration `json:"template,omitempty"`
}

// RayClusterReplicaSetSpecApplyConfiguration constructs a declarative configuration of the RayClusterReplicaSetSpec type for use with
// apply.
func RayClusterReplicaSetSpec() *RayClusterReplicaSetSpecApplyConfiguration {
	return &RayClusterReplicaSetSpecApplyConfiguration{}
}

// WithReplicas sets the Replicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Replicas field is set to the value of the last call.
func (b *RayClusterReplicaSetSpecApplyConfiguration) WithReplicas(value int32) *RayClusterReplicaSetSpecApplyConfiguration {
	b.Replicas = &value
	return b
}

// WithMinReadySeconds sets the MinReadySeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MinReadySeconds field is set to the value of the last call.
func (b *RayClusterReplicaSetSpecApplyConfiguration) WithMinReadySeconds(value int32) *RayClusterReplicaSetSpecApplyConfiguration {
	b.MinReadySeconds = &value
	return b
}

// WithSelector sets the Selector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Selector field is set to the value of the last call.
func (b *RayClusterReplicaSetSpecApplyConfiguration) WithSelector(value *v1.LabelSelectorApplyConfiguration) *RayClusterReplicaSetSpecApplyConfiguration {
	b.Selector = value
	return b
}

// WithTemplate sets the Template field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Template field is set to the value of the last call.
func (b *RayClusterReplicaSetSpecApplyConfiguration) WithTemplate(value *RayClusterTemplateSpecApplyConfiguration) *RayClusterReplicaSetSpecApplyConfiguration {
	b.Template = value
	return b
}
