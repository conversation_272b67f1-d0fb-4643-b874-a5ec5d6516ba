/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// RayClusterFleetSpecApplyConfiguration represents a declarative configuration of the RayClusterFleetSpec type for use
// with apply.
type RayClusterFleetSpecApplyConfiguration struct {
	Replicas                *int32                                    `json:"replicas,omitempty"`
	Selector                *v1.LabelSelectorApplyConfiguration       `json:"selector,omitempty"`
	Template                *RayClusterTemplateSpecApplyConfiguration `json:"template,omitempty"`
	Strategy                *appsv1.DeploymentStrategy                `json:"strategy,omitempty"`
	MinReadySeconds         *int32                                    `json:"minReadySeconds,omitempty"`
	RevisionHistoryLimit    *int32                                    `json:"revisionHistoryLimit,omitempty"`
	Paused                  *bool                                     `json:"paused,omitempty"`
	ProgressDeadlineSeconds *int32                                    `json:"progressDeadlineSeconds,omitempty"`
}

// RayClusterFleetSpecApplyConfiguration constructs a declarative configuration of the RayClusterFleetSpec type for use with
// apply.
func RayClusterFleetSpec() *RayClusterFleetSpecApplyConfiguration {
	return &RayClusterFleetSpecApplyConfiguration{}
}

// WithReplicas sets the Replicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Replicas field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithReplicas(value int32) *RayClusterFleetSpecApplyConfiguration {
	b.Replicas = &value
	return b
}

// WithSelector sets the Selector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Selector field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithSelector(value *v1.LabelSelectorApplyConfiguration) *RayClusterFleetSpecApplyConfiguration {
	b.Selector = value
	return b
}

// WithTemplate sets the Template field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Template field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithTemplate(value *RayClusterTemplateSpecApplyConfiguration) *RayClusterFleetSpecApplyConfiguration {
	b.Template = value
	return b
}

// WithStrategy sets the Strategy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Strategy field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithStrategy(value appsv1.DeploymentStrategy) *RayClusterFleetSpecApplyConfiguration {
	b.Strategy = &value
	return b
}

// WithMinReadySeconds sets the MinReadySeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MinReadySeconds field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithMinReadySeconds(value int32) *RayClusterFleetSpecApplyConfiguration {
	b.MinReadySeconds = &value
	return b
}

// WithRevisionHistoryLimit sets the RevisionHistoryLimit field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RevisionHistoryLimit field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithRevisionHistoryLimit(value int32) *RayClusterFleetSpecApplyConfiguration {
	b.RevisionHistoryLimit = &value
	return b
}

// WithPaused sets the Paused field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Paused field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithPaused(value bool) *RayClusterFleetSpecApplyConfiguration {
	b.Paused = &value
	return b
}

// WithProgressDeadlineSeconds sets the ProgressDeadlineSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ProgressDeadlineSeconds field is set to the value of the last call.
func (b *RayClusterFleetSpecApplyConfiguration) WithProgressDeadlineSeconds(value int32) *RayClusterFleetSpecApplyConfiguration {
	b.ProgressDeadlineSeconds = &value
	return b
}
