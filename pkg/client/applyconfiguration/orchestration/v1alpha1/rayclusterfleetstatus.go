/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// RayClusterFleetStatusApplyConfiguration represents a declarative configuration of the RayClusterFleetStatus type for use
// with apply.
type RayClusterFleetStatusApplyConfiguration struct {
	ObservedGeneration    *int64                                       `json:"observedGeneration,omitempty"`
	Replicas              *int32                                       `json:"replicas,omitempty"`
	UpdatedReplicas       *int32                                       `json:"updatedReplicas,omitempty"`
	ReadyReplicas         *int32                                       `json:"readyReplicas,omitempty"`
	AvailableReplicas     *int32                                       `json:"availableReplicas,omitempty"`
	UnavailableReplicas   *int32                                       `json:"unavailableReplicas,omitempty"`
	Conditions            []RayClusterFleetConditionApplyConfiguration `json:"conditions,omitempty"`
	CollisionCount        *int32                                       `json:"collisionCount,omitempty"`
	ScalingTargetSelector *string                                      `json:"scalingTargetSelector,omitempty"`
}

// RayClusterFleetStatusApplyConfiguration constructs a declarative configuration of the RayClusterFleetStatus type for use with
// apply.
func RayClusterFleetStatus() *RayClusterFleetStatusApplyConfiguration {
	return &RayClusterFleetStatusApplyConfiguration{}
}

// WithObservedGeneration sets the ObservedGeneration field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ObservedGeneration field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithObservedGeneration(value int64) *RayClusterFleetStatusApplyConfiguration {
	b.ObservedGeneration = &value
	return b
}

// WithReplicas sets the Replicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Replicas field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithReplicas(value int32) *RayClusterFleetStatusApplyConfiguration {
	b.Replicas = &value
	return b
}

// WithUpdatedReplicas sets the UpdatedReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UpdatedReplicas field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithUpdatedReplicas(value int32) *RayClusterFleetStatusApplyConfiguration {
	b.UpdatedReplicas = &value
	return b
}

// WithReadyReplicas sets the ReadyReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ReadyReplicas field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithReadyReplicas(value int32) *RayClusterFleetStatusApplyConfiguration {
	b.ReadyReplicas = &value
	return b
}

// WithAvailableReplicas sets the AvailableReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AvailableReplicas field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithAvailableReplicas(value int32) *RayClusterFleetStatusApplyConfiguration {
	b.AvailableReplicas = &value
	return b
}

// WithUnavailableReplicas sets the UnavailableReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UnavailableReplicas field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithUnavailableReplicas(value int32) *RayClusterFleetStatusApplyConfiguration {
	b.UnavailableReplicas = &value
	return b
}

// WithConditions adds the given value to the Conditions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Conditions field.
func (b *RayClusterFleetStatusApplyConfiguration) WithConditions(values ...*RayClusterFleetConditionApplyConfiguration) *RayClusterFleetStatusApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithConditions")
		}
		b.Conditions = append(b.Conditions, *values[i])
	}
	return b
}

// WithCollisionCount sets the CollisionCount field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CollisionCount field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithCollisionCount(value int32) *RayClusterFleetStatusApplyConfiguration {
	b.CollisionCount = &value
	return b
}

// WithScalingTargetSelector sets the ScalingTargetSelector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ScalingTargetSelector field is set to the value of the last call.
func (b *RayClusterFleetStatusApplyConfiguration) WithScalingTargetSelector(value string) *RayClusterFleetStatusApplyConfiguration {
	b.ScalingTargetSelector = &value
	return b
}
