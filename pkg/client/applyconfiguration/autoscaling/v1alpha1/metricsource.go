/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "github.com/vllm-project/aibrix/api/autoscaling/v1alpha1"
)

// MetricSourceApplyConfiguration represents a declarative configuration of the MetricSource type for use
// with apply.
type MetricSourceApplyConfiguration struct {
	MetricSourceType *v1alpha1.MetricSourceType `json:"metricSourceType,omitempty"`
	ProtocolType     *v1alpha1.ProtocolType     `json:"protocolType,omitempty"`
	Endpoint         *string                    `json:"endpoint,omitempty"`
	Path             *string                    `json:"path,omitempty"`
	Port             *string                    `json:"port,omitempty"`
	TargetMetric     *string                    `json:"targetMetric,omitempty"`
	TargetValue      *string                    `json:"targetValue,omitempty"`
}

// MetricSourceApplyConfiguration constructs a declarative configuration of the MetricSource type for use with
// apply.
func MetricSource() *MetricSourceApplyConfiguration {
	return &MetricSourceApplyConfiguration{}
}

// WithMetricSourceType sets the MetricSourceType field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MetricSourceType field is set to the value of the last call.
func (b *MetricSourceApplyConfiguration) WithMetricSourceType(value v1alpha1.MetricSourceType) *MetricSourceApplyConfiguration {
	b.MetricSourceType = &value
	return b
}

// WithProtocolType sets the ProtocolType field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ProtocolType field is set to the value of the last call.
func (b *MetricSourceApplyConfiguration) WithProtocolType(value v1alpha1.ProtocolType) *MetricSourceApplyConfiguration {
	b.ProtocolType = &value
	return b
}

// WithEndpoint sets the Endpoint field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Endpoint field is set to the value of the last call.
func (b *MetricSourceApplyConfiguration) WithEndpoint(value string) *MetricSourceApplyConfiguration {
	b.Endpoint = &value
	return b
}

// WithPath sets the Path field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Path field is set to the value of the last call.
func (b *MetricSourceApplyConfiguration) WithPath(value string) *MetricSourceApplyConfiguration {
	b.Path = &value
	return b
}

// WithPort sets the Port field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Port field is set to the value of the last call.
func (b *MetricSourceApplyConfiguration) WithPort(value string) *MetricSourceApplyConfiguration {
	b.Port = &value
	return b
}

// WithTargetMetric sets the TargetMetric field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TargetMetric field is set to the value of the last call.
func (b *MetricSourceApplyConfiguration) WithTargetMetric(value string) *MetricSourceApplyConfiguration {
	b.TargetMetric = &value
	return b
}

// WithTargetValue sets the TargetValue field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TargetValue field is set to the value of the last call.
func (b *MetricSourceApplyConfiguration) WithTargetValue(value string) *MetricSourceApplyConfiguration {
	b.TargetValue = &value
	return b
}
