/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	autoscalingv1alpha1 "github.com/vllm-project/aibrix/api/autoscaling/v1alpha1"
	v1 "k8s.io/api/core/v1"
)

// PodAutoscalerSpecApplyConfiguration represents a declarative configuration of the PodAutoscalerSpec type for use
// with apply.
type PodAutoscalerSpecApplyConfiguration struct {
	ScaleTargetRef  *v1.ObjectReference                      `json:"scaleTargetRef,omitempty"`
	MinReplicas     *int32                                   `json:"minReplicas,omitempty"`
	MaxReplicas     *int32                                   `json:"maxReplicas,omitempty"`
	MetricsSources  []MetricSourceApplyConfiguration         `json:"metricsSources,omitempty"`
	ScalingStrategy *autoscalingv1alpha1.ScalingStrategyType `json:"scalingStrategy,omitempty"`
}

// PodAutoscalerSpecApplyConfiguration constructs a declarative configuration of the PodAutoscalerSpec type for use with
// apply.
func PodAutoscalerSpec() *PodAutoscalerSpecApplyConfiguration {
	return &PodAutoscalerSpecApplyConfiguration{}
}

// WithScaleTargetRef sets the ScaleTargetRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ScaleTargetRef field is set to the value of the last call.
func (b *PodAutoscalerSpecApplyConfiguration) WithScaleTargetRef(value v1.ObjectReference) *PodAutoscalerSpecApplyConfiguration {
	b.ScaleTargetRef = &value
	return b
}

// WithMinReplicas sets the MinReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MinReplicas field is set to the value of the last call.
func (b *PodAutoscalerSpecApplyConfiguration) WithMinReplicas(value int32) *PodAutoscalerSpecApplyConfiguration {
	b.MinReplicas = &value
	return b
}

// WithMaxReplicas sets the MaxReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MaxReplicas field is set to the value of the last call.
func (b *PodAutoscalerSpecApplyConfiguration) WithMaxReplicas(value int32) *PodAutoscalerSpecApplyConfiguration {
	b.MaxReplicas = &value
	return b
}

// WithMetricsSources adds the given value to the MetricsSources field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the MetricsSources field.
func (b *PodAutoscalerSpecApplyConfiguration) WithMetricsSources(values ...*MetricSourceApplyConfiguration) *PodAutoscalerSpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithMetricsSources")
		}
		b.MetricsSources = append(b.MetricsSources, *values[i])
	}
	return b
}

// WithScalingStrategy sets the ScalingStrategy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ScalingStrategy field is set to the value of the last call.
func (b *PodAutoscalerSpecApplyConfiguration) WithScalingStrategy(value autoscalingv1alpha1.ScalingStrategyType) *PodAutoscalerSpecApplyConfiguration {
	b.ScalingStrategy = &value
	return b
}
