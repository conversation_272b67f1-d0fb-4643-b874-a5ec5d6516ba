/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	metav1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// PodAutoscalerStatusApplyConfiguration represents a declarative configuration of the PodAutoscalerStatus type for use
// with apply.
type PodAutoscalerStatusApplyConfiguration struct {
	LastScaleTime *v1.Time                             `json:"lastScaleTime,omitempty"`
	DesiredScale  *int32                               `json:"desiredScale,omitempty"`
	ActualScale   *int32                               `json:"actualScale,omitempty"`
	Conditions    []metav1.ConditionApplyConfiguration `json:"conditions,omitempty"`
}

// PodAutoscalerStatusApplyConfiguration constructs a declarative configuration of the PodAutoscalerStatus type for use with
// apply.
func PodAutoscalerStatus() *PodAutoscalerStatusApplyConfiguration {
	return &PodAutoscalerStatusApplyConfiguration{}
}

// WithLastScaleTime sets the LastScaleTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LastScaleTime field is set to the value of the last call.
func (b *PodAutoscalerStatusApplyConfiguration) WithLastScaleTime(value v1.Time) *PodAutoscalerStatusApplyConfiguration {
	b.LastScaleTime = &value
	return b
}

// WithDesiredScale sets the DesiredScale field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DesiredScale field is set to the value of the last call.
func (b *PodAutoscalerStatusApplyConfiguration) WithDesiredScale(value int32) *PodAutoscalerStatusApplyConfiguration {
	b.DesiredScale = &value
	return b
}

// WithActualScale sets the ActualScale field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ActualScale field is set to the value of the last call.
func (b *PodAutoscalerStatusApplyConfiguration) WithActualScale(value int32) *PodAutoscalerStatusApplyConfiguration {
	b.ActualScale = &value
	return b
}

// WithConditions adds the given value to the Conditions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Conditions field.
func (b *PodAutoscalerStatusApplyConfiguration) WithConditions(values ...*metav1.ConditionApplyConfiguration) *PodAutoscalerStatusApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithConditions")
		}
		b.Conditions = append(b.Conditions, *values[i])
	}
	return b
}
