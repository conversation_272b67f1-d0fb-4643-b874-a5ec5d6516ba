/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// ModelAdapterSpecApplyConfiguration represents a declarative configuration of the ModelAdapterSpec type for use
// with apply.
type ModelAdapterSpecApplyConfiguration struct {
	BaseModel            *string                             `json:"baseModel,omitempty"`
	PodSelector          *v1.LabelSelectorApplyConfiguration `json:"podSelector,omitempty"`
	SchedulerName        *string                             `json:"schedulerName,omitempty"`
	ArtifactURL          *string                             `json:"artifactURL,omitempty"`
	CredentialsSecretRef *corev1.LocalObjectReference        `json:"credentialsSecretRef,omitempty"`
	Replicas             *int32                              `json:"replicas,omitempty"`
	AdditionalConfig     map[string]string                   `json:"additionalConfig,omitempty"`
}

// ModelAdapterSpecApplyConfiguration constructs a declarative configuration of the ModelAdapterSpec type for use with
// apply.
func ModelAdapterSpec() *ModelAdapterSpecApplyConfiguration {
	return &ModelAdapterSpecApplyConfiguration{}
}

// WithBaseModel sets the BaseModel field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the BaseModel field is set to the value of the last call.
func (b *ModelAdapterSpecApplyConfiguration) WithBaseModel(value string) *ModelAdapterSpecApplyConfiguration {
	b.BaseModel = &value
	return b
}

// WithPodSelector sets the PodSelector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PodSelector field is set to the value of the last call.
func (b *ModelAdapterSpecApplyConfiguration) WithPodSelector(value *v1.LabelSelectorApplyConfiguration) *ModelAdapterSpecApplyConfiguration {
	b.PodSelector = value
	return b
}

// WithSchedulerName sets the SchedulerName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SchedulerName field is set to the value of the last call.
func (b *ModelAdapterSpecApplyConfiguration) WithSchedulerName(value string) *ModelAdapterSpecApplyConfiguration {
	b.SchedulerName = &value
	return b
}

// WithArtifactURL sets the ArtifactURL field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ArtifactURL field is set to the value of the last call.
func (b *ModelAdapterSpecApplyConfiguration) WithArtifactURL(value string) *ModelAdapterSpecApplyConfiguration {
	b.ArtifactURL = &value
	return b
}

// WithCredentialsSecretRef sets the CredentialsSecretRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CredentialsSecretRef field is set to the value of the last call.
func (b *ModelAdapterSpecApplyConfiguration) WithCredentialsSecretRef(value corev1.LocalObjectReference) *ModelAdapterSpecApplyConfiguration {
	b.CredentialsSecretRef = &value
	return b
}

// WithReplicas sets the Replicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Replicas field is set to the value of the last call.
func (b *ModelAdapterSpecApplyConfiguration) WithReplicas(value int32) *ModelAdapterSpecApplyConfiguration {
	b.Replicas = &value
	return b
}

// WithAdditionalConfig puts the entries into the AdditionalConfig field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the AdditionalConfig field,
// overwriting an existing map entries in AdditionalConfig field with the same key.
func (b *ModelAdapterSpecApplyConfiguration) WithAdditionalConfig(entries map[string]string) *ModelAdapterSpecApplyConfiguration {
	if b.AdditionalConfig == nil && len(entries) > 0 {
		b.AdditionalConfig = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.AdditionalConfig[k] = v
	}
	return b
}
