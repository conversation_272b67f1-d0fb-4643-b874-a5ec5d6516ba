/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	time "time"

	modelv1alpha1 "github.com/vllm-project/aibrix/api/model/v1alpha1"
	versioned "github.com/vllm-project/aibrix/pkg/client/clientset/versioned"
	internalinterfaces "github.com/vllm-project/aibrix/pkg/client/informers/externalversions/internalinterfaces"
	v1alpha1 "github.com/vllm-project/aibrix/pkg/client/listers/model/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// ModelAdapterInformer provides access to a shared informer and lister for
// ModelAdapters.
type ModelAdapterInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1alpha1.ModelAdapterLister
}

type modelAdapterInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
	namespace        string
}

// NewModelAdapterInformer constructs a new informer for ModelAdapter type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewModelAdapterInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredModelAdapterInformer(client, namespace, resyncPeriod, indexers, nil)
}

// NewFilteredModelAdapterInformer constructs a new informer for ModelAdapter type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredModelAdapterInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.ModelV1alpha1().ModelAdapters(namespace).List(context.TODO(), options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.ModelV1alpha1().ModelAdapters(namespace).Watch(context.TODO(), options)
			},
		},
		&modelv1alpha1.ModelAdapter{},
		resyncPeriod,
		indexers,
	)
}

func (f *modelAdapterInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredModelAdapterInformer(client, f.namespace, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *modelAdapterInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&modelv1alpha1.ModelAdapter{}, f.defaultInformer)
}

func (f *modelAdapterInformer) Lister() v1alpha1.ModelAdapterLister {
	return v1alpha1.NewModelAdapterLister(f.Informer().GetIndexer())
}
