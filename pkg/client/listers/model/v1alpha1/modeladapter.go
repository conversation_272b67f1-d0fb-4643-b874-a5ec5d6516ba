/*
Copyright 2024 The Aibrix Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "github.com/vllm-project/aibrix/api/model/v1alpha1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/listers"
	"k8s.io/client-go/tools/cache"
)

// ModelAdapterLister helps list ModelAdapters.
// All objects returned here must be treated as read-only.
type ModelAdapterLister interface {
	// List lists all ModelAdapters in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.ModelAdapter, err error)
	// ModelAdapters returns an object that can list and get ModelAdapters.
	ModelAdapters(namespace string) ModelAdapterNamespaceLister
	ModelAdapterListerExpansion
}

// modelAdapterLister implements the ModelAdapterLister interface.
type modelAdapterLister struct {
	listers.ResourceIndexer[*v1alpha1.ModelAdapter]
}

// NewModelAdapterLister returns a new ModelAdapterLister.
func NewModelAdapterLister(indexer cache.Indexer) ModelAdapterLister {
	return &modelAdapterLister{listers.New[*v1alpha1.ModelAdapter](indexer, v1alpha1.Resource("modeladapter"))}
}

// ModelAdapters returns an object that can list and get ModelAdapters.
func (s *modelAdapterLister) ModelAdapters(namespace string) ModelAdapterNamespaceLister {
	return modelAdapterNamespaceLister{listers.NewNamespaced[*v1alpha1.ModelAdapter](s.ResourceIndexer, namespace)}
}

// ModelAdapterNamespaceLister helps list and get ModelAdapters.
// All objects returned here must be treated as read-only.
type ModelAdapterNamespaceLister interface {
	// List lists all ModelAdapters in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.ModelAdapter, err error)
	// Get retrieves the ModelAdapter from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha1.ModelAdapter, error)
	ModelAdapterNamespaceListerExpansion
}

// modelAdapterNamespaceLister implements the ModelAdapterNamespaceLister
// interface.
type modelAdapterNamespaceLister struct {
	listers.ResourceIndexer[*v1alpha1.ModelAdapter]
}
