name: Installation Tests

on:
  workflow_dispatch:  # Allows manual trigger
  push:
    branches: [ "main", "release-*" ]
    paths:
      - '.github/workflows/**'
      - 'build/container/**'
      - 'config/**'
      - 'pkg/**'
      - 'test/**'
      - 'cmd/**'
      - 'python/**'
      - 'Makefile'
      - 'go.mod'
      - 'go.sum'
  pull_request:
    branches: [ "main", "release-*" ]
    paths:
      - '.github/workflows/**'
      - 'build/container/**'
      - 'config/**'
      - 'pkg/**'
      - 'test/**'
      - 'cmd/**'
      - 'python/**'
      - 'Makefile'
      - 'go.mod'
      - 'go.sum'

jobs:
  build-images:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        image: [controller-manager, gateway-plugins, runtime, metadata-service]
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build ${{ matrix.image }}
        run: |
          GIT_COMMIT_HASH=${{ github.sha }} make docker-build-${{ matrix.image }}

      - name: Save image
        run: |
          docker save aibrix/${{ matrix.image }}:${{ github.sha }} > ${{ matrix.image }}.tar

      - name: Upload image artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.image }}-image
          path: ${{ matrix.image }}.tar
          retention-days: 1
  
  build-mock-image:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        image: [vllm-mock]
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build vllm-mock
        run: |
          cd development/app
          docker build -t aibrix/vllm-mock:${{ github.sha }} -f Dockerfile .

      - name: Save image
        run: |
          docker save aibrix/vllm-mock:${{ github.sha }} > vllm-mock.tar

      - name: Upload image artifact
        uses: actions/upload-artifact@v4
        with:
          name: vllm-mock-image
          path: vllm-mock.tar
          retention-days: 1

  installation-test:
    needs: [build-images, build-mock-image]
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.22'

      - name: Download all image artifacts
        uses: actions/download-artifact@v4

      - name: Free disk space
        run: |-
          # https://github.com/actions/runner-images/issues/2840#issuecomment-2272410832
          # Remove software and language runtimes we're not using
          sudo rm -rf \
            /opt/google/chrome \
            /opt/microsoft/msedge \
            /opt/microsoft/powershell \
            /opt/pipx \
            /usr/lib/mono \
            /usr/local/julia* \
            /usr/local/lib/android \
            /usr/local/lib/node_modules \
            /usr/local/share/chromium \
            /usr/local/share/powershell \
            /usr/share/dotnet \
            /usr/share/swift
          df -h /

      - name: Install kind
        run: |
          [ $(uname -m) = x86_64 ] && curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.24.0/kind-linux-amd64
          chmod +x ./kind
          mv ./kind /usr/local/bin/kind
          kind version

      - name: Install kustomize
        run: |
          curl -L https://github.com/kubernetes-sigs/kustomize/releases/download/kustomize/v5.4.3/kustomize_v5.4.3_$(go env GOOS)_$(go env GOARCH).tar.gz | tar -xz -C /usr/local/bin/
          kustomize version

      - name: Set up testing environment
        run: |
          # Set up the cluster with kind or other Kubernetes environment needed
          kind create cluster --name installation-test --config=./hack/ci/kind-config.yaml
          kubectl cluster-info --context kind-installation-test

      - name: Load image into Kind
        run: |
          for image in controller-manager gateway-plugins runtime metadata-service vllm-mock; do
              docker load < ${image}-image/${image}.tar

              # Retag the image
              # This is for application integration, since it is not that easy to override all commits in manifest
              # It is better to use nightly to represent the latest image
              docker tag aibrix/${image}:${{ github.sha }} aibrix/${image}:nightly
          done

          kind load docker-image aibrix/controller-manager:${{ github.sha }} aibrix/gateway-plugins:${{ github.sha }} aibrix/metadata-service:${{ github.sha }} aibrix/runtime:${{ github.sha }} aibrix/vllm-mock:${{ github.sha }} --name installation-test
          kind load docker-image aibrix/controller-manager:nightly aibrix/gateway-plugins:nightly aibrix/metadata-service:nightly aibrix/runtime:nightly aibrix/vllm-mock:nightly --name installation-test

      - name: Deploy controller with the built image
        run: |
          kubectl apply -k config/dependency --server-side
          cd config/default
          kustomize edit set image controller=aibrix/controller-manager:${{ github.sha }}
          kustomize edit set image gateway-plugins=aibrix/gateway-plugins:${{ github.sha }}
          kustomize edit set image metadata-service=aibrix/metadata-service:${{ github.sha }}
          cd ${{ github.workspace }}
          kubectl apply -k config/default

      - name: Deploy Workload
        run: |
          cd development/app
          kubectl apply -k config/mock

      - name: Check pod status
        run: |
          sleep 45s
          kubectl get pods --all-namespaces
          kubectl wait pod --all --for=condition=ready --all-namespaces --timeout=300s

          kubectl port-forward svc/llama2-7b 8000:8000 &
          kubectl -n envoy-gateway-system port-forward service/envoy-aibrix-system-aibrix-eg-903790dc  8888:80 &
          kubectl -n aibrix-system port-forward service/aibrix-redis-master 6379:6379 &

      - name: Run e2e tests
        run: |
          kind get kubeconfig --name installation-test > /tmp/admin.conf
          export KUBECONFIG=/tmp/admin.conf
          make test-e2e

      - name: Clean up
        run: kind delete cluster --name installation-test

  delete-artifacts:
    needs: [installation-test]
    runs-on: ubuntu-latest  
    steps:   
      - uses: geekyeggo/delete-artifact@v5
        with:
          name: vllm-mock-image