name: <PERSON><PERSON> and <PERSON> Tests

on:
  workflow_dispatch:  # Allows manual trigger
  push:
    branches: [ "main", "release-*" ]
    paths:
      - 'pkg/**'
      - 'test/**'
      - 'cmd/**'
      - 'api/**'
      - 'go.mod'
      - 'go.sum'
      - '.golangci.yml'
  pull_request:
    branches: [ "main" ]
    paths:
      - 'pkg/**'
      - 'test/**'
      - 'cmd/**'
      - 'api/**'
      - 'go.mod'
      - 'go.sum'
      - '.golangci.yml'

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.22'
      - name: Lint
        run: make lint-all

  verify:
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.22'
      - name: Verify Codegen
        run: bash ${GITHUB_WORKSPACE}/hack/verify-codegen.sh

  test:
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.22'
      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Run Unit Tests
        run: make test
      - name: Run Integration Tests
        run: make test-integration
