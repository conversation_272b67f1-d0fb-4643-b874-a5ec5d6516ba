name: Release Build and Push

on:
  workflow_dispatch:  # Allows manual trigger
  push:
    tags:
      - "v*.*.*"  # This triggers the workflow on any new tag

jobs:
  image-release:
    runs-on: ubuntu-latest
    needs: [cut-github-release]
    permissions:
      packages: write
      contents: read
    steps:
      # Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v3

      # Set up Docker Buildx (optional but allows advanced Docker building)
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # Log in to Docker Hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      # Log in to Github Registry
      - name: Login to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.AIBRIX_ORG_PACKAGE_UPDATE_GITHUB_TOKEN }}

      # Build container images with docker registry namespace
      - name: Build Container Images
        run: |
          IS_MAIN_BRANCH=false GIT_COMMIT_HASH=${{ github.ref_name }} make docker-build-all

      # Push container image to DockerHub
      - name: Push container image to container registry
        run: |
          IS_MAIN_BRANCH=false GIT_COMMIT_HASH=${{ github.ref_name }} make docker-push-all

      # Build container images with Github registry namespace
      - name: Build Container Images with Github Container Registry prefix
        run: |
          IS_MAIN_BRANCH=false GIT_COMMIT_HASH=${{ github.ref_name }} AIBRIX_CONTAINER_REGISTRY_NAMESPACE=ghcr.io/aibrix make docker-build-all

      # Push container image to Github container registry
      - name: Push Container Images to Github Container Registry
        run: |
          IS_MAIN_BRANCH=false GIT_COMMIT_HASH=${{ github.ref_name }} AIBRIX_CONTAINER_REGISTRY_NAMESPACE=ghcr.io/aibrix make docker-push-all

  python-wheel-release:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]
    name: publish
    steps:
      - name: Check out source repository
        uses: actions/checkout@v4
      - name: Set up Python environment ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install poetry
        run: |
          cd python/aibrix
          python -m pip install --upgrade pip
          pip install -U pip poetry
      - name: Build and publish
        env:
          POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_TOKEN }}
        run: |
          pushd python/aibrix
          poetry publish --build
          popd

          pushd python/aibrix_kvcache
          poetry publish --build
          popd
      - name: Add package to release artifact
        run: |
          pushd python/aibrix
          poetry build
          ls -al dist/*
          popd

          pushd python/aibrix_kvcache
          poetry build
          ls -al dist/*
          popd
      - name: Upload release artifacts
        uses: actions/upload-artifact@v4
        with:
          name: aibrix-python-packages-${{ matrix.python-version }}
          path: |
            python/aibrix/dist/*.whl
            python/aibrix_kvcache/dist/*.whl

  artifact-release:
    runs-on: ubuntu-latest
    steps:
      # Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v3

      # Install Kustomize
      - name: Install Kustomize
        run: |
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
          mv kustomize /usr/local/bin/

      # Build Kustomize package
      - name: Build Kustomize
        run: |
          kustomize build config/dependency > aibrix-dependency-${{ github.ref_name }}.yaml
          kustomize build config/overlays/release > aibrix-core-${{ github.ref_name }}.yaml

      # Upload the Kustomize YAML as a release artifact
      - name: Upload Kustomize YAML
        uses: actions/upload-artifact@v4
        with:
          name: aibrix-dependency-${{ github.ref_name }}.yaml
          path: aibrix-dependency-${{ github.ref_name }}.yaml

      - name: Upload Kustomize YAML
        uses: actions/upload-artifact@v4
        with:
          name: aibrix-core-${{ github.ref_name }}.yaml
          path: aibrix-core-${{ github.ref_name }}.yaml

  cut-github-release:
    runs-on: ubuntu-latest
    needs: [python-wheel-release, artifact-release]
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      # Download the Kustomize artifact from the previous job
      - name: Download Kustomize YAML
        uses: actions/download-artifact@v4
        with:
          name: aibrix-dependency-${{ github.ref_name }}.yaml

      - name: Download Kustomize YAML
        uses: actions/download-artifact@v4
        with:
          name: aibrix-core-${{ github.ref_name }}.yaml

      # Determine if this is a prerelease based on the tag name
      # if it contains
      - name: Set prerelease flag
        id: prerelease_check
        run: |
          if [[ "${{ github.ref_name }}" == *"rc"* ]]; then
            echo "This is a prerelease"
            echo "prerelease=true" >> $GITHUB_ENV
          else
            echo "This is not a prerelease"
            echo "prerelease=false" >> $GITHUB_ENV
          fi

      - name: Create Draft Release
        uses: softprops/action-gh-release@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}  # Use the tag that triggered the workflow
          name: ${{ github.ref_name }}  # The name of the release
          body: |
            Automatically generated release for tag ${{ github.ref_name }}.
          draft: true # let's always check the release before officially published.
          prerelease: ${{ env.prerelease }}
          files: |
            aibrix-dependency-${{ github.ref_name }}.yaml
            aibrix-core-${{ github.ref_name }}.yaml
