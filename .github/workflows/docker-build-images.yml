name: Docker Build Images

on:
  workflow_dispatch:  # Allows manual trigger
  pull_request:
    branches: [ "main", "release-*" ]

jobs:
  build:
    # This prevents the job from running as other steps cover its functionality.
    # We use 'if: false' to keep the file for future reference without deleting it.
    if: false
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v4
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    - name: Docker Build Container Images
      run: make docker-build-all
