name: Release ARM64-based Build and Push

on:
  workflow_dispatch:  # Allows manual trigger
  push:
    tags:
      - "v*.*.*"  # This triggers the workflow on any new tag

jobs:
  build-controller:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push controller
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          file: build/container/Dockerfile
          push: true
          tags: aibrix/controller-manager:${{ github.ref_name }}
  
  build-gateway-plugins:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Login to DockerH<PERSON>
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push gateway-plugins
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          file: build/container/Dockerfile.gateway
          push: true
          tags: aibrix/gateway-plugins:${{ github.ref_name }}
  
  build-metadata-service:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push metadata-service
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          file: build/container/Dockerfile.metadata
          push: true
          tags: aibrix/metadata-service:${{ github.ref_name }}

  build-runtime:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push runtime
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          file: build/container/Dockerfile.runtime
          push: true
          tags: aibrix/runtime:${{ github.ref_name }}

  build-kvcache-watcher:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push kvcache-watcher
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          file: build/container/Dockerfile.kvcache
          push: true
          tags: aibrix/kvcache-watcher:${{ github.ref_name }}