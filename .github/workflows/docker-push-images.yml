name: Docker Push Images

on:
  workflow_dispatch:  # Allows manual trigger
  push:
    branches: [ "main", "release-*" ]
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}
      - name: Login to the Github Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.AIBRIX_ORG_PACKAGE_UPDATE_GITHUB_TOKEN }}

      - name: Build Container Images
        run: |
          make docker-build-all

      - name: Push Container Images to DockerHub
        run: |
          make docker-push-all

      - name: Build Container Images with Github Container Registry prefix
        run: |
          GIT_COMMIT_HASH=${{ github.sha }} AIBRIX_CONTAINER_REGISTRY_NAMESPACE=ghcr.io/aibrix make docker-build-all

      - name: Push Container Images to Github Container Registry
        run: |
          GIT_COMMIT_HASH=${{ github.sha }} AIBRIX_CONTAINER_REGISTRY_NAMESPACE=ghcr.io/aibrix make docker-push-all
