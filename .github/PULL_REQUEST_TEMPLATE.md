## Pull Request Description
[Please provide a clear and concise description of your changes here]

## Related Issues
Resolves: #[Insert issue number(s)]

**Important: Before submitting, please complete the description above and review the checklist below.**

---

<details>
<summary><strong>Contribution Guidelines (Expand for Details)</strong></summary>

<p>We appreciate your contribution to aibrix! To ensure a smooth review process and maintain high code quality, please adhere to the following guidelines:</p>

<h3>Pull Request Title Format</h3>
<p>Your PR title should start with one of these prefixes to indicate the nature of the change:</p>
<ul>
    <li><code>[Bug]</code>: Corrections to existing functionality</li>
    <li><code>[CI]</code>: Changes to build process or CI pipeline</li>
    <li><code>[Docs]</code>: Updates or additions to documentation</li>
    <li><code>[API]</code>: Modifications to aibrix's API or interface</li>
    <li><code>[CLI]</code>: Changes or additions to the Command Line Interface</li>
    <li><code>[Misc]</code>: For changes not covered above (use sparingly)</li>
</ul>
<p><em>Note: For changes spanning multiple categories, use multiple prefixes in order of importance.</em></p>

<h3>Submission Checklist</h3>
<ul>
    <li>[ ] PR title includes appropriate prefix(es)</li>
    <li>[ ] Changes are clearly explained in the PR description</li>
    <li>[ ] New and existing tests pass successfully</li>
    <li>[ ] Code adheres to project style and best practices</li>
    <li>[ ] Documentation updated to reflect changes (if applicable)</li>
    <li>[ ] Thorough testing completed, no regressions introduced</li>
</ul>

<p>By submitting this PR, you confirm that you've read these guidelines and your changes align with the project's contribution standards.</p>

</details>