name: 🐛 AIBrix Bug Report
description: Report a bug to help us improve AIBrix's GenAI inference infrastructure
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: >
        #### Before submitting a bug, please make sure the issue hasn't been already addressed by searching through [the
        existing and past issues](https://github.com/yourusername/aibrix/issues).
        It's likely that your issue might be resolved by checking our [documentation](https://aibrix.docs.yourdomain.com) or troubleshooting guide.
  - type: textarea
    attributes:
      label: 🐛 Describe the bug
      description: |
        Please provide a clear and concise description of what the bug is, provide any relevant error logs or stack traces.
      placeholder: |
        A clear and concise description of what the bug is, and how it affects your use of AIBrix for GenAI inference.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: |
        Please provide detailed steps to reproduce the bug.
      placeholder: |
        1. Deploy AIBrix with configuration '...'
        2. Set up LLM '...'
        3. Run inference with input '...'
        4. See error
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected behavior
      description: |
        A clear and concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment
      description: |
        Please provide information about your environment.
      placeholder: |
        - AIBrix version:
        - Deployment environment (e.g., Kubernetes, Docker, bare metal):
        - Cloud provider (if applicable):
        - LLM(s) being used:
        - Client library version (if applicable):
    validations:
      required: true
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Your feedback helps us improve AIBrix for everyone.