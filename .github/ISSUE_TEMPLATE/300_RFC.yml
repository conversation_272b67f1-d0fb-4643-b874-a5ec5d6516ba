name: 💬 Request for Comments (RFC)
description: Propose and discuss major architectural changes or design choices for AIBrix
title: "[RFC]: "
labels: ["RFC"]
body:
- type: markdown
  attributes:
    value: >
      #### Before submitting an RFC, please review previous RFCs for context and to avoid duplication.
- type: textarea
  attributes:
    label: Summary
    description: >
      Provide a brief overview of the proposed change or design decision.
  validations:
    required: true
- type: textarea
  attributes:
    label: Motivation
    description: >
      Explain the rationale behind this RFC. What problem does it solve? 
      How does it improve AIBrix's GenAI inference infrastructure?
  validations:
    required: true
- type: textarea
  attributes:
    label: Proposed Change
    description: >
      Describe the proposed change in detail. Include technical specifics, 
      potential implementation approaches, and how it integrates with existing AIBrix components.
  validations:
    required: true
- type: textarea
  attributes:
    label: Alternatives Considered
    description: >
      Discuss any alternative approaches you've considered and why they were not chosen.
  validations:
    required: false
- type: markdown
  attributes:
    value: >
      Thank you for contributing to AIBrix's development! Your input helps shape the future of our GenAI inference infrastructure. 🎉