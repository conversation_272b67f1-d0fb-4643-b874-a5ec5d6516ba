[tool.poetry]
name = "aibrix"
version = "0.3.0"
description = "AIBrix, the foundational building blocks for constructing your own GenAI inference infrastructure."
authors = [
    "AIBrix Authors <<EMAIL>>"
]
readme = "README.md"
license = "Apache-2.0"
repository = "https://github.com/vllm-project/aibrix/tree/main/python/aibrix"
classifiers = [
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "Intended Audience :: Science/Research",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
packages = [
    { include = "aibrix" },
]
exclude = ["test"]

[tool.poetry.scripts]
aibrix_runtime = 'aibrix.app:main'
aibrix_download = 'aibrix.downloader.__main__:main'
aibrix_benchmark = "aibrix.gpu_optimizer.optimizer.profiling.benchmark:main"
aibrix_gen_profile = 'aibrix.gpu_optimizer.optimizer.profiling.gen_profile:main'

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
huggingface-hub = "^0.24.6"
tos = "2.8.0"
boto3 = "^1.35.5"
fastapi = "^0.112.2"
gunicorn = "^23.0.0"
uvicorn = "^0.30.6"
prometheus-client = "^0.20.0"
types-requests = "^2.31.0"
httpx = "^0.27.2"
hf-transfer = "^0.1.8"
types-redis = "^4.6.0.20241004"
redis = "^5.2.0"
kubernetes = "^31.0.0"
numpy = "1.26.4"
pandas = "^2.2.3"
pulp = "2.8.0"
incdbscan = "^0.1.0"
aiohttp = "^3.11.7"
dash = "^2.18.2"
matplotlib = "^3.9.2"
filelock = "^3.16.1"
tiktoken = "^0.7.0"
transformers = ">=4.38.0"

[tool.poetry.group.dev.dependencies]
mypy = "1.11.1"
ruff = "0.6.1"
pytest = "^8.3.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
ignore_missing_imports = true

[tool.ruff.format]
quote-style = "double"
docstring-code-format = true

[tool.ruff.lint]
select = ["E4", "E7", "E9", "F", "I"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
