# Copyright 2024 The Aibrix Team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# 	http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import zstandard

from . import BaseMarshaller, Marshaller


class ZstdCompressor(BaseMarshaller):
    def __init__(self, marshaller: Marshaller | None = None) -> None:
        super().__init__(marshaller)
        self.compressor = zstandard.ZstdCompressor()
        self.decompressor = zstandard.ZstdDecompressor()

    def _marshal(self, data: bytes) -> bytes:
        return self.compressor.compress(data)

    def _unmarshal(self, data: bytes) -> bytes:
        return self.decompressor.decompress(data)
