[project]
name = "aibrix-kvcache"
version = "0.3.0"
description = "AIBrix KV Cache offloading framework for cross-engine KV reuse"
authors = [{name = "AIBrix Team"}]
license = "Apache-2.0"
license-files = ["LICENSE"]
readme = "README.md"
repository = "https://github.com/vllm-project/aibrix/tree/main/python/aibrix_kvcache"
classifiers = [
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Intended Audience :: Developers",
    "Intended Audience :: Information Technology",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
packages = [
    { include = "aibrix_kvcache" },
]
exclude = ["tests"]

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
asyncio = ">=3.4.3,<4.0.0"
nvtx = ">=0.2.11,<0.3.0"
torch = { version = ">=2.6.0", optional = true }
rocksdict = ">=0.3.26,<0.4.0"
msgspec = ">=0.19.0,<0.20.0"
zstandard = ">=0.23.0,<0.24.0"
sortedcontainers = ">=2.4.0,<3.0.0"
uvloop = ">=0.21.0,<0.22.0"
pytest = ">=8.3.5,<9.0.0"
pytest-asyncio = ">=0.26.0,<0.27.0"
pytest-mock = ">=3.14.0,<4.0.0"
pytest-timeout = ">=2.3.1,<3.0.0"
pytest-rerunfailures = ">=15.0,<16.0"
pytest-benchmark = ">=5.1.0,<6.0.0"
pytest-forked = ">=1.6.0,<2.0.0"
tqdm = ">=4.67.1,<5.0.0"
cityhash = "^0.4.8"
redis = "^6.0.0"
fakeredis = "^2.28.1"
infinistore = { version = "^0.2.35", optional = true }
more-itertools = "^10.7.0"
numpy = "*"

[tool.poetry.extras]
torch = ["torch"]
infinistore = ["infinistore"]

[tool.poetry.group.dev.dependencies]
mypy = "1.11.1"
ruff = "0.6.1"
codespell = "2.4.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
ignore_missing_imports = true
check_untyped_defs = true
follow_imports = "silent"

exclude = [
    'aibrix_kvcache/common/absl_logging.py',
    'tests/.*\.py$'
]

[tool.ruff]
# Allow lines to be as long as 80.
line-length = 80

[tool.ruff.format]
quote-style = "double"
docstring-code-format = true

[tool.ruff.lint]
select = ["E4", "E7", "E9", "F", "I"]
extend-select = ["E501"]
ignore = [
    # star imports
    "F405", "F403",
    # lambda expression assignment
    "E731",
    # Loop control variable not used within loop body
    "B007",
    # f-string format
    "UP032",
    # Can remove once 3.10+ is the minimum Python version
    "UP007",
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.codespell]
ignore-words-list = "dout, te, indicies, subtile, ElementE"
