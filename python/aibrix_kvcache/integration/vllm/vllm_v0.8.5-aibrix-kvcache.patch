diff --git a/csrc/cache.h b/csrc/cache.h
index 0970b704b..f3fc899ec 100644
--- a/csrc/cache.h
+++ b/csrc/cache.h
@@ -31,6 +31,20 @@ void reshape_and_cache_flash(torch::Tensor& key, torch::Tensor& value,
                              const std::string& kv_cache_dtype,
                              torch::Tensor& k_scale, torch::Tensor& v_scale);
 
+void reshape_and_cache_multi_layer(
+    const std::vector<torch::Tensor>& offload_kv_cache_blocks,
+    const std::vector<torch::Tensor>& kv_caches, torch::Tensor& slot_mapping,
+    const int64_t block_size, const std::string& kv_cache_dtype,
+    const std::vector<torch::Tensor>& k_scales,
+    const std::vector<torch::Tensor>& v_scales, const std::string& layout_str);
+
+void reshape_and_offload_multi_layer(
+    const std::vector<torch::Tensor>& offload_kv_cache_blocks,
+    const std::vector<torch::Tensor>& kv_caches, torch::Tensor& slot_mapping,
+    const int64_t block_size, const std::string& kv_cache_dtype,
+    const std::vector<torch::Tensor>& k_scales,
+    const std::vector<torch::Tensor>& v_scales, const std::string& layout_str);
+
 void concat_and_cache_mla(torch::Tensor& kv_c, torch::Tensor& k_pe,
                           torch::Tensor& kv_cache, torch::Tensor& slot_mapping,
                           const std::string& kv_cache_dtype,
diff --git a/csrc/cache_kernels.cu b/csrc/cache_kernels.cu
index 88559c8fe..5052d2da0 100644
--- a/csrc/cache_kernels.cu
+++ b/csrc/cache_kernels.cu
@@ -349,6 +349,335 @@ __global__ void concat_and_cache_mla_kernel(
   copy(k_pe, kv_cache, k_pe_stride, block_stride, pe_dim, kv_lora_rank);
 }
 
+template <typename TPtr, typename TTensor>
+TPtr* get_device_ptr(TTensor& tensor) {
+  torch::Device device = tensor.device();
+  bool is_pinned = tensor.is_pinned();
+
+  if (device.is_cuda()) {
+    return reinterpret_cast<TPtr*>(tensor.data_ptr());
+  } else if (is_pinned) {
+    void* ptr;
+    cudaHostGetDevicePointer(&ptr, static_cast<void*>(tensor.data_ptr()), 0);
+    return static_cast<TPtr*>(ptr);
+  }
+
+  TORCH_CHECK(false, "Tensor must be on GPU or be pinned");
+  return nullptr;
+}
+
+template <typename TTensor>
+torch::Tensor get_device_ptrs(const std::vector<TTensor>& tensors) {
+  // Create a vector to store the GPU memory pointers
+  std::vector<void*> data_ptrs;
+  data_ptrs.reserve(tensors.size());
+
+  // Extract data pointers
+  for (const auto& tensor : tensors) {
+    data_ptrs.push_back(get_device_ptr<void*>(tensor));
+  }
+
+  torch::Tensor gpu_data_ptrs =
+      torch::from_blob(data_ptrs.data(),
+                       {static_cast<int64_t>(data_ptrs.size())}, torch::kInt64)
+          .to(torch::kCUDA);
+
+  return gpu_data_ptrs;
+}
+
+__device__ __forceinline__ int64_t
+get_kv_cache_offset(const int64_t kv_type, const int64_t num_blocks,
+                    const int64_t block_size, const int64_t embed_dim,
+                    const int64_t slot_idx, const int64_t scalar_offset) {
+  const int64_t block_idx = slot_idx / block_size;
+  const int64_t block_offset = slot_idx % block_size;
+  return kv_type * num_blocks * block_size * embed_dim +
+         block_idx * block_size * embed_dim + block_offset * embed_dim +
+         scalar_offset;
+}
+
+__device__ __forceinline__ int64_t get_offload_kv_cache_offset_lcnd(
+    const int64_t kv_type, const int64_t layer_idx, const int64_t block_size,
+    const int64_t num_layers, const int64_t embed_dim, const int64_t token_idx,
+    const int64_t scalar_offset) {
+  const int64_t block_offset = token_idx % block_size;
+  return layer_idx * 2 * block_size * embed_dim +
+         kv_type * block_size * embed_dim + block_offset * embed_dim +
+         scalar_offset;
+}
+
+__device__ __forceinline__ int64_t get_offload_kv_cache_offset_ncld(
+    const int64_t kv_type, const int64_t layer_idx, const int64_t block_size,
+    const int64_t num_layers, const int64_t embed_dim, const int64_t token_idx,
+    const int64_t scalar_offset) {
+  const int64_t block_offset = token_idx % block_size;
+  return block_offset * 2 * num_layers * embed_dim +
+         kv_type * num_layers * embed_dim + layer_idx * embed_dim +
+         scalar_offset;
+}
+
+enum class KVCacheOffloadLayout {
+  kLCND = 1,
+  kNCLD,
+};
+
+/*
+ * Template arguments:
+ * - TOnload: True if offload_kv_cache to kv_cache.
+ * - TLayout: The layout of offload_kv_cache.
+ * Args:
+ * - offload_kv_cache: Supports LCND and NCLD layouts.
+ *                     LCND: [num_blocks, num_layers, 2, block_size, dim]
+ *                     NCLD: [num_blocks, block_size, 2, num_layers, dim]
+ * - kv_cache: Supports both [num_layers, 2, num_blocks, block_size, num_heads,
+ * head_size] and [num_layers, 2, num_blocks, block_size * num_heads *
+ * head_size]
+ * - slot_mapping: [num_tokens]
+ */
+template <typename scalar_t, typename cache_t, Fp8KVCacheDataType kv_dt,
+          bool TOnload, KVCacheOffloadLayout TLayout>
+__global__ void reshape_and_cache_multi_layer_kernel(
+    scalar_t** __restrict__ offload_kv_cache,
+    const int64_t offload_kv_cache_block_size, cache_t** __restrict__ kv_cache,
+    const int64_t kv_cache_block_size, const int64_t kv_cache_num_blocks,
+    const int64_t* __restrict__ slot_mapping, const int64_t num_layers,
+    const int64_t embed_dim,
+    const float** k_scales,  // Scaling factor for keys
+    const float** v_scales   // Scaling factor for values
+) {
+  const int64_t token_idx = blockIdx.x;
+  const int64_t layer_idx = blockIdx.y;
+  const int64_t kv_type = blockIdx.z;
+  const int64_t tid = threadIdx.x;
+  const int64_t num_threads = blockDim.x;
+
+  const int64_t slot_idx = slot_mapping[token_idx];
+  if (slot_idx < 0) return;
+
+  const int64_t offload_kv_cache_block_idx =
+      token_idx / offload_kv_cache_block_size;
+
+  scalar_t* offload_kv_cache_block =
+      offload_kv_cache[offload_kv_cache_block_idx];
+  cache_t* kv_cache_layer = kv_cache[layer_idx];
+  const float* k_scale = k_scales[layer_idx];
+  const float* v_scale = v_scales[layer_idx];
+
+  // Copy data between kv_cache and offload_kv_cache
+  for (int i = tid; i < embed_dim; i += num_threads) {
+    int64_t offload_kv_cache_offset = 0;
+    if constexpr (TLayout == KVCacheOffloadLayout::kLCND) {
+      offload_kv_cache_offset = get_offload_kv_cache_offset_lcnd(
+          kv_type, layer_idx, offload_kv_cache_block_size, num_layers,
+          embed_dim, token_idx, i);
+    } else {
+      offload_kv_cache_offset = get_offload_kv_cache_offset_ncld(
+          kv_type, layer_idx, offload_kv_cache_block_size, num_layers,
+          embed_dim, token_idx, i);
+    }
+
+    int64_t kv_cache_offset =
+        get_kv_cache_offset(kv_type, kv_cache_num_blocks, kv_cache_block_size,
+                            embed_dim, slot_idx, i);
+
+    if (TOnload) {  // true: offload_kv_cache to kv_cache
+      kv_cache_layer[kv_cache_offset] =
+          fp8::scaled_convert<cache_t, scalar_t, kv_dt>(
+              offload_kv_cache_block[offload_kv_cache_offset],
+              (kv_type == 0) ? *k_scale : *v_scale);
+    } else {  // false: kv_cache to offload_kv_cache
+      offload_kv_cache_block[offload_kv_cache_offset] =
+          fp8::scaled_convert<scalar_t, cache_t, kv_dt>(
+              kv_cache_layer[kv_cache_offset],
+              (kv_type == 0) ? *k_scale : *v_scale);
+    }
+  }
+}
+
+/*
+ * Template arguments:
+ * - TOnload: True if offload_kv_cache to kv_cache.
+ * - TLayout: The layout of offload_kv_cache.
+ * Args:
+ * - offload_kv_cache: Supports LCND and NCLD layouts.
+ *                     LCND: [num_blocks, num_layers, 2, block_size, dim]
+ *                     NCLD: [num_blocks, block_size, 2, num_layers, dim]
+ * - kv_cache: Supports both [num_layers, 2, num_blocks, block_size, num_heads,
+ * head_size] and [num_layers, 2, num_blocks, block_size * num_heads *
+ * head_size]
+ * - slot_mapping: [num_tokens]
+ */
+template <typename vec_t, bool TOnload, KVCacheOffloadLayout TLayout>
+__global__ void reshape_and_cache_multi_layer_vec_kernel(
+    vec_t** __restrict__ offload_kv_cache,
+    const int64_t offload_kv_cache_block_size, vec_t** __restrict__ kv_cache,
+    const int64_t kv_cache_block_size, const int64_t kv_cache_num_blocks,
+    const int64_t* __restrict__ slot_mapping, const int64_t num_layers,
+    const int64_t num_vecs) {
+  const int64_t token_idx = blockIdx.x;
+  const int64_t layer_idx = blockIdx.y;
+  const int64_t kv_type = blockIdx.z;
+  const int64_t tid = threadIdx.x;
+  const int64_t num_threads = blockDim.x;
+
+  const int64_t slot_idx = slot_mapping[token_idx];
+  if (slot_idx < 0) return;
+
+  const int64_t offload_kv_cache_block_idx =
+      token_idx / offload_kv_cache_block_size;
+
+  vec_t* offload_kv_cache_block = offload_kv_cache[offload_kv_cache_block_idx];
+  vec_t* kv_cache_layer = kv_cache[layer_idx];
+
+  // Copy data between kv_cache and offload_kv_cache
+  for (int i = tid; i < num_vecs; i += num_threads) {
+    int64_t offload_kv_cache_offset = 0;
+    if constexpr (TLayout == KVCacheOffloadLayout::kLCND) {
+      offload_kv_cache_offset = get_offload_kv_cache_offset_lcnd(
+          kv_type, layer_idx, offload_kv_cache_block_size, num_layers, num_vecs,
+          token_idx, i);
+    } else {
+      offload_kv_cache_offset = get_offload_kv_cache_offset_ncld(
+          kv_type, layer_idx, offload_kv_cache_block_size, num_layers, num_vecs,
+          token_idx, i);
+    }
+
+    int64_t kv_cache_offset =
+        get_kv_cache_offset(kv_type, kv_cache_num_blocks, kv_cache_block_size,
+                            num_vecs, slot_idx, i);
+
+    if (TOnload) {  // true: offload_kv_cache to kv_cache
+      kv_cache_layer[kv_cache_offset] =
+          offload_kv_cache_block[offload_kv_cache_offset];
+    } else {  // false: kv_cache to offload_kv_cache
+      offload_kv_cache_block[offload_kv_cache_offset] =
+          kv_cache_layer[kv_cache_offset];
+    }
+  }
+}
+
+// KV_T is the data type of offload kv cache.
+// CACHE_T is the stored data type of kv cache.
+// KV_DTYPE is the real data type of kv cache.
+// onload_const: true if offload_kv_cache to kv_cache
+// layout_const: LCND, NCLD
+#define CALL_RESHAPE_AND_CACHE_MULTI_LAYER(KV_T, CACHE_T, KV_DTYPE)            \
+  reshape_and_cache_multi_layer_kernel<KV_T, CACHE_T, KV_DTYPE, onload_const,  \
+                                       layout_const>                           \
+      <<<grid, block, 0, stream>>>(                                            \
+          reinterpret_cast<KV_T**>(offload_kv_cache_ptrs.data_ptr()),          \
+          offload_kv_cache_block_size,                                         \
+          reinterpret_cast<CACHE_T**>(kv_cache_ptrs.data_ptr()), block_size,   \
+          kv_cache_num_blocks, slot_mapping.data_ptr<int64_t>(), num_layers,   \
+          embed_dim, reinterpret_cast<const float**>(k_scale_ptrs.data_ptr()), \
+          reinterpret_cast<const float**>(v_scale_ptrs.data_ptr()));
+
+#define CALL_RESHAPE_AND_CACHE_MULTI_LAYER_VEC                                \
+  reshape_and_cache_multi_layer_vec_kernel<vec_t, onload_const, layout_const> \
+      <<<grid, block, 0, stream>>>(                                           \
+          reinterpret_cast<vec_t**>(offload_kv_cache_ptrs.data_ptr()),        \
+          offload_kv_cache_block_size,                                        \
+          reinterpret_cast<vec_t**>(kv_cache_ptrs.data_ptr()), block_size,    \
+          kv_cache_num_blocks, slot_mapping.data_ptr<int64_t>(), num_layers,  \
+          num_vecs);
+
+#define DISPATCH_RESHAPE_AND_CACHE_MULTI_LAYER_BY_KV_CACHE_DTYPE   \
+  DISPATCH_BY_KV_CACHE_DTYPE(kv_caches[0].dtype(), kv_cache_dtype, \
+                             CALL_RESHAPE_AND_CACHE_MULTI_LAYER);
+
+#define DISPATCH_RESHAPE_AND_CACHE_MULTI_LAYER_BY_ONLOAD_AND_LAYOUT( \
+    ONLOAD_T, LAYOUT_T, FN)                                          \
+  if (LAYOUT_T == vllm::KVCacheOffloadLayout::kLCND) {               \
+    const auto layout_const = vllm::KVCacheOffloadLayout::kLCND;     \
+    if (ONLOAD_T) {                                                  \
+      const auto onload_const = true;                                \
+      FN;                                                            \
+    } else {                                                         \
+      const auto onload_const = false;                               \
+      FN;                                                            \
+    }                                                                \
+  } else {                                                           \
+    const auto layout_const = vllm::KVCacheOffloadLayout::kNCLD;     \
+    if (ONLOAD_T) {                                                  \
+      const auto onload_const = true;                                \
+      FN;                                                            \
+    } else {                                                         \
+      const auto onload_const = false;                               \
+      FN;                                                            \
+    }                                                                \
+  }
+
+void reshape_and_cache_multi_layer_impl(
+    const std::vector<torch::Tensor>& offload_kv_cache_blocks,  // [num_blocks]
+    const std::vector<torch::Tensor>& kv_caches,                // [num_layers]
+    torch::Tensor& slot_mapping,                                // [num_tokens]
+    const int64_t block_size, const std::string& kv_cache_dtype,
+    const std::vector<torch::Tensor>& k_scales,
+    const std::vector<torch::Tensor>& v_scales, bool onload,
+    const std::string& layout_str) {
+  const auto layout = (layout_str == "LCND")
+                          ? vllm::KVCacheOffloadLayout::kLCND
+                          : vllm::KVCacheOffloadLayout::kNCLD;
+  const int64_t num_tokens = slot_mapping.size(0);
+  torch::IntArrayRef kv_cache_shape = kv_caches[0].sizes();
+  int64_t embed_dim;
+  if (kv_cache_shape.size() == 3) {
+    // [2, num_blocks, block_size * num_heads * head_size]
+    const int64_t block_dim = kv_caches[0].stride(1);
+    embed_dim = block_dim / block_size;
+  } else {
+    // [2, num_blocks, block_size, num_heads, head_size]
+    embed_dim = kv_caches[0].stride(2);
+  }
+
+  torch::IntArrayRef offload_kv_cache_block_shape =
+      offload_kv_cache_blocks[0].sizes();
+  const int64_t offload_kv_cache_block_size =
+      (layout == vllm::KVCacheOffloadLayout::kLCND)
+          ? offload_kv_cache_block_shape[2]
+          : offload_kv_cache_block_shape[0];
+  const int64_t offload_kv_cache_num_layers =
+      (layout == vllm::KVCacheOffloadLayout::kLCND)
+          ? offload_kv_cache_block_shape[0]
+          : offload_kv_cache_block_shape[2];
+
+  TORCH_CHECK(num_tokens ==
+              offload_kv_cache_blocks.size() * offload_kv_cache_block_size);
+
+  const int64_t num_layers = kv_caches.size();
+  TORCH_CHECK(num_layers == offload_kv_cache_num_layers);
+
+  // Assume all layers have the same shape
+  for (int64_t i = 0; i < num_layers; i++) {
+    TORCH_CHECK(kv_cache_shape == kv_caches[i].sizes());
+  }
+
+  const int64_t kv_cache_num_blocks = kv_cache_shape[1];
+
+  torch::Tensor offload_kv_cache_ptrs =
+      vllm::get_device_ptrs(offload_kv_cache_blocks);
+  torch::Tensor kv_cache_ptrs = vllm::get_device_ptrs(kv_caches);
+  torch::Tensor k_scale_ptrs = vllm::get_device_ptrs(k_scales);
+  torch::Tensor v_scale_ptrs = vllm::get_device_ptrs(v_scales);
+
+  const at::cuda::OptionalCUDAGuard device_guard(device_of(kv_caches[0]));
+  const cudaStream_t stream = at::cuda::getCurrentCUDAStream();
+
+  dim3 grid(num_tokens, num_layers, 2);
+  if (kv_cache_dtype == "auto") {
+    auto element_size = kv_caches[0].element_size();
+    using vec_t = __int128_t;
+    const int64_t num_vecs = embed_dim / sizeof(vec_t) * element_size;
+    dim3 block(std::min(num_vecs, static_cast<int64_t>(128)));
+    DISPATCH_RESHAPE_AND_CACHE_MULTI_LAYER_BY_ONLOAD_AND_LAYOUT(
+        onload, layout, CALL_RESHAPE_AND_CACHE_MULTI_LAYER_VEC);
+  } else {
+    dim3 block(std::min(embed_dim, static_cast<int64_t>(512)));
+    DISPATCH_RESHAPE_AND_CACHE_MULTI_LAYER_BY_ONLOAD_AND_LAYOUT(
+        onload, layout,
+        DISPATCH_RESHAPE_AND_CACHE_MULTI_LAYER_BY_KV_CACHE_DTYPE);
+  }
+}
 }  // namespace vllm
 
 // KV_T is the data type of key and value tensors.
@@ -449,6 +778,30 @@ void reshape_and_cache_flash(
                              CALL_RESHAPE_AND_CACHE_FLASH);
 }
 
+void reshape_and_cache_multi_layer(
+    const std::vector<torch::Tensor>& offload_kv_cache_blocks,  // [num_blocks]
+    const std::vector<torch::Tensor>& kv_caches,                // [num_layers]
+    torch::Tensor& slot_mapping,                                // [num_tokens]
+    const int64_t block_size, const std::string& kv_cache_dtype,
+    const std::vector<torch::Tensor>& k_scales,
+    const std::vector<torch::Tensor>& v_scales, const std::string& layout_str) {
+  vllm::reshape_and_cache_multi_layer_impl(
+      offload_kv_cache_blocks, kv_caches, slot_mapping, block_size,
+      kv_cache_dtype, k_scales, v_scales, true, layout_str);
+}
+
+void reshape_and_offload_multi_layer(
+    const std::vector<torch::Tensor>& offload_kv_cache_blocks,  // [num_blocks]
+    const std::vector<torch::Tensor>& kv_caches,                // [num_layers]
+    torch::Tensor& slot_mapping,                                // [num_tokens]
+    const int64_t block_size, const std::string& kv_cache_dtype,
+    const std::vector<torch::Tensor>& k_scales,
+    const std::vector<torch::Tensor>& v_scales, const std::string& layout_str) {
+  vllm::reshape_and_cache_multi_layer_impl(
+      offload_kv_cache_blocks, kv_caches, slot_mapping, block_size,
+      kv_cache_dtype, k_scales, v_scales, false, layout_str);
+}
+
 // KV_T is the data type of key and value tensors.
 // CACHE_T is the stored data type of kv-cache.
 // KV_DTYPE is the real data type of kv-cache.
@@ -677,8 +1030,7 @@ void gather_cache(
     torch::Tensor const& dst,          // [TOT_TOKENS, ENTRIES...]
     torch::Tensor const& block_table,  // [BATCH, BLOCK_INDICES]
     torch::Tensor const& cu_seq_lens,  // [BATCH+1]
-    int64_t batch_size,
-    std::optional<torch::Tensor> seq_starts = std::nullopt) {
+    int64_t batch_size, std::optional<torch::Tensor> seq_starts) {
   at::cuda::OptionalCUDAGuard device_guard(src_cache.device());
   const cudaStream_t stream = at::cuda::getCurrentCUDAStream();
 
diff --git a/csrc/torch_bindings.cpp b/csrc/torch_bindings.cpp
index c9a120976..d28416ddf 100644
--- a/csrc/torch_bindings.cpp
+++ b/csrc/torch_bindings.cpp
@@ -602,6 +602,28 @@ TORCH_LIBRARY_EXPAND(CONCAT(TORCH_EXTENSION_NAME, _cache_ops), cache_ops) {
   cache_ops.impl("reshape_and_cache_flash", torch::kCUDA,
                  &reshape_and_cache_flash);
 
+  cache_ops.def(
+      "reshape_and_cache_multi_layer(Tensor[] offload_kv_cache_blocks,"
+      "                              Tensor(b!)[] key_caches,"
+      "                              Tensor slot_mapping,"
+      "                              SymInt block_size,"
+      "                              str kv_cache_dtype,"
+      "                              Tensor[] k_scales, Tensor[] v_scales,"
+      "                              str layout) -> ()");
+  cache_ops.impl("reshape_and_cache_multi_layer", torch::kCUDA,
+                 &reshape_and_cache_multi_layer);
+
+  cache_ops.def(
+      "reshape_and_offload_multi_layer(Tensor(a!)[] offload_kv_cache_blocks,"
+      "                                Tensor[] key_caches,"
+      "                                Tensor slot_mapping,"
+      "                                SymInt block_size,"
+      "                                str kv_cache_dtype,"
+      "                                Tensor[] k_scales, Tensor[] v_scales,"
+      "                                str layout) -> ()");
+  cache_ops.impl("reshape_and_offload_multi_layer", torch::kCUDA,
+                 &reshape_and_offload_multi_layer);
+
   // Concat kv_c and k_pe and cache them.
   cache_ops.def(
       "concat_and_cache_mla(Tensor kv_c, Tensor k_pe,"
diff --git a/examples/online_serving/prometheus_grafana/grafana_kv_cache_offload.json b/examples/online_serving/prometheus_grafana/grafana_kv_cache_offload.json
new file mode 100644
index 000000000..311169501
--- /dev/null
+++ b/examples/online_serving/prometheus_grafana/grafana_kv_cache_offload.json
@@ -0,0 +1,4919 @@
+{
+    "annotations": {
+      "list": [
+        {
+          "builtIn": 1,
+          "datasource": {
+            "type": "grafana",
+            "uid": "-- Grafana --"
+          },
+          "enable": true,
+          "hide": true,
+          "iconColor": "rgba(0, 211, 255, 1)",
+          "name": "Annotations & Alerts",
+          "target": {
+            "limit": 100,
+            "matchAny": false,
+            "tags": [],
+            "type": "dashboard"
+          },
+          "type": "dashboard"
+        }
+      ]
+    },
+    "description": "Monitoring vLLM Inference Server",
+    "editable": true,
+    "fiscalYearStartMonth": 0,
+    "graphTooltip": 0,
+    "id": 4,
+    "links": [],
+    "liveNow": false,
+    "panels": [
+      {
+        "collapsed": false,
+        "gridPos": {
+          "h": 1,
+          "w": 24,
+          "x": 0,
+          "y": 0
+        },
+        "id": 19,
+        "panels": [],
+        "title": "vLLM Engine",
+        "type": "row"
+      },
+      {
+        "datasource": {
+          "default": true,
+          "type": "prometheus",
+          "uid": "cdy727ch5evi8d"
+        },
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "thresholds"
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            }
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 3,
+          "x": 0,
+          "y": 1
+        },
+        "id": 16,
+        "options": {
+          "colorMode": "value",
+          "graphMode": "area",
+          "justifyMode": "auto",
+          "orientation": "auto",
+          "percentChangeColorMode": "standard",
+          "reduceOptions": {
+            "calcs": [
+              "lastNotNull"
+            ],
+            "fields": "",
+            "values": false
+          },
+          "showPercentChange": false,
+          "textMode": "auto",
+          "wideLayout": true
+        },
+        "pluginVersion": "11.2.0",
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "cdy727ch5evi8d"
+            },
+            "editorMode": "code",
+            "expr": "sum(increase(vllm:request_success_total{finished_reason=\"stop\", model_name=\"$model_name\", job=\"pods\"}[$__range]))",
+            "instant": false,
+            "legendFormat": "Success Request Count(stop)",
+            "range": true,
+            "refId": "A"
+          }
+        ],
+        "title": "Success Request Count(Stop)",
+        "type": "stat"
+      },
+      {
+        "datasource": {
+          "default": true,
+          "type": "prometheus",
+          "uid": "cdy727ch5evi8d"
+        },
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            }
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 5,
+          "x": 3,
+          "y": 1
+        },
+        "id": 17,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "pluginVersion": "11.2.0",
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "cdy727ch5evi8d"
+            },
+            "editorMode": "code",
+            "expr": "sum by(finished_reason) (increase(vllm:request_success_total{model_name=\"$model_name\", job=\"pods\"}[$__range]))",
+            "instant": false,
+            "legendFormat": "Success Request Count",
+            "range": true,
+            "refId": "A"
+          }
+        ],
+        "title": "Success Request Count",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Heatmap of request prompt length",
+        "fieldConfig": {
+          "defaults": {
+            "custom": {
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "scaleDistribution": {
+                "type": "linear"
+              }
+            }
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 5,
+          "x": 8,
+          "y": 1
+        },
+        "id": 12,
+        "options": {
+          "calculate": false,
+          "cellGap": 1,
+          "cellValues": {
+            "unit": "none"
+          },
+          "color": {
+            "exponent": 0.5,
+            "fill": "dark-orange",
+            "min": 0,
+            "mode": "scheme",
+            "reverse": false,
+            "scale": "exponential",
+            "scheme": "Spectral",
+            "steps": 64
+          },
+          "exemplars": {
+            "color": "rgba(255,0,255,0.7)"
+          },
+          "filterValues": {
+            "le": 1e-9
+          },
+          "legend": {
+            "show": true,
+            "showLegend": true
+          },
+          "rowsFrame": {
+            "layout": "auto",
+            "value": "Request count"
+          },
+          "tooltip": {
+            "mode": "single",
+            "showColorScale": false,
+            "yHistogram": true
+          },
+          "yAxis": {
+            "axisLabel": "Prompt Length",
+            "axisPlacement": "left",
+            "reverse": false,
+            "unit": "none"
+          }
+        },
+        "pluginVersion": "11.2.0",
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum by(le) (increase(vllm:request_prompt_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+            "format": "heatmap",
+            "fullMetaSearch": false,
+            "includeNullMetadata": true,
+            "instant": false,
+            "legendFormat": "{{le}}",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          }
+        ],
+        "title": "Request Prompt Length",
+        "type": "heatmap"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Heatmap of request generation length",
+        "fieldConfig": {
+          "defaults": {
+            "custom": {
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "scaleDistribution": {
+                "type": "linear"
+              }
+            }
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 5,
+          "x": 13,
+          "y": 1
+        },
+        "id": 13,
+        "options": {
+          "calculate": false,
+          "cellGap": 1,
+          "cellValues": {
+            "unit": "none"
+          },
+          "color": {
+            "exponent": 0.5,
+            "fill": "dark-orange",
+            "min": 0,
+            "mode": "scheme",
+            "reverse": false,
+            "scale": "exponential",
+            "scheme": "Spectral",
+            "steps": 64
+          },
+          "exemplars": {
+            "color": "rgba(255,0,255,0.7)"
+          },
+          "filterValues": {
+            "le": 1e-9
+          },
+          "legend": {
+            "show": true
+          },
+          "rowsFrame": {
+            "layout": "auto",
+            "value": "Request count"
+          },
+          "tooltip": {
+            "mode": "single",
+            "showColorScale": false,
+            "yHistogram": true
+          },
+          "yAxis": {
+            "axisLabel": "Generation Length",
+            "axisPlacement": "left",
+            "reverse": false,
+            "unit": "none"
+          }
+        },
+        "pluginVersion": "11.2.0",
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum by(le) (increase(vllm:request_generation_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+            "format": "heatmap",
+            "fullMetaSearch": false,
+            "includeNullMetadata": true,
+            "instant": false,
+            "legendFormat": "{{le}}",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          }
+        ],
+        "title": "Request Generation Length",
+        "type": "heatmap"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Prompt & Decode length",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            },
+            "unit": "none"
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 6,
+          "x": 18,
+          "y": 1
+        },
+        "id": 18,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.99, sum by(le) (rate(vllm:request_prompt_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "Prompt-P99",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.9, sum by(le) (rate(vllm:request_prompt_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "Prompt-P90",
+            "range": true,
+            "refId": "C",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.5, sum by(le) (rate(vllm:request_prompt_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "Prompt-P50",
+            "range": true,
+            "refId": "D",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.99, sum by(le) (rate(vllm:request_generation_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "hide": false,
+            "instant": false,
+            "legendFormat": "Decode-P99",
+            "range": true,
+            "refId": "B"
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.90, sum by(le) (rate(vllm:request_generation_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "hide": false,
+            "instant": false,
+            "legendFormat": "Decode-P90",
+            "range": true,
+            "refId": "E"
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.50, sum by(le) (rate(vllm:request_generation_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "hide": false,
+            "instant": false,
+            "legendFormat": "Decode-P50",
+            "range": true,
+            "refId": "F"
+          }
+        ],
+        "title": "Prompt & Decode Length",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "End to end request latency measured in seconds.",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            },
+            "unit": "s"
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 8,
+          "x": 0,
+          "y": 8
+        },
+        "id": 9,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.99, sum by(le) (rate(vllm:e2e_request_latency_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P99",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "builder",
+            "expr": "histogram_quantile(0.95, sum by(le) (rate(vllm:e2e_request_latency_seconds_bucket{model_name=\"$model_name\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P95",
+            "range": true,
+            "refId": "B",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "builder",
+            "expr": "histogram_quantile(0.9, sum by(le) (rate(vllm:e2e_request_latency_seconds_bucket{model_name=\"$model_name\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P90",
+            "range": true,
+            "refId": "C",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "builder",
+            "expr": "histogram_quantile(0.5, sum by(le) (rate(vllm:e2e_request_latency_seconds_bucket{model_name=\"$model_name\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P50",
+            "range": true,
+            "refId": "D",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "avg(rate(vllm:e2e_request_latency_seconds_sum{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])\n/\nrate(vllm:e2e_request_latency_seconds_count{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+            "hide": false,
+            "instant": false,
+            "legendFormat": "Average",
+            "range": true,
+            "refId": "E"
+          }
+        ],
+        "title": "E2E Request Latency",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "P50, P90, P95, and P99 TTFT latency in seconds.",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            },
+            "unit": "s"
+          },
+          "overrides": [
+            {
+              "__systemRef": "hideSeriesFrom",
+              "matcher": {
+                "id": "byNames",
+                "options": {
+                  "mode": "exclude",
+                  "names": [
+                    "Average"
+                  ],
+                  "prefix": "All except:",
+                  "readOnly": true
+                }
+              },
+              "properties": [
+                {
+                  "id": "custom.hideFrom",
+                  "value": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": true
+                  }
+                }
+              ]
+            }
+          ]
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 8,
+          "x": 8,
+          "y": 8
+        },
+        "id": 5,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.99, sum by(le) (rate(vllm:time_to_first_token_seconds_bucket{model_name=\"$model_name\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P99",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.95, sum by(le) (rate(vllm:time_to_first_token_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P95",
+            "range": true,
+            "refId": "B",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.9, sum by(le) (rate(vllm:time_to_first_token_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P90",
+            "range": true,
+            "refId": "C",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.5, sum by(le) (rate(vllm:time_to_first_token_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P50",
+            "range": true,
+            "refId": "D",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "avg(rate(vllm:time_to_first_token_seconds_sum{model_name=\"$model_name\",job=\"pods\"}[$__rate_interval])\n/\nrate(vllm:time_to_first_token_seconds_count{model_name=\"$model_name\",job=\"pods\"}[$__rate_interval]))",
+            "hide": false,
+            "instant": false,
+            "legendFormat": "Average",
+            "range": true,
+            "refId": "E"
+          }
+        ],
+        "title": "Time To First Token Latency",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Inter token latency in seconds.",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            },
+            "unit": "s"
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 7,
+          "w": 8,
+          "x": 16,
+          "y": 8
+        },
+        "id": 10,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.99, sum by(le) (rate(vllm:time_per_output_token_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P99",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.95, sum by(le) (rate(vllm:time_per_output_token_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P95",
+            "range": true,
+            "refId": "B",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.9, sum by(le) (rate(vllm:time_per_output_token_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P90",
+            "range": true,
+            "refId": "C",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "histogram_quantile(0.5, sum by(le) (rate(vllm:time_per_output_token_seconds_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "P50",
+            "range": true,
+            "refId": "D",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "rate(vllm:time_per_output_token_seconds_sum{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])\n/\nrate(vllm:time_per_output_token_seconds_count{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])",
+            "hide": false,
+            "instant": false,
+            "legendFormat": "Mean",
+            "range": true,
+            "refId": "E"
+          }
+        ],
+        "title": "Time Per Output Token Latency",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Number of tokens processed per second",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            }
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 8,
+          "w": 8,
+          "x": 0,
+          "y": 15
+        },
+        "id": 8,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum(rate(vllm:prompt_tokens_total{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+            "fullMetaSearch": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "Prompt Tokens/Sec {{model_name}}",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          }
+        ],
+        "title": "Prompt Token Throughput",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Number of tokens processed per second",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            }
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 8,
+          "w": 8,
+          "x": 8,
+          "y": 15
+        },
+        "id": 14,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum(rate(vllm:generation_tokens_total{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": false,
+            "instant": false,
+            "legendFormat": "Generation Tokens/Sec {{pod}}",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          }
+        ],
+        "title": "Decode Token Throughput",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Number of requests in RUNNING, WAITING, and SWAPPED state",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            },
+            "unit": "none"
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 8,
+          "w": 8,
+          "x": 16,
+          "y": 15
+        },
+        "id": 3,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum(vllm:num_requests_running{model_name=\"$model_name\", job=\"pods\"})",
+            "fullMetaSearch": false,
+            "includeNullMetadata": true,
+            "instant": false,
+            "legendFormat": "Num Running",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum(vllm:num_requests_swapped{model_name=\"$model_name\", job=\"pods\"})",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": true,
+            "instant": false,
+            "legendFormat": "Num Swapped",
+            "range": true,
+            "refId": "B",
+            "useBackend": false
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum(vllm:num_requests_waiting{model_name=\"$model_name\", job=\"pods\"})",
+            "fullMetaSearch": false,
+            "hide": false,
+            "includeNullMetadata": true,
+            "instant": false,
+            "legendFormat": "Num Waiting",
+            "range": true,
+            "refId": "C",
+            "useBackend": false
+          }
+        ],
+        "title": "Scheduler State",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Percentage of prefix cache hit rate",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            },
+            "unit": "percentunit"
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 8,
+          "w": 8,
+          "x": 0,
+          "y": 23
+        },
+        "id": 15,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "avg(vllm:gpu_prefix_cache_hit_rate{model_name=\"$model_name\",job=\"pods\"})",
+            "instant": false,
+            "legendFormat": "GPU Prefix Cache Hit Ratio",
+            "range": true,
+            "refId": "A"
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "avg(vllm:cpu_prefix_cache_hit_rate{model_name=\"$model_name\",job=\"pods\"})",
+            "hide": true,
+            "instant": false,
+            "legendFormat": "CPU Prefix Cache Hit Ratio",
+            "range": true,
+            "refId": "B"
+          }
+        ],
+        "title": "Prefix Cache Hit Rate",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Percentage of used cache blocks by vLLM.",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            },
+            "unit": "percentunit"
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 8,
+          "w": 8,
+          "x": 8,
+          "y": 23
+        },
+        "id": 4,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "avg(vllm:gpu_cache_usage_perc{model_name=\"$model_name\", job=\"pods\"})",
+            "instant": false,
+            "legendFormat": "GPU Cache Usage",
+            "range": true,
+            "refId": "A"
+          },
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "editorMode": "code",
+            "expr": "avg(vllm:cpu_cache_usage_perc{model_name=\"$model_name\", job=\"pods\"})",
+            "hide": false,
+            "instant": false,
+            "legendFormat": "CPU Cache Usage",
+            "range": true,
+            "refId": "B"
+          }
+        ],
+        "title": "Cache Utilization",
+        "type": "timeseries"
+      },
+      {
+        "datasource": {
+          "default": false,
+          "type": "prometheus",
+          "uid": "${DS_PROMETHEUS}"
+        },
+        "description": "Number of finished requests by their finish reason: either an EOS token was generated or the max sequence length was reached.",
+        "fieldConfig": {
+          "defaults": {
+            "color": {
+              "mode": "palette-classic"
+            },
+            "custom": {
+              "axisBorderShow": false,
+              "axisCenteredZero": false,
+              "axisColorMode": "text",
+              "axisLabel": "",
+              "axisPlacement": "auto",
+              "barAlignment": 0,
+              "barWidthFactor": 0.6,
+              "drawStyle": "line",
+              "fillOpacity": 0,
+              "gradientMode": "none",
+              "hideFrom": {
+                "legend": false,
+                "tooltip": false,
+                "viz": false
+              },
+              "insertNulls": false,
+              "lineInterpolation": "linear",
+              "lineWidth": 1,
+              "pointSize": 5,
+              "scaleDistribution": {
+                "type": "linear"
+              },
+              "showPoints": "auto",
+              "spanNulls": false,
+              "stacking": {
+                "group": "A",
+                "mode": "none"
+              },
+              "thresholdsStyle": {
+                "mode": "off"
+              }
+            },
+            "mappings": [],
+            "thresholds": {
+              "mode": "absolute",
+              "steps": [
+                {
+                  "color": "green",
+                  "value": null
+                },
+                {
+                  "color": "red",
+                  "value": 80
+                }
+              ]
+            }
+          },
+          "overrides": []
+        },
+        "gridPos": {
+          "h": 8,
+          "w": 8,
+          "x": 16,
+          "y": 23
+        },
+        "id": 11,
+        "options": {
+          "legend": {
+            "calcs": [],
+            "displayMode": "list",
+            "placement": "bottom",
+            "showLegend": true
+          },
+          "tooltip": {
+            "mode": "single",
+            "sort": "none"
+          }
+        },
+        "targets": [
+          {
+            "datasource": {
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "disableTextWrap": false,
+            "editorMode": "code",
+            "expr": "sum by(finished_reason) (increase(vllm:request_success_total{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+            "fullMetaSearch": false,
+            "includeNullMetadata": true,
+            "instant": false,
+            "interval": "",
+            "legendFormat": "__auto",
+            "range": true,
+            "refId": "A",
+            "useBackend": false
+          }
+        ],
+        "title": "Finish Reason",
+        "type": "timeseries"
+      },
+      {
+        "collapsed": true,
+        "gridPos": {
+          "h": 1,
+          "w": 24,
+          "x": 0,
+          "y": 31
+        },
+        "id": 31,
+        "panels": [
+          {
+            "datasource": {
+              "default": true,
+              "type": "prometheus",
+              "uid": "cdy727ch5evi8d"
+            },
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "thresholds"
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 4,
+              "x": 0,
+              "y": 32
+            },
+            "id": 39,
+            "options": {
+              "colorMode": "value",
+              "graphMode": "area",
+              "justifyMode": "auto",
+              "orientation": "auto",
+              "percentChangeColorMode": "standard",
+              "reduceOptions": {
+                "calcs": [
+                  "lastNotNull"
+                ],
+                "fields": "",
+                "values": false
+              },
+              "showPercentChange": false,
+              "textMode": "auto",
+              "wideLayout": true
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "cdy727ch5evi8d"
+                },
+                "editorMode": "code",
+                "expr": "sum by(op_type) (increase(vllm:ol_connector_num_ops_total{model_name=\"$model_name\", job=\"pods\"}[$__range]))",
+                "instant": false,
+                "legendFormat": "{{op_type}}",
+                "range": true,
+                "refId": "A"
+              }
+            ],
+            "title": "Total Ops",
+            "type": "stat"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Heatmap of prefix length",
+            "fieldConfig": {
+              "defaults": {
+                "custom": {
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "scaleDistribution": {
+                    "type": "linear"
+                  }
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 4,
+              "y": 32
+            },
+            "id": 29,
+            "options": {
+              "calculate": false,
+              "cellGap": 1,
+              "cellValues": {
+                "unit": "none"
+              },
+              "color": {
+                "exponent": 0.5,
+                "fill": "dark-orange",
+                "min": 0,
+                "mode": "scheme",
+                "reverse": false,
+                "scale": "exponential",
+                "scheme": "Spectral",
+                "steps": 64
+              },
+              "exemplars": {
+                "color": "rgba(255,0,255,0.7)"
+              },
+              "filterValues": {
+                "le": 1e-9
+              },
+              "legend": {
+                "show": true,
+                "showLegend": true
+              },
+              "rowsFrame": {
+                "layout": "auto",
+                "value": "Request count"
+              },
+              "tooltip": {
+                "mode": "single",
+                "showColorScale": false,
+                "yHistogram": true
+              },
+              "yAxis": {
+                "axisLabel": "Prompt Length",
+                "axisPlacement": "left",
+                "reverse": false,
+                "unit": "none"
+              }
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "sum by(le, op_type) (increase(vllm:ol_connector_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+                "format": "heatmap",
+                "fullMetaSearch": false,
+                "includeNullMetadata": true,
+                "instant": false,
+                "legendFormat": "{{op_type}}-{{le}}",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              }
+            ],
+            "title": "Prefix Length",
+            "type": "heatmap"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Prefix length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 9,
+              "y": 32
+            },
+            "id": 30,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:ol_connector_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:ol_connector_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:ol_connector_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Prefix Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Heatmap of token length",
+            "fieldConfig": {
+              "defaults": {
+                "custom": {
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "scaleDistribution": {
+                    "type": "linear"
+                  }
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 14,
+              "y": 32
+            },
+            "id": 32,
+            "options": {
+              "calculate": false,
+              "cellGap": 1,
+              "cellValues": {
+                "unit": "none"
+              },
+              "color": {
+                "exponent": 0.5,
+                "fill": "dark-orange",
+                "min": 0,
+                "mode": "scheme",
+                "reverse": false,
+                "scale": "exponential",
+                "scheme": "Spectral",
+                "steps": 64
+              },
+              "exemplars": {
+                "color": "rgba(255,0,255,0.7)"
+              },
+              "filterValues": {
+                "le": 1e-9
+              },
+              "legend": {
+                "show": true,
+                "showLegend": true
+              },
+              "rowsFrame": {
+                "layout": "auto",
+                "value": "Request count"
+              },
+              "tooltip": {
+                "mode": "single",
+                "showColorScale": false,
+                "yHistogram": true
+              },
+              "yAxis": {
+                "axisLabel": "Prompt Length",
+                "axisPlacement": "left",
+                "reverse": false,
+                "unit": "none"
+              }
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "sum by(le, op_type) (increase(vllm:ol_connector_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+                "format": "heatmap",
+                "fullMetaSearch": false,
+                "includeNullMetadata": true,
+                "instant": false,
+                "legendFormat": "{{op_type}}-{{le}}",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              }
+            ],
+            "title": "Token Length",
+            "type": "heatmap"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Token length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 19,
+              "y": 32
+            },
+            "id": 33,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:ol_connector_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:ol_connector_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:ol_connector_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Token Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Latency",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "ms"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 6,
+              "x": 0,
+              "y": 39
+            },
+            "id": 34,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:ol_connector_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:ol_connector_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:ol_connector_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le) (rate(vllm:ol_connector_iteration_rebuild_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "hide": false,
+                "instant": false,
+                "legendFormat": "rebuild_model_input-P99",
+                "range": true,
+                "refId": "B"
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le) (rate(vllm:ol_connector_iteration_rebuild_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "hide": false,
+                "instant": false,
+                "legendFormat": "rebuild_model_input-P90",
+                "range": true,
+                "refId": "E"
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le) (rate(vllm:ol_connector_iteration_rebuild_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "hide": false,
+                "instant": false,
+                "legendFormat": "rebuild_model_input-P50",
+                "range": true,
+                "refId": "F"
+              }
+            ],
+            "title": "Latency",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Heatmap of sent tokens",
+            "fieldConfig": {
+              "defaults": {
+                "custom": {
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "scaleDistribution": {
+                    "type": "linear"
+                  }
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 4,
+              "x": 6,
+              "y": 39
+            },
+            "id": 35,
+            "options": {
+              "calculate": false,
+              "cellGap": 1,
+              "cellValues": {
+                "unit": "none"
+              },
+              "color": {
+                "exponent": 0.5,
+                "fill": "dark-orange",
+                "min": 0,
+                "mode": "scheme",
+                "reverse": false,
+                "scale": "exponential",
+                "scheme": "Spectral",
+                "steps": 64
+              },
+              "exemplars": {
+                "color": "rgba(255,0,255,0.7)"
+              },
+              "filterValues": {
+                "le": 1e-9
+              },
+              "legend": {
+                "show": true,
+                "showLegend": true
+              },
+              "rowsFrame": {
+                "layout": "auto",
+                "value": "Request count"
+              },
+              "tooltip": {
+                "mode": "single",
+                "showColorScale": false,
+                "yHistogram": true
+              },
+              "yAxis": {
+                "axisLabel": "Prompt Length",
+                "axisPlacement": "left",
+                "reverse": false,
+                "unit": "none"
+              }
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "sum by(le, op_type) (increase(vllm:ol_connector_iteration_sent_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+                "format": "heatmap",
+                "fullMetaSearch": false,
+                "includeNullMetadata": true,
+                "instant": false,
+                "legendFormat": "{{op_type}}-{{le}}",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              }
+            ],
+            "title": "Sent Tokens",
+            "type": "heatmap"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Sent tokens",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 10,
+              "y": 39
+            },
+            "id": 36,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:ol_connector_iteration_sent_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:ol_connector_iteration_sent_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:ol_connector_iteration_sent_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Sent Tokens",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Heatmap of received tokens",
+            "fieldConfig": {
+              "defaults": {
+                "custom": {
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "scaleDistribution": {
+                    "type": "linear"
+                  }
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 4,
+              "x": 15,
+              "y": 39
+            },
+            "id": 37,
+            "options": {
+              "calculate": false,
+              "cellGap": 1,
+              "cellValues": {
+                "unit": "none"
+              },
+              "color": {
+                "exponent": 0.5,
+                "fill": "dark-orange",
+                "min": 0,
+                "mode": "scheme",
+                "reverse": false,
+                "scale": "exponential",
+                "scheme": "Spectral",
+                "steps": 64
+              },
+              "exemplars": {
+                "color": "rgba(255,0,255,0.7)"
+              },
+              "filterValues": {
+                "le": 1e-9
+              },
+              "legend": {
+                "show": true,
+                "showLegend": true
+              },
+              "rowsFrame": {
+                "layout": "auto",
+                "value": "Request count"
+              },
+              "tooltip": {
+                "mode": "single",
+                "showColorScale": false,
+                "yHistogram": true
+              },
+              "yAxis": {
+                "axisLabel": "Prompt Length",
+                "axisPlacement": "left",
+                "reverse": false,
+                "unit": "none"
+              }
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "sum by(le, op_type) (increase(vllm:ol_connector_iteration_received_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval]))",
+                "format": "heatmap",
+                "fullMetaSearch": false,
+                "includeNullMetadata": true,
+                "instant": false,
+                "legendFormat": "{{op_type}}-{{le}}",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              }
+            ],
+            "title": "Received Tokens",
+            "type": "heatmap"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Received tokens",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 19,
+              "y": 39
+            },
+            "id": 38,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:ol_connector_iteration_received_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:ol_connector_iteration_received_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:ol_connector_iteration_received_tokens_bucket{model_name=\"$model_name\", job=\"pods\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Received Tokens",
+            "type": "timeseries"
+          }
+        ],
+        "title": "KV Cache Offloading - AIBrix Connector",
+        "type": "row"
+      },
+      {
+        "collapsed": true,
+        "gridPos": {
+          "h": 1,
+          "w": 24,
+          "x": 0,
+          "y": 32
+        },
+        "id": 40,
+        "panels": [
+          {
+            "datasource": {
+              "default": true,
+              "type": "prometheus",
+              "uid": "cdy727ch5evi8d"
+            },
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "thresholds"
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 4,
+              "x": 0,
+              "y": 33
+            },
+            "id": 47,
+            "options": {
+              "colorMode": "value",
+              "graphMode": "area",
+              "justifyMode": "auto",
+              "orientation": "auto",
+              "percentChangeColorMode": "standard",
+              "reduceOptions": {
+                "calcs": [
+                  "lastNotNull"
+                ],
+                "fields": "",
+                "values": false
+              },
+              "showPercentChange": false,
+              "textMode": "auto",
+              "wideLayout": true
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "cdy727ch5evi8d"
+                },
+                "editorMode": "code",
+                "expr": "sum by(op_type) (increase(vllm:kv_cache_ol_num_ops_total{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__range]))",
+                "instant": false,
+                "legendFormat": "{{op_type}}",
+                "range": true,
+                "refId": "A"
+              }
+            ],
+            "title": "Total Ops",
+            "type": "stat"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Prefix length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 4,
+              "y": 33
+            },
+            "id": 50,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Prefix Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Token length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 9,
+              "y": 33
+            },
+            "id": 51,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Token Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Fetched tokens",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 14,
+              "y": 33
+            },
+            "id": 53,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Fetched Tokens",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Latency",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "ms"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 19,
+              "y": 33
+            },
+            "id": 45,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"cachemgr\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Latency",
+            "type": "timeseries"
+          }
+        ],
+        "title": "KV Cache Offloading - Internal - CacheMgr",
+        "type": "row"
+      },
+      {
+        "collapsed": true,
+        "gridPos": {
+          "h": 1,
+          "w": 24,
+          "x": 0,
+          "y": 33
+        },
+        "id": 57,
+        "panels": [
+          {
+            "datasource": {
+              "default": true,
+              "type": "prometheus",
+              "uid": "cdy727ch5evi8d"
+            },
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "thresholds"
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 4,
+              "x": 0,
+              "y": 34
+            },
+            "id": 48,
+            "options": {
+              "colorMode": "value",
+              "graphMode": "area",
+              "justifyMode": "auto",
+              "orientation": "auto",
+              "percentChangeColorMode": "standard",
+              "reduceOptions": {
+                "calcs": [
+                  "lastNotNull"
+                ],
+                "fields": "",
+                "values": false
+              },
+              "showPercentChange": false,
+              "textMode": "auto",
+              "wideLayout": true
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "cdy727ch5evi8d"
+                },
+                "editorMode": "code",
+                "expr": "sum by(op_type) (increase(vllm:kv_cache_ol_num_ops_total{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__range]))",
+                "instant": false,
+                "legendFormat": "{{op_type}}",
+                "range": true,
+                "refId": "A"
+              }
+            ],
+            "title": "Total Ops",
+            "type": "stat"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Prefix length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 4,
+              "y": 34
+            },
+            "id": 43,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Prefix Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Token length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 9,
+              "y": 34
+            },
+            "id": 44,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Token Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Fetched tokens",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 14,
+              "y": 34
+            },
+            "id": 46,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Fetched Tokens",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Latency",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "ms"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 19,
+              "y": 34
+            },
+            "id": 55,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l1cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Latency",
+            "type": "timeseries"
+          }
+        ],
+        "title": "KV Cache Offloading - Internal - L1Cache",
+        "type": "row"
+      },
+      {
+        "collapsed": true,
+        "gridPos": {
+          "h": 1,
+          "w": 24,
+          "x": 0,
+          "y": 34
+        },
+        "id": 58,
+        "panels": [
+          {
+            "datasource": {
+              "default": true,
+              "type": "prometheus",
+              "uid": "cdy727ch5evi8d"
+            },
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "thresholds"
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                }
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 4,
+              "x": 0,
+              "y": 35
+            },
+            "id": 41,
+            "options": {
+              "colorMode": "value",
+              "graphMode": "area",
+              "justifyMode": "auto",
+              "orientation": "auto",
+              "percentChangeColorMode": "standard",
+              "reduceOptions": {
+                "calcs": [
+                  "lastNotNull"
+                ],
+                "fields": "",
+                "values": false
+              },
+              "showPercentChange": false,
+              "textMode": "auto",
+              "wideLayout": true
+            },
+            "pluginVersion": "11.2.0",
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "cdy727ch5evi8d"
+                },
+                "editorMode": "code",
+                "expr": "sum by(op_type) (increase(vllm:kv_cache_ol_num_ops_total{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__range]))",
+                "instant": false,
+                "legendFormat": "{{op_type}}",
+                "range": true,
+                "refId": "A"
+              }
+            ],
+            "title": "Total Ops",
+            "type": "stat"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Prefix length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 4,
+              "y": 35
+            },
+            "id": 49,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_prefixes_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Prefix Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Token length",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 9,
+              "y": 35
+            },
+            "id": 52,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Token Length",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Fetched tokens",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "none"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 14,
+              "y": 35
+            },
+            "id": 54,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_fetched_tokens_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Fetched Tokens",
+            "type": "timeseries"
+          },
+          {
+            "datasource": {
+              "default": false,
+              "type": "prometheus",
+              "uid": "${DS_PROMETHEUS}"
+            },
+            "description": "Latency",
+            "fieldConfig": {
+              "defaults": {
+                "color": {
+                  "mode": "palette-classic"
+                },
+                "custom": {
+                  "axisBorderShow": false,
+                  "axisCenteredZero": false,
+                  "axisColorMode": "text",
+                  "axisLabel": "",
+                  "axisPlacement": "auto",
+                  "barAlignment": 0,
+                  "barWidthFactor": 0.6,
+                  "drawStyle": "line",
+                  "fillOpacity": 0,
+                  "gradientMode": "none",
+                  "hideFrom": {
+                    "legend": false,
+                    "tooltip": false,
+                    "viz": false
+                  },
+                  "insertNulls": false,
+                  "lineInterpolation": "linear",
+                  "lineWidth": 1,
+                  "pointSize": 5,
+                  "scaleDistribution": {
+                    "type": "linear"
+                  },
+                  "showPoints": "auto",
+                  "spanNulls": false,
+                  "stacking": {
+                    "group": "A",
+                    "mode": "none"
+                  },
+                  "thresholdsStyle": {
+                    "mode": "off"
+                  }
+                },
+                "mappings": [],
+                "thresholds": {
+                  "mode": "absolute",
+                  "steps": [
+                    {
+                      "color": "green",
+                      "value": null
+                    },
+                    {
+                      "color": "red",
+                      "value": 80
+                    }
+                  ]
+                },
+                "unit": "ms"
+              },
+              "overrides": []
+            },
+            "gridPos": {
+              "h": 7,
+              "w": 5,
+              "x": 19,
+              "y": 35
+            },
+            "id": 56,
+            "options": {
+              "legend": {
+                "calcs": [],
+                "displayMode": "list",
+                "placement": "bottom",
+                "showLegend": true
+              },
+              "tooltip": {
+                "mode": "single",
+                "sort": "none"
+              }
+            },
+            "targets": [
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.99, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P99",
+                "range": true,
+                "refId": "A",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.9, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P90",
+                "range": true,
+                "refId": "C",
+                "useBackend": false
+              },
+              {
+                "datasource": {
+                  "type": "prometheus",
+                  "uid": "${DS_PROMETHEUS}"
+                },
+                "disableTextWrap": false,
+                "editorMode": "code",
+                "expr": "histogram_quantile(0.5, sum by(le, op_type) (rate(vllm:kv_cache_ol_iteration_op_lat_ms_bucket{model_name=\"$model_name\", job=\"pods\", cache_type=\"l2cache\"}[$__rate_interval])))",
+                "fullMetaSearch": false,
+                "hide": false,
+                "includeNullMetadata": false,
+                "instant": false,
+                "legendFormat": "{{op_type}}-P50",
+                "range": true,
+                "refId": "D",
+                "useBackend": false
+              }
+            ],
+            "title": "Latency",
+            "type": "timeseries"
+          }
+        ],
+        "title": "KV Cache Offloading - Internal - L2Cache",
+        "type": "row"
+      }
+    ],
+    "refresh": "",
+    "schemaVersion": 39,
+    "tags": [
+      "vllm",
+      "model"
+    ],
+    "templating": {
+      "list": [
+        {
+          "current": {
+            "selected": false,
+            "text": "prometheus",
+            "value": "cdy727ch5evi8d"
+          },
+          "hide": 0,
+          "includeAll": false,
+          "label": "datasource",
+          "multi": false,
+          "name": "DS_PROMETHEUS",
+          "options": [],
+          "query": "prometheus",
+          "queryValue": "",
+          "refresh": 1,
+          "regex": "",
+          "skipUrlSync": false,
+          "type": "datasource"
+        },
+        {
+          "current": {
+            "selected": true,
+            "text": "/models/deepseek-coder-6.7b-instruct",
+            "value": "/models/deepseek-coder-6.7b-instruct"
+          },
+          "datasource": {
+            "type": "prometheus",
+            "uid": "${DS_PROMETHEUS}"
+          },
+          "definition": "label_values(model_name)",
+          "hide": 0,
+          "includeAll": false,
+          "label": "model_name",
+          "multi": false,
+          "name": "model_name",
+          "options": [],
+          "query": {
+            "qryType": 1,
+            "query": "label_values(model_name)",
+            "refId": "PrometheusVariableQueryEditor-VariableQuery"
+          },
+          "refresh": 1,
+          "regex": "",
+          "skipUrlSync": false,
+          "sort": 0,
+          "type": "query"
+        }
+      ]
+    },
+    "time": {
+      "from": "now-5m",
+      "to": "now"
+    },
+    "timepicker": {},
+    "timezone": "",
+    "title": "AIBrix Engine Dashboard (vLLM)",
+    "uid": "ce2azhsbhel8gd",
+    "version": 41,
+    "weekStart": ""
+  }
diff --git a/requirements/common.txt b/requirements/common.txt
index 33c4c3219..fe343b5ca 100644
--- a/requirements/common.txt
+++ b/requirements/common.txt
@@ -47,3 +47,4 @@ opentelemetry-sdk>=1.26.0,<1.27.0  # vllm.tracing
 opentelemetry-api>=1.26.0,<1.27.0  # vllm.tracing
 opentelemetry-exporter-otlp>=1.26.0,<1.27.0  # vllm.tracing
 opentelemetry-semantic-conventions-ai>=0.4.1,<0.5.0  # vllm.tracing
+aibrix-kvcache # Required for kv cache offloading
diff --git a/tests/kernels/attention/test_cache.py b/tests/kernels/attention/test_cache.py
index 2f2212dd2..9cf51f3e6 100644
--- a/tests/kernels/attention/test_cache.py
+++ b/tests/kernels/attention/test_cache.py
@@ -8,6 +8,7 @@ import torch
 from tests.kernels.utils import DEFAULT_OPCHECK_TEST_UTILS, opcheck
 from vllm import _custom_ops as ops
 from vllm.platforms import current_platform
+from vllm.utils import get_kv_cache_torch_dtype
 
 COPYING_DIRECTION = [('cuda', 'cpu'), ('cuda', 'cuda'), ('cpu', 'cuda')]
 DTYPES = [torch.half, torch.bfloat16, torch.float]
@@ -349,6 +350,296 @@ def test_reshape_and_cache_flash(
         torch.testing.assert_close(value_cache_compact, cloned_value_cache)
 
 
+@pytest.mark.parametrize("num_layers", [8])
+@pytest.mark.parametrize("num_heads", NUM_HEADS)
+@pytest.mark.parametrize("head_size", HEAD_SIZES[:2])
+@pytest.mark.parametrize("block_size", BLOCK_SIZES[:1])
+@pytest.mark.parametrize("num_blocks", NUM_BLOCKS[:1])
+@pytest.mark.parametrize("dtype", DTYPES)
+@pytest.mark.parametrize("seed", SEEDS)
+@pytest.mark.parametrize("device", CUDA_DEVICES)
+@pytest.mark.parametrize("kv_cache_dtype", ["auto"])
+@pytest.mark.parametrize("offload_layout", ["LCND", "NCLD"])
+@torch.inference_mode()
+def test_reshape_and_cache_multi_layer(
+    kv_cache_factory_flashinfer,
+    num_layers: int,
+    num_heads: int,
+    head_size: int,
+    block_size: int,
+    num_blocks: int,
+    dtype: torch.dtype,
+    seed: int,
+    device: str,
+    kv_cache_dtype: str,
+    offload_layout: str,
+) -> None:
+    current_platform.seed_everything(seed)
+    torch.set_default_device(device)
+
+    # Create a random slot mapping.
+    num_slots = block_size * num_blocks
+    num_tokens = num_slots
+    slot_mapping_lst = random.sample(range(num_slots), num_tokens)
+    slot_mapping = torch.tensor(slot_mapping_lst,
+                                dtype=torch.long,
+                                device=device)
+
+    embed_dim = num_heads * head_size
+    offload_kv_cache_blocks = []
+    if offload_layout == "LCND":
+        for _ in range(num_blocks):
+            offload_kv_cache_block = torch.randn(
+                num_layers,
+                2,
+                block_size,
+                embed_dim,
+                dtype=get_kv_cache_torch_dtype(kv_cache_dtype, dtype),
+                device="cpu",
+                pin_memory=True,
+            )
+            offload_kv_cache_blocks.append(offload_kv_cache_block)
+    else:  # offload_layout == "NCLD"
+        for _ in range(num_blocks):
+            offload_kv_cache_block = torch.randn(
+                block_size,
+                2,
+                num_layers,
+                embed_dim,
+                dtype=get_kv_cache_torch_dtype(kv_cache_dtype, dtype),
+                device="cpu",
+                pin_memory=True,
+            )
+            offload_kv_cache_blocks.append(offload_kv_cache_block)
+
+    # Create the KV caches.
+    key_caches, value_caches = kv_cache_factory_flashinfer(
+        num_blocks,
+        block_size,
+        num_layers,
+        num_heads,
+        head_size,
+        kv_cache_dtype,
+        dtype,
+        device=device,
+    )
+    kv_caches = [
+        torch.stack((key_caches[i], value_caches[i]))
+        for i in range(num_layers)
+    ]
+
+    scale = max(block.amax() for block in offload_kv_cache_blocks)
+    scale = (scale / 64.0).to(torch.float32).to(device)
+    scales = [scale] * num_layers
+
+    # Clone the KV caches.
+    cloned_kv_caches = [kv_cache.clone() for kv_cache in kv_caches]
+
+    # opcheck(
+    #     torch.ops._C_cache_ops.reshape_and_cache_multi_layer,
+    #     (
+    #         offload_kv_cache_blocks,
+    #         kv_caches,
+    #         slot_mapping,
+    #         block_size,
+    #         kv_cache_dtype,
+    #         scales,
+    #         scales,
+    #         offload_layout,
+    #     ),
+    #     cond=(head_size == HEAD_SIZES[0]),
+    # )
+
+    ops.reshape_and_cache_multi_layer(
+        offload_kv_cache_blocks,
+        kv_caches,
+        slot_mapping,
+        block_size,
+        kv_cache_dtype,
+        scales,
+        scales,
+        offload_layout,
+    )
+
+    # Run the reference implementation.
+    block_indicies = torch.div(slot_mapping, block_size, rounding_mode="floor")
+    block_indicies_lst = block_indicies.cpu().tolist()
+    block_offsets = slot_mapping % block_size
+    block_offsets_lst = block_offsets.cpu().tolist()
+    for i in range(num_layers):
+        for j in range(num_tokens):
+            block_idx = block_indicies_lst[j]
+            block_offset = block_offsets_lst[j]
+            ol_block_idx = j // block_size
+            ol_block_offset = j % block_size
+            if offload_layout == "LCND":
+                cloned_kv_caches[i][0, block_idx, block_offset, :] = (
+                    offload_kv_cache_blocks[ol_block_idx][
+                        i, 0, ol_block_offset].view(num_heads, head_size))
+                cloned_kv_caches[i][1, block_idx, block_offset, :] = (
+                    offload_kv_cache_blocks[ol_block_idx][
+                        i, 1, ol_block_offset].view(num_heads, head_size))
+            else:
+                cloned_kv_caches[i][0, block_idx, block_offset, :] = (
+                    offload_kv_cache_blocks[ol_block_idx][ol_block_offset, 0,
+                                                          i].view(
+                                                              num_heads,
+                                                              head_size))
+                cloned_kv_caches[i][1, block_idx, block_offset, :] = (
+                    offload_kv_cache_blocks[ol_block_idx][ol_block_offset, 1,
+                                                          i].view(
+                                                              num_heads,
+                                                              head_size))
+
+    for i in range(num_layers):
+        torch.testing.assert_close(kv_caches[i], cloned_kv_caches[i])
+
+
+@pytest.mark.parametrize("num_layers", [8])
+@pytest.mark.parametrize("num_heads", NUM_HEADS)
+@pytest.mark.parametrize("head_size", HEAD_SIZES[:2])
+@pytest.mark.parametrize("block_size", BLOCK_SIZES[:1])
+@pytest.mark.parametrize("num_blocks", NUM_BLOCKS[:1])
+@pytest.mark.parametrize("dtype", DTYPES)
+@pytest.mark.parametrize("seed", SEEDS)
+@pytest.mark.parametrize("device", CUDA_DEVICES)
+@pytest.mark.parametrize("kv_cache_dtype", ["auto"])
+@pytest.mark.parametrize("offload_layout", ["LCND", "NCLD"])
+@torch.inference_mode()
+def test_reshape_and_offload_multi_layer(
+    kv_cache_factory_flashinfer,
+    num_layers: int,
+    num_heads: int,
+    head_size: int,
+    block_size: int,
+    num_blocks: int,
+    dtype: torch.dtype,
+    seed: int,
+    device: str,
+    kv_cache_dtype: str,
+    offload_layout: str,
+) -> None:
+    current_platform.seed_everything(seed)
+    torch.set_default_device(device)
+
+    # Create a random slot mapping.
+    num_slots = block_size * num_blocks
+    num_tokens = num_slots
+    slot_mapping_lst = random.sample(range(num_slots), num_tokens)
+    slot_mapping = torch.tensor(slot_mapping_lst,
+                                dtype=torch.long,
+                                device=device)
+
+    embed_dim = num_heads * head_size
+    offload_kv_cache_blocks = []
+    if offload_layout == "LCND":
+        for _ in range(num_blocks):
+            offload_kv_cache_blocks.append(
+                torch.randn(
+                    num_layers,
+                    2,
+                    block_size,
+                    embed_dim,
+                    dtype=get_kv_cache_torch_dtype(kv_cache_dtype, dtype),
+                    device="cpu",
+                    pin_memory=True,
+                ))
+    else:  # offload_layout == "NCLD"
+        for _ in range(num_blocks):
+            offload_kv_cache_blocks.append(
+                torch.randn(
+                    block_size,
+                    2,
+                    num_layers,
+                    embed_dim,
+                    dtype=get_kv_cache_torch_dtype(kv_cache_dtype, dtype),
+                    device="cpu",
+                    pin_memory=True,
+                ))
+
+    # Create the KV caches.
+    key_caches, value_caches = kv_cache_factory_flashinfer(
+        num_blocks,
+        block_size,
+        num_layers,
+        num_heads,
+        head_size,
+        kv_cache_dtype,
+        dtype,
+        device=device,
+    )
+    kv_caches = [
+        torch.stack((key_caches[i], value_caches[i]))
+        for i in range(num_layers)
+    ]
+
+    scales = [kv_cache.amax() for kv_cache in kv_caches]
+    scales = [(scale / 64.0).to(torch.float32) for scale in scales]
+
+    # Clone the offload kc cache blocks.
+    cloned_offload_kv_cache_blocks = [
+        block.clone() for block in offload_kv_cache_blocks
+    ]
+
+    # opcheck(
+    #     torch.ops._C_cache_ops.reshape_and_offload_multi_layer,
+    #     (
+    #         offload_kv_cache_blocks,
+    #         kv_caches,
+    #         slot_mapping,
+    #         block_size,
+    #         kv_cache_dtype,
+    #         scales,
+    #         scales,
+    #         offload_layout,
+    #     ),
+    #     cond=(head_size == HEAD_SIZES[0]),
+    # )
+
+    ops.reshape_and_offload_multi_layer(
+        offload_kv_cache_blocks,
+        kv_caches,
+        slot_mapping,
+        block_size,
+        kv_cache_dtype,
+        scales,
+        scales,
+        offload_layout,
+    )
+
+    # Run the reference implementation.
+    block_indicies = torch.div(slot_mapping, block_size, rounding_mode="floor")
+    block_indicies_lst = block_indicies.cpu().tolist()
+    block_offsets = slot_mapping % block_size
+    block_offsets_lst = block_offsets.cpu().tolist()
+    for i in range(num_layers):
+        for j in range(num_tokens):
+            block_idx = block_indicies_lst[j]
+            block_offset = block_offsets_lst[j]
+            ol_block_idx = j // block_size
+            ol_block_offset = j % block_size
+            if offload_layout == "LCND":
+                cloned_offload_kv_cache_blocks[ol_block_idx][
+                    i, 0, ol_block_offset] = kv_caches[i][
+                        0, block_idx, block_offset, :].view(embed_dim)
+                cloned_offload_kv_cache_blocks[ol_block_idx][
+                    i, 1, ol_block_offset] = kv_caches[i][
+                        1, block_idx, block_offset, :].view(embed_dim)
+            else:
+                cloned_offload_kv_cache_blocks[ol_block_idx][
+                    ol_block_offset, 0,
+                    i] = kv_caches[i][0, block_idx,
+                                      block_offset, :].view(embed_dim)
+                cloned_offload_kv_cache_blocks[ol_block_idx][
+                    ol_block_offset, 1,
+                    i] = kv_caches[i][1, block_idx,
+                                      block_offset, :].view(embed_dim)
+
+    for i in range(num_blocks):
+        torch.testing.assert_close(offload_kv_cache_blocks[i],
+                                   cloned_offload_kv_cache_blocks[i])
+
+
 @pytest.mark.parametrize("direction", COPYING_DIRECTION)
 @pytest.mark.parametrize("num_mappings", NUM_MAPPINGS)
 @pytest.mark.parametrize("num_heads", NUM_HEADS)
diff --git a/vllm/_custom_ops.py b/vllm/_custom_ops.py
index 4c577c1c4..92dc1d8e9 100644
--- a/vllm/_custom_ops.py
+++ b/vllm/_custom_ops.py
@@ -1361,6 +1361,50 @@ def reshape_and_cache_flash(
                                                    v_scale)
 
 
+def reshape_and_cache_multi_layer(
+    offload_kv_cache_blocks: list[torch.Tensor],
+    kv_caches: list[torch.Tensor],
+    slot_mapping: torch.Tensor,
+    block_size: int,
+    kv_cache_dtype: str,
+    k_scales: list[torch.Tensor],
+    v_scales: list[torch.Tensor],
+    layout: str,
+) -> None:
+    torch.ops._C_cache_ops.reshape_and_cache_multi_layer(
+        offload_kv_cache_blocks,
+        kv_caches,
+        slot_mapping,
+        block_size,
+        kv_cache_dtype,
+        k_scales,
+        v_scales,
+        layout,
+    )
+
+
+def reshape_and_offload_multi_layer(
+    offload_kv_cache_blocks: list[torch.Tensor],
+    kv_caches: list[torch.Tensor],
+    slot_mapping: torch.Tensor,
+    block_size: int,
+    kv_cache_dtype: str,
+    k_scales: list[torch.Tensor],
+    v_scales: list[torch.Tensor],
+    layout: str,
+) -> None:
+    torch.ops._C_cache_ops.reshape_and_offload_multi_layer(
+        offload_kv_cache_blocks,
+        kv_caches,
+        slot_mapping,
+        block_size,
+        kv_cache_dtype,
+        k_scales,
+        v_scales,
+        layout,
+    )
+
+
 def concat_and_cache_mla(
     kv_c: torch.Tensor,
     k_pe: torch.Tensor,
diff --git a/vllm/distributed/kv_transfer/kv_connector/aibrix_offloading_connector.py b/vllm/distributed/kv_transfer/kv_connector/aibrix_offloading_connector.py
new file mode 100644
index 000000000..6301f3a95
--- /dev/null
+++ b/vllm/distributed/kv_transfer/kv_connector/aibrix_offloading_connector.py
@@ -0,0 +1,1123 @@
+# SPDX-License-Identifier: Apache-2.0
+import copy
+import dataclasses
+import enum
+import itertools
+import logging
+import time
+from typing import TYPE_CHECKING, Dict, List, Optional, Tuple, Union
+
+import torch
+from aibrix_kvcache import (BaseKVCacheManager, GroupAwareKVCacheManager,
+                            KVCacheBlockLayout, KVCacheBlockSpec,
+                            KVCacheConfig, KVCacheMetrics, KVCacheTensorSpec)
+from aibrix_kvcache.common.absl_logging import (getLogger, log_every_n_seconds,
+                                                log_if)
+from aibrix_kvcache.metrics import (MS_BUCKETS, TOKEN_BUCKETS,
+                                    BaseMetricsExporter,
+                                    KVCacheMetricsExporter, Metrics)
+from aibrix_kvcache.utils import perf_timer
+
+from vllm._custom_ops import (reshape_and_cache_multi_layer,
+                              reshape_and_offload_multi_layer)
+from vllm.attention import get_attn_backend
+from vllm.attention.backends.flash_attn import FlashAttentionBackend
+# from vllm.attention.backends.flashinfer import FlashInferBackend
+from vllm.attention.backends.xformers import XFormersBackend
+from vllm.distributed import broadcast_tensor_dict, get_tp_group
+from vllm.distributed.kv_transfer.kv_connector.base import KVConnectorBase
+from vllm.distributed.kv_transfer.kv_transfer_metrics import (
+    KVTransferMetrics, KVTransferMetricsExporter)
+from vllm.model_executor import SamplingMetadata
+from vllm.utils import get_kv_cache_torch_dtype, round_down
+
+if TYPE_CHECKING:
+    from vllm.config import VllmConfig
+    from vllm.sequence import IntermediateTensors
+    from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata
+
+logger = getLogger(__name__)
+
+OFFLOADING_CONNECTOR_SKIP_THRESHOLD = 8
+OFFLOADING_CONNECTOR_SUPPORTED_ATTN_BACKENDS = {
+    FlashAttentionBackend.get_name():
+    KVCacheBlockLayout.LCND,
+    # TODO: support FlashInferBackend.
+    # FlashInferBackend.get_name(): KVCacheBlockLayout.LCND,
+    XFormersBackend.get_name():
+    KVCacheBlockLayout.LCND,
+}
+
+
+class AIBrixOffloadingConnectorComputeMetrics(Metrics):
+    """Compute metrics."""
+
+    num_tokens: List[int] = []
+    op_lat_ms: Optional[List[int]] = None
+
+    def __init__(
+        self,
+        enable_time_measurement: bool = True,
+    ) -> None:
+        self._enable_time_measurement = enable_time_measurement
+        self._init_optionals()
+
+    def _init_optionals(self) -> None:
+        if self._enable_time_measurement:
+            self.op_lat_ms = []
+
+    def add(
+        self,
+        num_tokens: int,
+        lat_ms: int,
+    ) -> None:
+        self.num_tokens.append(num_tokens)
+        if self._enable_time_measurement:
+            self.op_lat_ms.append(lat_ms)
+
+    def reset(self) -> None:
+        self.num_tokens = []
+        self._init_optionals()
+
+    def summary(self) -> str:
+        iter_len = len(self.num_tokens)
+        total_tokens = sum(self.num_tokens)
+        avg_tokens = total_tokens / iter_len if iter_len > 0 else 0
+        summary = f"COMPUTE: Num. of tokens (iter): " \
+                  f"total={total_tokens}, avg={avg_tokens:.2f}"
+        if self._enable_time_measurement:
+            total_lat_ms = sum(self.op_lat_ms)
+            avg_lat_ms = total_lat_ms / iter_len if iter_len > 0 else 0
+            summary += f", Latency (iter, ms): total={total_lat_ms:.2f}, " \
+                       f"avg={avg_lat_ms:.2f}"
+        return summary
+
+
+class AIBrixOffloadingConnectorOpMetrics(Metrics):
+    """Op metrics."""
+
+    class OP(enum.Enum):
+        SEND = enum.auto()
+        RECV = enum.auto()
+
+    num_ops: int = 0
+    total_tokens: int = 0
+    total_sent_or_recved_tokens: int = 0
+    num_prefixes: List[int] = []
+    num_tokens: List[int] = []
+    num_sent_or_recved_tokens: List[int] = []
+    op_lat_ms: Optional[List[int]] = None
+    # tracks the latency of rebuilding model_input
+    rebuild_lat_ms: Optional[List[int]] = None
+
+    def __init__(
+        self,
+        op: OP,
+        enable_time_measurement: bool = True,
+    ) -> None:
+        self._op = op
+        self._enable_time_measurement = enable_time_measurement
+        self._init_optionals()
+
+    def _init_optionals(self) -> None:
+        if self._enable_time_measurement:
+            self.op_lat_ms = []
+            self.rebuild_lat_ms = []
+
+    def add(
+        self,
+        num_prefix: int,
+        num_tokens: int,
+        num_sent_or_recved_tokens: int,
+        lat_ms: int,
+    ) -> None:
+        self.num_ops += 1
+        self.total_tokens += num_tokens
+        self.total_sent_or_recved_tokens += num_sent_or_recved_tokens
+        self.num_sent_or_recved_tokens.append(num_sent_or_recved_tokens)
+        self.num_prefixes.append(num_prefix)
+        self.num_tokens.append(num_tokens)
+        if self._enable_time_measurement:
+            self.op_lat_ms.append(lat_ms)
+
+    def record_rebuild_latency(self, lat_ms: int) -> None:
+        if self._enable_time_measurement:
+            self.rebuild_lat_ms.append(lat_ms)
+
+    def reset(self) -> None:
+        self.num_prefixes = []
+        self.num_tokens = []
+        self.num_sent_or_recved_tokens = []
+        self._init_optionals()
+
+    def summary(self) -> str:
+        iter_len = len(self.num_prefixes)
+        total_prefixes = sum(self.num_prefixes)
+        avg_prefixes = total_prefixes / iter_len if iter_len > 0 else 0
+        total_tokens = sum(self.num_tokens)
+        avg_tokens = total_tokens / iter_len if iter_len > 0 else 0
+        total_sent_or_recved_tokens = sum(self.num_sent_or_recved_tokens)
+        avg_sent_or_recved_tokens = (total_sent_or_recved_tokens /
+                                     iter_len if iter_len > 0 else 0)
+        summary = f"{self._op.name}: Num. of ops: {self.num_ops}, " \
+                  f"Total num. of tokens: {self.total_tokens}, "
+        if self._op is AIBrixOffloadingConnectorOpMetrics.OP.SEND:
+            summary += f"Total num. of sent tokens: " \
+                       f"{self.total_sent_or_recved_tokens}, "
+        else:
+            summary += f"Total num. of received tokens: " \
+                       f"{self.total_sent_or_recved_tokens}, "
+        summary += f"Num. of prefixes (iter): total={total_prefixes}, " \
+                   f"avg={avg_prefixes:.2f}, " \
+                   f"Num. of tokens (iter): total={total_tokens}, " \
+                   f"avg={avg_tokens:.2f}"
+        if self._op is AIBrixOffloadingConnectorOpMetrics.OP.SEND:
+            summary += f", Num. of sent tokens (iter): " \
+                       f"total={total_sent_or_recved_tokens}, " \
+                       f"avg={avg_sent_or_recved_tokens:.2f}"
+        else:
+            summary += f", Num. of received tokens (iter): " \
+                       f"total={total_sent_or_recved_tokens}, " \
+                       f"avg={avg_sent_or_recved_tokens:.2f}"
+        if self._enable_time_measurement:
+            total_lat_ms = sum(self.op_lat_ms)
+            avg_lat_ms = total_lat_ms / iter_len if iter_len > 0 else 0
+            summary += f", Latency (iter, ms): total={total_lat_ms:.2f}, " \
+                       f"avg={avg_lat_ms:.2f}"
+            if len(self.rebuild_lat_ms) > 0:
+                iter_len = len(self.rebuild_lat_ms)
+                total_rebuild_lat_ms = sum(self.rebuild_lat_ms)
+                avg_rebuild_lat_ms = total_rebuild_lat_ms / iter_len \
+                    if iter_len > 0 else 0
+                summary += f", model_input rebuild latency (iter, ms): " \
+                           f"total={total_rebuild_lat_ms:.2f}, " \
+                           f"avg={avg_rebuild_lat_ms:.2f}"
+        if self._op is AIBrixOffloadingConnectorOpMetrics.OP.RECV:
+            hit_rate = (self.total_sent_or_recved_tokens * 100 /
+                        self.total_tokens) if self.total_tokens > 0 else 0
+            summary += f", Hit rate: {hit_rate:.2f}%"
+        return summary
+
+
+class AIBrixOffloadingConnectorOpMetricsExporter(BaseMetricsExporter):
+    OP_TYPE_LABELNAME = "op_type"
+
+    def __init__(self, *, prefix, labelnames, counter_cls, gauge_cls,
+                 histogram_cls) -> None:
+        labelnames = labelnames or []
+        labelnames.append(self.OP_TYPE_LABELNAME)
+
+        super().__init__(
+            prefix=f"{prefix}ol_connector_",
+            labelnames=labelnames,
+            counter_cls=counter_cls,
+            gauge_cls=gauge_cls,
+            histogram_cls=histogram_cls,
+        )
+
+        self._init_exporter_fields()
+
+    def _init_exporter_fields(self) -> None:
+        self.counter_num_ops = self._counter_cls(
+            name=f"{self._prefix}num_ops",
+            documentation="Cumulative number of operations.",
+            labelnames=self._labelnames)
+        self.histogram_iteration_prefixes = self._histogram_cls(
+            name=f"{self._prefix}iteration_prefixes",
+            documentation="Histogram of number of prefixes per iteration.",
+            labelnames=self._labelnames,
+            buckets=TOKEN_BUCKETS)
+        self.histogram_iteration_tokens = self._histogram_cls(
+            name=f"{self._prefix}iteration_tokens",
+            documentation="Histogram of number of tokens per iteration.",
+            labelnames=self._labelnames,
+            buckets=TOKEN_BUCKETS)
+        self.histogram_iteration_sent_tokens = self._histogram_cls(
+            name=f"{self._prefix}iteration_sent_tokens",
+            documentation=("Histogram of number of sent tokens "
+                           "per iteration."),
+            labelnames=self._labelnames,
+            buckets=TOKEN_BUCKETS)
+        self.histogram_iteration_received_tokens = self._histogram_cls(
+            name=f"{self._prefix}iteration_received_tokens",
+            documentation=("Histogram of number of received tokens "
+                           "per iteration."),
+            labelnames=self._labelnames,
+            buckets=TOKEN_BUCKETS)
+        self.histogram_iteration_op_lat_ms = self._histogram_cls(
+            name=f"{self._prefix}iteration_op_lat_ms",
+            documentation=("Histogram of operation latencies "
+                           "per iteration in ms."),
+            labelnames=self._labelnames,
+            buckets=MS_BUCKETS)
+        self.histogram_iteration_rebuild_lat_ms = self._histogram_cls(
+            name=f"{self._prefix}iteration_rebuild_lat_ms",
+            documentation=("Histogram of rebuilding model_input latencies "
+                           "per iteration in ms."),
+            labelnames=self._labelnames,
+            buckets=MS_BUCKETS)
+
+    def export(
+        self,
+        labels: Dict[str, str],
+        metrics: AIBrixOffloadingConnectorOpMetrics
+        | AIBrixOffloadingConnectorComputeMetrics,
+    ) -> None:
+        labels = labels.copy()
+
+        if isinstance(metrics, AIBrixOffloadingConnectorOpMetrics):
+            labels[self.OP_TYPE_LABELNAME] = metrics._op.name.lower()
+            self._export_op_metrics(labels, metrics)
+        else:
+            labels[self.OP_TYPE_LABELNAME] = "compute"
+            self._export_compute_metrics(labels, metrics)
+
+    def _export_op_metrics(
+        self,
+        labels: Dict[str, str],
+        metrics: AIBrixOffloadingConnectorOpMetrics,
+    ) -> None:
+        self._export_counter(self.counter_num_ops, labels,
+                             len(metrics.num_prefixes))
+        self._export_histogram(self.histogram_iteration_prefixes, labels,
+                               metrics.num_prefixes)
+        self._export_histogram(self.histogram_iteration_tokens, labels,
+                               metrics.num_tokens)
+        if metrics._op is AIBrixOffloadingConnectorOpMetrics.OP.SEND:
+            self._export_histogram(self.histogram_iteration_sent_tokens,
+                                   labels, metrics.num_sent_or_recved_tokens)
+        else:
+            self._export_histogram(self.histogram_iteration_received_tokens,
+                                   labels, metrics.num_sent_or_recved_tokens)
+        if metrics._enable_time_measurement:
+            self._export_histogram(self.histogram_iteration_op_lat_ms, labels,
+                                   metrics.op_lat_ms)
+            self._export_histogram(self.histogram_iteration_rebuild_lat_ms,
+                                   labels, metrics.rebuild_lat_ms)
+
+    def _export_compute_metrics(
+        self,
+        labels: Dict[str, str],
+        metrics: AIBrixOffloadingConnectorComputeMetrics,
+    ) -> None:
+        self._export_histogram(self.histogram_iteration_tokens, labels,
+                               metrics.num_tokens)
+        if metrics._enable_time_measurement:
+            self._export_histogram(self.histogram_iteration_op_lat_ms, labels,
+                                   metrics.op_lat_ms)
+
+
+class AIBrixOffloadingConnectorMetrics(KVTransferMetrics):
+
+    def __init__(self, metrics: KVCacheMetrics) -> None:
+        self._cache_metrics = metrics
+        self._time_measurement_enabled = (
+            self._cache_metrics.time_measurement_enabled)
+        self._compute_metrics = AIBrixOffloadingConnectorComputeMetrics(
+            enable_time_measurement=self._time_measurement_enabled)
+        self._send_metrics = AIBrixOffloadingConnectorOpMetrics(
+            AIBrixOffloadingConnectorOpMetrics.OP.SEND,
+            enable_time_measurement=self._time_measurement_enabled,
+        )
+        self._recv_metrics = AIBrixOffloadingConnectorOpMetrics(
+            AIBrixOffloadingConnectorOpMetrics.OP.RECV,
+            enable_time_measurement=self._time_measurement_enabled,
+        )
+
+    @property
+    def time_measurement_enabled(self) -> bool:
+        return self._time_measurement_enabled
+
+    def reset(self) -> None:
+        self._cache_metrics.reset()
+        self._compute_metrics.reset()
+        self._send_metrics.reset()
+        self._recv_metrics.reset()
+
+    def __str__(self) -> str:
+        return f"AIBrixOffloadingConnector metrics: " \
+               f"{self._compute_metrics.summary()}" \
+               f"\n\t{self._send_metrics.summary()}" \
+               f"\n\t{self._recv_metrics.summary()}" \
+               f"\n\t{self._cache_metrics.summary()}"
+
+
+class AIBrixOffloadingConnectorMetricsExporter(KVTransferMetricsExporter):
+    """Metrics for AIBrixOffloadingConnector."""
+
+    def __init__(self, *, prefix, labelnames, gauge_cls, counter_cls,
+                 histogram_cls):
+        self.kv_cache_metrics_exporter = KVCacheMetricsExporter(
+            prefix=prefix,
+            labelnames=labelnames,
+            gauge_cls=gauge_cls,
+            counter_cls=counter_cls,
+            histogram_cls=histogram_cls,
+        )
+        self.ol_connector_op_metrics_exporter = \
+            AIBrixOffloadingConnectorOpMetricsExporter(
+            prefix=prefix,
+            labelnames=labelnames,
+            gauge_cls=gauge_cls,
+            counter_cls=counter_cls,
+            histogram_cls=histogram_cls,
+        )
+
+    def export(
+        self,
+        *,
+        metrics: KVTransferMetrics,
+        labels: Dict[str, str],
+    ):
+        self.kv_cache_metrics_exporter.export(
+            metrics=metrics._cache_metrics,
+            labels=labels,
+        )
+        self.ol_connector_op_metrics_exporter.export(
+            metrics=metrics._compute_metrics,
+            labels=labels,
+        )
+        self.ol_connector_op_metrics_exporter.export(
+            metrics=metrics._send_metrics,
+            labels=labels,
+        )
+        self.ol_connector_op_metrics_exporter.export(
+            metrics=metrics._recv_metrics,
+            labels=labels,
+        )
+
+
+@dataclasses.dataclass
+class AIBrixOffloadingConnectorCachedMeta:
+    context_tokens: List[int] = dataclasses.field(default_factory=list)
+    context_tokens_offset: int = 0
+
+    # When chunked prefill is enabled, the query length in each iteration may
+    # not be divisible by block_size. Consider the following example, assuming
+    # recv always returns nothing for simplicity. Suppose query_len = 511:
+    #
+    # - In the first iteration, since no tokens are received, we need to send
+    #   all 511 tokens. However, in AIBrixOffloadingConnector, the length is
+    #   aligned to 496, and the remaining 15 tokens are ignored.
+    # - In the second iteration, query_len is likely 511 again, while
+    #   context_len is 511. To prevent a gap in the storage layer caused by
+    #   skipping 15 tokens in the first iteration, AIBrixOffloadingConnector
+    #   shifts context_len to 496 and attempts to send (15 + 511) tokens.
+    #
+    # To handle this correctly, we need to determine the slot mapping for those
+    # 15 tokens. The actual number varies from 1 to 15 but is always less than
+    # block_size. Therefore, we store the slot mapping of the last block in the
+    # metadata.
+    last_block_slot_mapping: Optional[torch.Tensor] = None
+
+    def __init__(self, seq_len: int) -> None:
+        self.context_tokens = [-1] * seq_len
+
+    def get_context_tokens(self) -> List[int]:
+        return self.context_tokens[:self.context_tokens_offset]
+
+    def extend_context_tokens(self, tokens: List[int]) -> None:
+        offset = self.context_tokens_offset
+        length = len(tokens)
+        self.context_tokens[offset:offset + length] = tokens
+        self.context_tokens_offset += length
+
+    def clear_context_tokens(self) -> None:
+        self.context_tokens.clear()
+        self.context_tokens_offset = 0
+
+
+class AIBrixOffloadingConnector(KVConnectorBase):
+    """AIBrixOffloadingConnector is a KVConnector that offloads KV caches
+    and hidden states to the kv cache offloading service.
+    """
+
+    def __init__(
+        self,
+        rank: int,
+        local_rank: int,
+        config: "VllmConfig",
+    ):
+        cache_config = config.cache_config
+        model_config = config.model_config
+        parallel_config = config.parallel_config
+
+        tp_size = parallel_config.tensor_parallel_size
+        num_kv_heads = model_config.get_num_kv_heads(parallel_config)
+        hidden_size = model_config.get_hidden_size()
+        num_attention_heads = model_config.get_num_attention_heads(
+            parallel_config) * tp_size
+        head_size = int(hidden_size / num_attention_heads)
+
+        kv_head_ids = list(
+            range(num_kv_heads * rank, num_kv_heads * (rank + 1)))
+        layer_ids = list(
+            range(*model_config.get_layers_start_end_indices(parallel_config)))
+        num_layers = len(layer_ids)
+
+        block_ntokens = cache_config.block_size
+        block_dtype = get_kv_cache_torch_dtype(cache_config.cache_dtype,
+                                               model_config.dtype)
+
+        kv_cache_dtype = cache_config.cache_dtype
+
+        self.attn_backend = get_attn_backend(
+            model_config.get_head_size(),
+            model_config.dtype,
+            kv_cache_dtype,
+            block_ntokens,
+            model_config.is_attention_free,
+            use_mla=model_config.use_mla,
+        )
+
+        block_spec = KVCacheBlockSpec(
+            block_ntokens=block_ntokens,
+            block_dtype=block_dtype,
+            block_layout=self._get_block_layout(),
+            tensor_spec=KVCacheTensorSpec(
+                heads=kv_head_ids,
+                layers=layer_ids,
+                head_size=head_size,
+            ),
+        )
+
+        config = KVCacheConfig(block_spec=block_spec)
+
+        if parallel_config.tensor_parallel_size == 1:
+            self.cache = BaseKVCacheManager(config=config)
+        else:
+            pg = get_tp_group().cpu_group
+            self.cache = GroupAwareKVCacheManager(config=config,
+                                                  process_group=pg)
+
+        self.rank = rank
+        self.head_size = head_size
+        self.tp_size = tp_size
+        self.num_kv_heads = num_kv_heads
+        self.num_layers = num_layers
+        self.kv_head_ids = kv_head_ids
+        self.layer_ids = layer_ids
+        self.block_ntokens = block_ntokens
+        self.block_dtype = block_dtype
+        self.block_shape = block_spec.block_shape
+        self.block_spec = block_spec
+        self.block_layout = block_spec.block_layout
+        self.chunk_size = self.cache.chunk_size
+        self.cache_feature = self.cache.feature
+        self.kv_cache_dtype = kv_cache_dtype
+
+        self._connector_cache: Dict[str,
+                                    AIBrixOffloadingConnectorCachedMeta] = {}
+        self._metrics = AIBrixOffloadingConnectorMetrics(self.cache.metrics)
+        # meta to track compute perf
+        self._compute_start_event = torch.cuda.Event(enable_timing=True)
+        self._compute_end_event = torch.cuda.Event(enable_timing=True)
+        self._compute_total_tokens = 0
+
+    @property
+    def metrics(self) -> KVTransferMetrics:
+        return self._metrics
+
+    def get_metrics_exporter_cls(self):
+        return AIBrixOffloadingConnectorMetricsExporter
+
+    def close(self) -> None:
+        if self.cache:
+            self.cache.close()
+            self.cache = None
+
+    def _get_block_layout(self) -> KVCacheBlockLayout:
+
+        if self.attn_backend.get_name() in \
+            OFFLOADING_CONNECTOR_SUPPORTED_ATTN_BACKENDS:
+            return KVCacheBlockLayout(
+                OFFLOADING_CONNECTOR_SUPPORTED_ATTN_BACKENDS[
+                    self.attn_backend.get_name()])
+        raise NotImplementedError(
+            f"Only support attn backends in "
+            f"{list(OFFLOADING_CONNECTOR_SUPPORTED_ATTN_BACKENDS.keys())}. "
+            f"{self.attn_backend.get_name()} is used.")
+
+    def _prepare_request_cache(
+        self,
+        model_input: "ModelInputForGPUWithSamplingMetadata",
+    ) -> None:
+        # remove finished requests
+        for req_id in model_input.finished_requests_ids:
+            if req_id in self._connector_cache:
+                self._connector_cache.pop(req_id)
+
+        # remove decode requests
+        seq_lens = model_input.seq_lens
+        num_prefills = model_input.attn_metadata.num_prefills
+        request_ids = list(model_input.request_ids_to_seq_ids.keys())
+        for seq_idx, _ in enumerate(seq_lens):
+            if seq_idx >= num_prefills:
+                seq_request_id = request_ids[seq_idx]
+                if seq_request_id in self._connector_cache:
+                    self._connector_cache.pop(seq_request_id)
+
+        # add new requests
+        for req_id in model_input.request_ids_to_seq_ids:
+            if req_id not in self._connector_cache:
+                self._connector_cache[req_id] = None
+
+    def _update_request_cache(
+        self,
+        seq_request_id: str,
+        seq_len: int,
+        seq_input_tokens: List[int],
+        seq_slot_mapping: torch.Tensor,
+        kv_transfer_context_tokens: List[int],
+    ) -> None:
+        if self._connector_cache[seq_request_id] is None:
+            self._connector_cache[
+                seq_request_id] = AIBrixOffloadingConnectorCachedMeta(seq_len)
+        seq_request_cache = self._connector_cache[seq_request_id]
+        kv_transfer_context_tokens = kv_transfer_context_tokens or []
+        if seq_len == len(kv_transfer_context_tokens):
+            # when using prefix caching, if all tokens are cached,
+            # we will leave a token in `seq_input_tokens` to avoid
+            # erroneous behavior. In this case, we need to ignore
+            # the token in `seq_input_tokens`.
+            # See `_compute_for_prefix_cache_hit` for more details.
+            seq_input_tokens = []
+        if seq_len != len(seq_request_cache.get_context_tokens()) + len(
+                kv_transfer_context_tokens) + len(seq_input_tokens):
+            # this is a preempted request to be recomputed, let's drop
+            # the context tokens
+            seq_request_cache.clear_context_tokens()
+        if len(kv_transfer_context_tokens) > 0:
+            assert seq_request_cache.context_tokens_offset == 0
+            seq_request_cache.extend_context_tokens(kv_transfer_context_tokens)
+        seq_request_cache.extend_context_tokens(seq_input_tokens)
+        last_block_len = min(self.block_ntokens, seq_slot_mapping.shape[0])
+        seq_request_cache.last_block_slot_mapping = seq_slot_mapping[
+            -last_block_len:]
+
+    def _get_chunk_slot_mapping(
+        self,
+        seq_request_id: str,
+        seq_slot_mapping: torch.Tensor,
+        offset: int,
+        length: int,
+    ) -> torch.Tensor:
+        if offset < 0:
+            chunk_slot_mapping = seq_slot_mapping[:offset + length]
+            # attach more to slot mapping
+            seq_last_block_slot_mapping = self._connector_cache[
+                seq_request_id].last_block_slot_mapping
+            assert seq_last_block_slot_mapping is not None
+            assert -offset <= seq_last_block_slot_mapping.shape[0]
+            prepend_slot_mapping = seq_last_block_slot_mapping[offset:]
+            chunk_slot_mapping = torch.cat(
+                (prepend_slot_mapping, chunk_slot_mapping))
+        else:
+            chunk_slot_mapping = seq_slot_mapping[offset:offset + length]
+
+        assert chunk_slot_mapping.shape[0] == length
+        return chunk_slot_mapping
+
+    def send_kv_caches_and_hidden_states(
+        self,
+        model_executable: torch.nn.Module,
+        model_input: "ModelInputForGPUWithSamplingMetadata",
+        kv_caches: List[torch.Tensor],
+        hidden_or_intermediate_states: Union[torch.Tensor,
+                                             "IntermediateTensors"],
+    ) -> None:
+        attn_metadata = model_input.attn_metadata
+        kv_transfer_metadata = model_input.kv_transfer_metadata
+
+        if kv_transfer_metadata is None:
+            return
+
+        assert attn_metadata is not None
+
+        self._prepare_request_cache(model_input)
+
+        # only send prompt KV caches
+        if attn_metadata.num_prefills <= 0:
+            return
+
+        num_prefills = attn_metadata.num_prefills
+        seq_lens = model_input.seq_lens[:num_prefills]
+        query_lens = model_input.query_lens[:num_prefills]
+        slot_mapping = model_input.attn_metadata.slot_mapping.flatten()
+        start_layer = model_executable.model.start_layer
+        end_layer = model_executable.model.end_layer
+        request_ids = list(model_input.request_ids_to_seq_ids.keys())
+
+        if self._metrics.time_measurement_enabled:
+            self._compute_end_event.record()
+            self._compute_end_event.synchronize()
+            compute_lat_ms = self._compute_start_event.elapsed_time(
+                self._compute_end_event)
+            self._metrics._compute_metrics.add(self._compute_total,
+                                               compute_lat_ms)
+
+        # query_lens contains new KV caches that need to be offloaded
+        for seq_idx, query_len in enumerate(query_lens):
+            start_pos = sum(query_lens[:seq_idx])
+            end_pos = start_pos + query_len
+            seq_request_id = request_ids[seq_idx]
+            seq_context_len = seq_lens[seq_idx] - query_len
+            seq_slot_mapping = slot_mapping[start_pos:end_pos]
+            seq_cached_meta = self._connector_cache[seq_request_id]
+            assert seq_cached_meta is not None
+            seq_all_tokens = seq_cached_meta.get_context_tokens()
+            assert seq_all_tokens is not None
+            assert len(seq_all_tokens) == seq_lens[
+                seq_idx], f"{len(seq_all_tokens)}!={seq_lens[seq_idx]}"
+            prompt_len = kv_transfer_metadata.seq_groups[seq_idx].prompt_len
+
+            # align to block boundary
+            aligned_context_len = round_down(seq_context_len,
+                                             self.block_ntokens)
+            actual_query_len = seq_context_len + query_len - aligned_context_len
+            aligned_query_len = round_down(actual_query_len,
+                                           self.block_ntokens)
+
+            # skip if there are not enough tokens to send after alignment
+            if prompt_len == seq_lens[seq_idx]:
+                # If chunked prefill is not enabled or this is the last
+                # chunk, we use a larger skip threshold
+                skip_threshold = OFFLOADING_CONNECTOR_SKIP_THRESHOLD
+            else:
+                # This is an intermediate chunk, only skip if this is not
+                # a full block
+                skip_threshold = 1
+            if aligned_query_len <= skip_threshold * self.block_ntokens:
+                continue
+
+            assert len(
+                seq_all_tokens) >= aligned_context_len + aligned_query_len
+
+            prefix = seq_all_tokens[:aligned_context_len]
+            tokens = seq_all_tokens[aligned_context_len:aligned_context_len +
+                                    aligned_query_len]
+
+            if self._metrics.time_measurement_enabled:
+                start = torch.cuda.Event(enable_timing=True)
+                end = torch.cuda.Event(enable_timing=True)
+                start.record()
+
+            total_sent = 0
+            for (
+                    chunk_prefix,
+                    chunk_tokens,
+                    _,
+                    all,
+            ) in self.cache.cache_chunk_keys(prefix, tokens):
+                # |----------- seq_context_len ---------|- offset -|
+                # |- aligned_context_len -|- shift_len -|
+                #                         |--- n * chunk_size -----|
+                # |----------------- chunk_prefix -----------------|- tokens -|
+                #                         |-------- (n + 1) * chunk_size -----|
+                chunk_size = len(chunk_tokens)
+                offset = len(chunk_prefix) - seq_context_len
+                length = chunk_size
+
+                exists_status = self.cache.exists(chunk_prefix, chunk_tokens)
+                if exists_status.is_ok():
+                    num_existing_tokens = exists_status.value
+                    logger.info(
+                        "Request[id=%s] send(%d) encounters %d existing tokens",
+                        seq_request_id, length, num_existing_tokens)
+                    if chunk_size - num_existing_tokens < self.block_ntokens:
+                        continue
+                    else:
+                        # partially exists
+                        offset += num_existing_tokens
+                        length -= num_existing_tokens
+                        new_chunk_prefix_len = (len(chunk_prefix) +
+                                                num_existing_tokens)
+                        chunk_prefix = all[:new_chunk_prefix_len]
+                        chunk_tokens = all[
+                            new_chunk_prefix_len:new_chunk_prefix_len + length]
+
+                # allocate space for KV caches
+                status = self.cache.allocate(length // self.block_ntokens)
+                if not status.is_ok():
+                    log_every_n_seconds(logger, logging.ERROR,
+                                        "Failed to allocate : %s", 3,
+                                        str(status))
+                    break
+                handle = status.value
+                tensors = handle.to_tensors()
+                length = len(tensors) * self.block_ntokens
+
+                chunk_slot_mapping = self._get_chunk_slot_mapping(
+                    seq_request_id, seq_slot_mapping, offset, length)
+
+                layers = model_executable.model.layers[start_layer:end_layer]
+                k_scales = [layer.self_attn.attn._k_scale for layer in layers]
+                v_scales = [layer.self_attn.attn._v_scale for layer in layers]
+
+                with perf_timer() as get_kernel_offload_dur_ms:
+                    reshape_and_offload_multi_layer(
+                        tensors,
+                        kv_caches[start_layer:end_layer],
+                        chunk_slot_mapping,
+                        self.block_ntokens,
+                        self.kv_cache_dtype,
+                        k_scales,
+                        v_scales,
+                        self.block_layout.name,
+                    )
+
+                logger.info("Request[id=%s] offloads %d tokens in %.4f ms",
+                            seq_request_id, length,
+                            get_kernel_offload_dur_ms())
+
+                # put KV caches to offloading service
+                status = self.cache.put(chunk_prefix, chunk_tokens[:length],
+                                        handle)
+                if not status.is_ok():
+                    # TODO: notify other ranks in the group to stop sending
+                    # if this is a fatal error
+                    log_every_n_seconds(
+                        logger, logging.ERROR,
+                        "Failed to put to offloading service: %s", 3,
+                        str(status))
+                    break
+                else:
+                    total_sent += length
+
+            log_if(
+                logger,
+                logging.INFO,
+                "Request[id=%s, prompt_len=%d, context_len=%d] sent %d tokens",
+                total_sent > 0,
+                seq_request_id,
+                prompt_len,
+                seq_context_len,
+                total_sent,
+            )
+
+            if self._metrics.time_measurement_enabled:
+                end.record()
+                end.synchronize()
+                lat_ms = start.elapsed_time(end)
+                self._metrics._send_metrics.add(aligned_context_len,
+                                                aligned_query_len, total_sent,
+                                                lat_ms)
+
+    def recv_kv_caches_and_hidden_states(
+        self,
+        model_executable: torch.nn.Module,
+        model_input: "ModelInputForGPUWithSamplingMetadata",
+        kv_caches: List[torch.Tensor],
+    ) -> Tuple[
+            Union[torch.Tensor, "IntermediateTensors"],
+            bool,
+            "ModelInputForGPUWithSamplingMetadata",
+    ]:
+        attn_metadata = model_input.attn_metadata
+        kv_transfer_metadata = model_input.kv_transfer_metadata
+
+        hidden_or_intermediate_states = None
+
+        # TODO:
+        # When using KV cache offloading with chunk-prefill enabled, for
+        # the first `n` chunks (except the last chunk before the decode
+        # phase), we can bypass the model execution if there is a full
+        # hit in the KV cache.
+        bypass_model_exec = False
+
+        if kv_transfer_metadata is None:
+            return hidden_or_intermediate_states, bypass_model_exec, model_input
+
+        assert attn_metadata is not None
+
+        self._compute_total = 0
+        self._prepare_request_cache(model_input)
+
+        # only recv prompt KV caches
+        if attn_metadata.num_prefills <= 0:
+            return hidden_or_intermediate_states, bypass_model_exec, model_input
+
+        num_prefills = attn_metadata.num_prefills
+        input_tokens_tensor = model_input.input_tokens
+        seq_lens = model_input.seq_lens[:num_prefills]
+        query_lens = model_input.query_lens[:num_prefills]
+        slot_mapping = model_input.attn_metadata.slot_mapping.flatten()
+        start_layer = model_executable.model.start_layer
+        end_layer = model_executable.model.end_layer
+        request_ids = list(model_input.request_ids_to_seq_ids.keys())
+
+        if kv_transfer_metadata.seq_group_metadata_list is not None:
+            assert len(request_ids) == len(
+                kv_transfer_metadata.seq_group_metadata_list)
+
+        assert list(range(start_layer, end_layer)) == self.layer_ids, (
+            f"{list(range(start_layer, end_layer))} != {self.layer_ids}")
+
+        model_config = model_executable.model.config
+        num_kv_heads = int(model_config.num_key_value_heads / self.tp_size)
+        hidden_size = model_config.hidden_size
+        num_attention_heads = model_config.num_attention_heads
+        head_size = int(hidden_size / num_attention_heads)
+
+        assert num_kv_heads == self.num_kv_heads
+        assert head_size == self.head_size
+
+        reused_lens = []
+        rebuild_model_input = False
+        # query_lens contains new KV caches to be received
+        for seq_idx, query_len in enumerate(query_lens):
+            start_pos = sum(query_lens[:seq_idx])
+            end_pos = start_pos + query_len
+            seq_request_id = request_ids[seq_idx]
+            seq_context_len = seq_lens[seq_idx] - query_len
+            seq_input_tokens = (
+                input_tokens_tensor[start_pos:end_pos].cpu().tolist())
+            prompt_len = kv_transfer_metadata.seq_groups[seq_idx].prompt_len
+
+            seq_slot_mapping = slot_mapping[start_pos:end_pos]
+            # will update these lens later if needed
+            reused_lens.append(0)
+
+            # align to block boundary
+            aligned_context_len = round_down(seq_context_len,
+                                             self.block_ntokens)
+            actual_query_len = seq_context_len + query_len - aligned_context_len
+            aligned_query_len = round_down(actual_query_len,
+                                           self.block_ntokens)
+            shift_len = seq_context_len - aligned_context_len
+
+            self._update_request_cache(
+                seq_request_id,
+                seq_lens[seq_idx],
+                seq_input_tokens,
+                seq_slot_mapping,
+                kv_transfer_metadata.seq_groups[seq_idx].context_tokens,
+            )
+
+            # skip if there are not enough tokens to receive after alignment
+            if prompt_len == seq_lens[seq_idx]:
+                # If chunked prefill is not enabled or this is the last
+                # chunk, we use a larger skip threshold
+                skip_threshold = OFFLOADING_CONNECTOR_SKIP_THRESHOLD
+            else:
+                # This is an intermediate chunk, only skip if this is not
+                # a full block
+                skip_threshold = 1
+            if aligned_query_len <= skip_threshold * self.block_ntokens:
+                continue
+
+            seq_cached_meta = self._connector_cache[seq_request_id]
+            assert seq_cached_meta is not None
+            seq_all_tokens = seq_cached_meta.get_context_tokens()
+            assert seq_all_tokens is not None
+            assert len(seq_all_tokens) == seq_lens[
+                seq_idx], f"{len(seq_all_tokens)}!={seq_lens[seq_idx]}"
+
+            assert len(
+                seq_all_tokens) >= aligned_context_len + aligned_query_len
+
+            prefix = seq_all_tokens[:aligned_context_len]
+            tokens = seq_all_tokens[aligned_context_len:aligned_context_len +
+                                    aligned_query_len]
+
+            if self._metrics.time_measurement_enabled:
+                start = torch.cuda.Event(enable_timing=True)
+                end = torch.cuda.Event(enable_timing=True)
+                start.record()
+
+            seq_recv_len = 0
+            for (
+                    chunk_prefix,
+                    chunk_tokens,
+                    next_tokens,
+                    _,
+            ) in self.cache.cache_chunk_keys(prefix, tokens):
+                if next_tokens and len(next_tokens) > 0:
+                    # prefetch
+                    self.cache.prefetch(chunk_prefix + chunk_tokens,
+                                        next_tokens)
+
+                # get KV caches from offloading service
+                status = self.cache.acquire(chunk_prefix, chunk_tokens)
+
+                if not status.is_ok():
+                    if not status.is_not_found():
+                        log_every_n_seconds(
+                            logger, logging.ERROR,
+                            "Failed to get from offloading service: %s", 3,
+                            str(status))
+                    break
+
+                num_fetched_tokens, handle = status.value
+                kv_blocks = handle.to_tensors()
+
+                offset = len(chunk_prefix) - seq_context_len
+                length = num_fetched_tokens
+
+                chunk_slot_mapping = self._get_chunk_slot_mapping(
+                    seq_request_id, seq_slot_mapping, offset, length)
+
+                layers = model_executable.model.layers[start_layer:end_layer]
+                k_scales = [layer.self_attn.attn._k_scale for layer in layers]
+                v_scales = [layer.self_attn.attn._v_scale for layer in layers]
+
+                with perf_timer() as get_kernel_onload_dur_ms:
+                    reshape_and_cache_multi_layer(
+                        kv_blocks,
+                        kv_caches[start_layer:end_layer],
+                        chunk_slot_mapping,
+                        self.block_ntokens,
+                        self.kv_cache_dtype,
+                        k_scales,
+                        v_scales,
+                        self.block_layout.name,
+                    )
+
+                logger.info("Request[id=%s] onloads %d tokens in %.4f ms",
+                            seq_request_id, length, get_kernel_onload_dur_ms())
+
+                # update recv_len
+                seq_recv_len += num_fetched_tokens - shift_len
+                rebuild_model_input = True
+                # reset shift_len
+                shift_len = 0
+
+                # release handle
+                handle.release()
+
+                if num_fetched_tokens < len(chunk_tokens):
+                    # didn't receive all tokens for current chunk, break
+                    break
+
+            reused_lens[-1] = seq_recv_len
+            log_if(
+                logger,
+                logging.INFO,
+                ("Request[id=%s, prompt_len=%d, context_len=%d] "
+                 "reused %d tokens"),
+                seq_recv_len > 0,
+                seq_request_id,
+                prompt_len,
+                seq_context_len,
+                seq_recv_len,
+            )
+
+            if self._metrics.time_measurement_enabled:
+                end.record()
+                end.synchronize()
+                lat_ms = start.elapsed_time(end)
+                self._metrics._recv_metrics.add(aligned_context_len,
+                                                aligned_query_len,
+                                                seq_recv_len, lat_ms)
+
+        if rebuild_model_input:
+            if self._metrics.time_measurement_enabled:
+                start = time.perf_counter()
+            model_input = self._rebuild_model_input(model_input, reused_lens)
+            if self._metrics.time_measurement_enabled:
+                end = time.perf_counter()
+                lat_ms = (end - start) * 1000
+                self._metrics._recv_metrics.record_rebuild_latency(lat_ms)
+
+        if self._metrics.time_measurement_enabled:
+            self._compute_start_event.record()
+            self._compute_total = sum(query_lens) - sum(reused_lens)
+
+        return hidden_or_intermediate_states, bypass_model_exec, model_input
+
+    def _rebuild_model_input(
+        self,
+        model_input: "ModelInputForGPUWithSamplingMetadata",
+        reused_lens: List[int],
+    ) -> "ModelInputForGPUWithSamplingMetadata":
+        seq_group_metadata_list = (
+            model_input.kv_transfer_metadata.seq_group_metadata_list)
+        if seq_group_metadata_list:
+            new_model_input = self._driver_rebuild_model_input(
+                model_input, reused_lens)
+            if self.tp_size > 1:
+                broadcast_data = new_model_input.as_broadcastable_tensor_dict()
+                broadcast_tensor_dict(broadcast_data, src=0)
+        else:
+            # worker
+            runner = model_input.kv_transfer_metadata.runner
+            broadcast_data = broadcast_tensor_dict(src=0)
+            new_model_input = (
+                runner.make_model_input_from_broadcasted_tensor_dict(
+                    broadcast_data))
+
+        # replace fields
+        return dataclasses.replace(
+            model_input,
+            input_tokens=new_model_input.input_tokens,
+            input_positions=new_model_input.input_positions,
+            token_types=new_model_input.token_types,
+            attn_metadata=new_model_input.attn_metadata,
+            seq_lens=new_model_input.seq_lens,
+            query_lens=new_model_input.query_lens,
+            lora_mapping=new_model_input.lora_mapping,
+            lora_requests=new_model_input.lora_requests,
+            multi_modal_kwargs=new_model_input.multi_modal_kwargs,
+            prompt_adapter_mapping=new_model_input.prompt_adapter_mapping,
+            prompt_adapter_requests=new_model_input.prompt_adapter_requests,
+            sampling_metadata=new_model_input.sampling_metadata,
+        )
+
+    def _driver_rebuild_model_input(
+        self,
+        model_input: "ModelInputForGPUWithSamplingMetadata",
+        reused_lens: List[int],
+    ) -> "ModelInputForGPUWithSamplingMetadata":
+        seq_group_metadata_list = (
+            model_input.kv_transfer_metadata.seq_group_metadata_list)
+        runner = model_input.kv_transfer_metadata.runner
+        finished_requests_ids = model_input.finished_requests_ids
+
+        request_ids = list(model_input.request_ids_to_seq_ids.keys())
+        assert len(request_ids) == len(seq_group_metadata_list)
+
+        seq_lens = model_input.seq_lens
+        query_lens = model_input.query_lens
+
+        backup = [None] * len(seq_group_metadata_list)
+        for offset, seq_group_metadata in enumerate(seq_group_metadata_list):
+            if not seq_group_metadata.is_prompt:
+                break
+            if reused_lens[offset] == 0:
+                continue
+            # Prefill has only 1 sequence
+            backup[offset] = seq_group_metadata.computed_block_nums
+            context_len = seq_lens[offset] - query_lens[offset]
+            context_len += reused_lens[offset]
+            num_blocks = context_len // self.block_ntokens
+            seq_group_metadata.computed_block_nums = copy.deepcopy(
+                seq_group_metadata.computed_block_nums)
+            seq_group_metadata.computed_block_nums.extend(
+                itertools.repeat(0, num_blocks))
+
+        new_model_input = runner._prepare_model_input_tensors(
+            seq_group_metadata_list, finished_requests_ids)
+
+        if model_input.sampling_metadata is not None:
+            generators = runner.get_generators(finished_requests_ids)
+            sampling_metadata = SamplingMetadata.prepare(
+                seq_group_metadata_list,
+                new_model_input.seq_lens,
+                new_model_input.query_lens,
+                runner.device,
+                runner.pin_memory,
+                generators,
+                runner.sampling_metadata_cache,
+            )
+            new_model_input = dataclasses.replace(
+                new_model_input, sampling_metadata=sampling_metadata)
+
+        # revert changes on seq_group_metadata_list
+        for offset, seq_group_metadata in enumerate(seq_group_metadata_list):
+            if not seq_group_metadata.is_prompt:
+                break
+            if reused_lens[offset] == 0:
+                continue
+            seq_group_metadata.computed_block_nums = backup[offset]
+
+        return new_model_input
diff --git a/vllm/distributed/kv_transfer/kv_connector/base.py b/vllm/distributed/kv_transfer/kv_connector/base.py
index 0d1a3d40a..85a4c295d 100644
--- a/vllm/distributed/kv_transfer/kv_connector/base.py
+++ b/vllm/distributed/kv_transfer/kv_connector/base.py
@@ -19,6 +19,9 @@ if TYPE_CHECKING:
     from vllm.config import VllmConfig
     from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata
 
+    from ..kv_transfer_metrics import (KVTransferMetrics,
+                                       KVTransferMetricsExporter)
+
 
 class KVConnectorBase(ABC):
     """
@@ -50,6 +53,21 @@ class KVConnectorBase(ABC):
         """
         raise NotImplementedError
 
+    @property
+    def metrics(self) -> 'KVTransferMetrics':
+        """
+        Get the metrics object associated with the connector.
+        Returns:
+            KVTransferMetrics: The metrics object.
+        """
+        return None
+
+    def get_metrics_exporter_cls(self) -> 'KVTransferMetricsExporter':
+        """
+        Get the metrics exporter class associated with the connector.
+        """
+        return None
+
     @abstractmethod
     def send_kv_caches_and_hidden_states(
         self,
diff --git a/vllm/distributed/kv_transfer/kv_connector/factory.py b/vllm/distributed/kv_transfer/kv_connector/factory.py
index 6532c101a..de1b93be1 100644
--- a/vllm/distributed/kv_transfer/kv_connector/factory.py
+++ b/vllm/distributed/kv_transfer/kv_connector/factory.py
@@ -105,3 +105,8 @@ KVConnectorFactory.register_connector(
     "LMCacheConnectorV1",
     "vllm.distributed.kv_transfer.kv_connector.v1.lmcache_connector",
     "LMCacheConnectorV1")
+
+KVConnectorFactory.register_connector(
+    "AIBrixOffloadingConnector",
+    "vllm.distributed.kv_transfer.kv_connector.aibrix_offloading_connector",
+    "AIBrixOffloadingConnector")
diff --git a/vllm/distributed/kv_transfer/kv_connector_agent.py b/vllm/distributed/kv_transfer/kv_connector_agent.py
index 9d7145098..979ca1206 100644
--- a/vllm/distributed/kv_transfer/kv_connector_agent.py
+++ b/vllm/distributed/kv_transfer/kv_connector_agent.py
@@ -8,6 +8,8 @@ This implementation is a shim wrapper on two APIs exposed by `kv_connector`:
 from typing import TYPE_CHECKING, List, Tuple, Union
 
 if TYPE_CHECKING:
+    from .kv_transfer_metrics import (KVTransferMetrics,
+                                      KVTransferMetricsExporter)
     from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata
     from vllm.config import VllmConfig
 
@@ -49,6 +51,23 @@ class KVTransferAgent:
         self.connector = KVConnectorFactory.create_connector_v0(
             rank, local_rank, config)
 
+    @property
+    def metrics(self) -> "KVTransferMetrics":
+        """
+        Get the metrics object associated with the connector.
+        Returns:
+            KVTransferMetrics: The metrics object.
+        """
+        return self.connector.metrics
+
+    def get_metrics_exporter_cls(self) -> "KVTransferMetricsExporter":
+        """
+        Get the metrics object associated with the connector.
+        Returns:
+            KVTransferMetrics: The metrics object.
+        """
+        return self.connector.get_metrics_exporter_cls()
+
     def send_kv_caches_and_hidden_states(
         self,
         model_executable: torch.nn.Module,
diff --git a/vllm/distributed/kv_transfer/kv_transfer_metadata.py b/vllm/distributed/kv_transfer/kv_transfer_metadata.py
new file mode 100644
index 000000000..95c129633
--- /dev/null
+++ b/vllm/distributed/kv_transfer/kv_transfer_metadata.py
@@ -0,0 +1,119 @@
+# SPDX-License-Identifier: Apache-2.0
+
+from dataclasses import dataclass
+from typing import TYPE_CHECKING, List, Optional
+
+from vllm.sequence import SequenceGroupMetadata
+from vllm.utils import PyObjectCache
+
+if TYPE_CHECKING:
+    from vllm.worker.model_runner import GPUModelRunnerBase
+
+
+@dataclass
+class SequenceGroupToKVTransfer:
+    """
+    Metadata mainly used by kv_both scenarios.
+    """
+    prompt_len: Optional[int]
+    # Context tokens in prefix cache
+    context_tokens: Optional[List[int]]
+
+
+def seq_group_to_kv_transfer_builder():
+    return SequenceGroupToKVTransfer(
+        prompt_len=0,
+        context_tokens=None,
+    )
+
+
+class KVTransferMetadataCache:
+    """Used to cache SequenceGroupToKVTransfer objects between
+    scheduler iterations.
+    """
+
+    def __init__(self):
+        self._seq_group_to_kv_transfer_cache: PyObjectCache = PyObjectCache(
+            seq_group_to_kv_transfer_builder)
+
+    def get_cached_seq_group_to_kv_transfer(self):
+        obj = self._seq_group_to_kv_transfer_cache.get_object()
+        obj.prompt_len = 0
+        obj.context_tokens = None
+        return obj
+
+    def reset(self):
+        self._seq_group_to_kv_transfer_cache.reset()
+
+
+class KVTransferMetadata:
+    """Metadata for input sequences. Used in KV transfer.
+
+    Args:
+        seq_groups: List of batched sequence groups.
+        model_input_builder: Model input builder for rebuilding model input.
+    """
+
+    def __init__(
+        self,
+        seq_groups: List[SequenceGroupToKVTransfer],
+    ) -> None:
+        self.seq_groups = seq_groups
+        # only driver has seq_group_metadata_list and runner
+        self.seq_group_metadata_list: Optional[
+            List[SequenceGroupMetadata]] = None
+        self.runner: Optional[GPUModelRunnerBase] = None
+
+    @staticmethod
+    def prepare(
+        seq_group_metadata_list: List[SequenceGroupMetadata],
+        cache: Optional[KVTransferMetadataCache] = None,
+    ) -> "KVTransferMetadata":
+        """
+        context_lens include num of tokens in prefix cache.
+        """
+        seq_groups: List[SequenceGroupToKVTransfer] = []
+        ctx_idx = 0
+        for seq_group_metadata in seq_group_metadata_list:
+            seq_ids = list(seq_group_metadata.seq_data.keys())
+            for i in range(len(seq_ids)):
+                if cache is not None:
+                    seq_group_obj = cache.get_cached_seq_group_to_kv_transfer()
+                else:
+                    seq_group_obj = seq_group_to_kv_transfer_builder()
+
+                seq_id = seq_ids[i]
+                seq_data = seq_group_metadata.seq_data[seq_id]
+
+                seq_group_obj.prompt_len = seq_data.get_prompt_len()
+
+                seq_groups.append(seq_group_obj)
+
+            # prefill sequence group only has one sequence
+            if seq_group_metadata.is_prompt:
+                seq_id = seq_ids[0]
+                nblocks = len(seq_group_metadata.computed_block_nums or [])
+                seq_data = seq_group_metadata.seq_data[seq_id]
+                context_len = seq_data.get_num_cached_tokens()
+                # prefix caching:
+                #     context_len > 0 and nblocks > 0
+                # chunked prefill intermediate steps:
+                #     context_len > 0 and nblocks == 0
+                #
+                # We only carry context_tokens for prefix caching since
+                # chunked prefill intermediate steps can use the cached
+                # context tokens in the offloading connector.
+                if context_len > 0 and nblocks > 0:
+                    seq_groups[-1].context_tokens = seq_data.get_token_ids(
+                    )[:context_len]
+
+            ctx_idx += len(seq_ids)
+
+        if cache is not None:
+            cache.reset()
+
+        metadata = KVTransferMetadata(seq_groups=seq_groups)
+        return metadata
+
+    def __repr__(self) -> str:
+        return f"KVTransferMetadata(seq_groups={self.seq_groups})"
diff --git a/vllm/distributed/kv_transfer/kv_transfer_metrics.py b/vllm/distributed/kv_transfer/kv_transfer_metrics.py
new file mode 100644
index 000000000..e92fc8935
--- /dev/null
+++ b/vllm/distributed/kv_transfer/kv_transfer_metrics.py
@@ -0,0 +1,48 @@
+# SPDX-License-Identifier: Apache-2.0
+from abc import ABC, abstractmethod
+from typing import Dict
+
+
+class KVTransferMetrics(ABC):
+    """
+    KVTransferMetrics is used to collect metrics for KVTransferAgent.
+    """
+
+    @abstractmethod
+    def reset(self) -> None:
+        raise NotImplementedError
+
+    @abstractmethod
+    def __str__(self) -> str:
+        raise NotImplementedError
+
+
+class KVTransferMetricsExporter(ABC):
+    """
+    KVTransferMetrics is used to collect metrics for KVTransferAgent.
+    """
+
+    def __init__(
+        self,
+        *,
+        prefix,
+        labelnames,
+        gauge_cls,
+        counter_cls,
+        histogram_cls,
+    ):
+        self._prefix = prefix
+        self._labelnames = labelnames
+        self._gauge_cls = gauge_cls
+        self._counter_cls = counter_cls
+        self._histogram_cls = histogram_cls
+
+    @abstractmethod
+    def export(
+        self,
+        *,
+        metrics: KVTransferMetrics,
+        labels: Dict[str, str],
+    ) -> None:
+        """Export metrics to external systems."""
+        raise NotImplementedError
diff --git a/vllm/engine/llm_engine.py b/vllm/engine/llm_engine.py
index c23530990..6313de055 100644
--- a/vllm/engine/llm_engine.py
+++ b/vllm/engine/llm_engine.py
@@ -20,6 +20,7 @@ from vllm.config import (DecodingConfig, LoRAConfig, ModelConfig,
                          ObservabilityConfig, ParallelConfig, SchedulerConfig,
                          VllmConfig)
 from vllm.core.scheduler import ScheduledSequenceGroup, SchedulerOutputs
+from vllm.distributed.kv_transfer import get_kv_transfer_group
 from vllm.engine.arg_utils import EngineArgs
 from vllm.engine.metrics_types import StatLoggerBase, Stats
 from vllm.engine.output_processor.interfaces import (
@@ -1842,6 +1843,12 @@ class LLMEngine:
         else:
             spec_decode_metrics = None
 
+        if (self.vllm_config.kv_transfer_config is not None and
+                self.vllm_config.kv_transfer_config.is_kv_transfer_instance):
+            kv_transfer_metrics = get_kv_transfer_group().metrics
+        else:
+            kv_transfer_metrics = None
+
         return Stats(
             now=now,
             # System stats
@@ -1864,6 +1871,7 @@ class LLMEngine:
             time_per_output_tokens_iter=time_per_output_tokens_iter,
             spec_decode_metrics=spec_decode_metrics,
             num_preemption_iter=num_preemption_iter,
+            kv_transfer_metrics=kv_transfer_metrics,
 
             # Request stats
             #   Latency
diff --git a/vllm/engine/metrics.py b/vllm/engine/metrics.py
index 033551d07..4b740d8fa 100644
--- a/vllm/engine/metrics.py
+++ b/vllm/engine/metrics.py
@@ -9,6 +9,7 @@ import numpy as np
 import prometheus_client
 
 from vllm.config import SupportsMetricsInfo, VllmConfig
+from vllm.distributed.kv_transfer import get_kv_transfer_group
 from vllm.engine.metrics_types import StatLoggerBase, Stats
 from vllm.executor.ray_utils import ray
 from vllm.logger import init_logger
@@ -292,8 +293,20 @@ class Metrics:
             documentation="Number of emitted tokens.",
             labelnames=labelnames))
 
+        self.kv_transfer_metrics_exporter = None
+        if (vllm_config.kv_transfer_config is not None
+                and vllm_config.kv_transfer_config.is_kv_transfer_instance):
+            exporter_cls = get_kv_transfer_group().get_metrics_exporter_cls()
+            if exporter_cls is not None:
+                self.kv_transfer_metrics_exporter = exporter_cls(
+                    prefix="vllm:",
+                    labelnames=labelnames,
+                    gauge_cls=self._gauge_cls,
+                    counter_cls=self._counter_cls,
+                    histogram_cls=self._histogram_cls,
+                )
 
-# end-metrics-definitions
+    # end-metrics-definitions
 
     def _unregister_vllm_metrics(self) -> None:
         for collector in list(prometheus_client.REGISTRY._collector_to_names):
@@ -509,6 +522,9 @@ class LoggingStatLogger(StatLoggerBase):
                     self._format_spec_decode_metrics_str(
                         self.spec_decode_metrics))
 
+            if stats.kv_transfer_metrics is not None:
+                log_fn(str(stats.kv_transfer_metrics))
+
             self._reset(stats, prompt_throughput, generation_throughput)
 
     def _reset(self, stats, prompt_throughput, generation_throughput) -> None:
@@ -519,6 +535,8 @@ class LoggingStatLogger(StatLoggerBase):
         self.spec_decode_metrics = None
         self.last_prompt_throughput = prompt_throughput
         self.last_generation_throughput = generation_throughput
+        if stats.kv_transfer_metrics is not None:
+            stats.kv_transfer_metrics.reset()
 
     def _format_spec_decode_metrics_str(
             self, metrics: "SpecDecodeWorkerMetrics") -> str:
@@ -657,6 +675,12 @@ class PrometheusStatLogger(StatLoggerBase):
             stats.max_num_generation_tokens_requests)
         self._log_histogram(self.metrics.histogram_max_tokens_request,
                             stats.max_tokens_requests)
+        if (stats.kv_transfer_metrics is not None
+                and self.metrics.kv_transfer_metrics_exporter is not None):
+            self.metrics.kv_transfer_metrics_exporter.export(
+                metrics=stats.kv_transfer_metrics,
+                labels=self.labels,
+            )
 
     def log(self, stats: Stats):
         """Logs to prometheus and tracked stats every iteration."""
@@ -694,6 +718,8 @@ class PrometheusStatLogger(StatLoggerBase):
             self.num_generation_tokens = []
             self.last_local_log = stats.now
             self.spec_decode_metrics = None
+            if stats.kv_transfer_metrics is not None:
+                stats.kv_transfer_metrics.reset()
 
     def info(self, type: str, obj: SupportsMetricsInfo) -> None:
         # Info type metrics are syntactic sugar for a gauge permanently set to 1
diff --git a/vllm/engine/metrics_types.py b/vllm/engine/metrics_types.py
index 9e6d5ef29..3f9415ce6 100644
--- a/vllm/engine/metrics_types.py
+++ b/vllm/engine/metrics_types.py
@@ -18,6 +18,7 @@ from dataclasses import dataclass
 from typing import List, Optional
 
 from vllm.config import SupportsMetricsInfo, VllmConfig
+from vllm.distributed.kv_transfer.kv_transfer_metrics import KVTransferMetrics
 from vllm.spec_decode.metrics import SpecDecodeWorkerMetrics
 
 
@@ -68,6 +69,7 @@ class Stats:
     max_lora: str
 
     spec_decode_metrics: Optional["SpecDecodeWorkerMetrics"] = None
+    kv_transfer_metrics: Optional["KVTransferMetrics"] = None
 
 
 class StatLoggerBase(ABC):
diff --git a/vllm/worker/model_runner.py b/vllm/worker/model_runner.py
index 73e0eff9a..7a50b16ba 100644
--- a/vllm/worker/model_runner.py
+++ b/vllm/worker/model_runner.py
@@ -25,6 +25,8 @@ from vllm.config import CompilationLevel, VllmConfig
 from vllm.core.scheduler import SchedulerOutputs
 from vllm.distributed import get_pp_group
 from vllm.distributed.kv_transfer import get_kv_transfer_group
+from vllm.distributed.kv_transfer.kv_transfer_metadata import (
+    KVTransferMetadata, KVTransferMetadataCache)
 from vllm.distributed.parallel_state import (get_tensor_model_parallel_rank,
                                              graph_capture)
 from vllm.forward_context import get_forward_context, set_forward_context
@@ -56,8 +58,10 @@ from vllm.utils import (DeviceMemoryProfiler, GiB_bytes, PyObjectCache,
 from vllm.worker.model_runner_base import (
     InputProcessingError, ModelRunnerBase, ModelRunnerInputBase,
     ModelRunnerInputBuilderBase, _add_attn_metadata_broadcastable_dict,
+    _add_kv_transfer_metadata_broadcastable_dict,
     _add_sampling_metadata_broadcastable_dict,
     _init_attn_metadata_from_tensor_dict,
+    _init_kv_transfer_metadata_from_tensor_dict,
     _init_sampling_metadata_from_tensor_dict)
 
 if TYPE_CHECKING:
@@ -101,11 +105,14 @@ class ModelInputForGPU(ModelRunnerInputBase):
     async_callback: Optional[Callable] = None
     scheduler_outputs: Optional[SchedulerOutputs] = None
     previous_hidden_states: Optional[torch.Tensor] = None
+    kv_transfer_metadata: Optional[KVTransferMetadata] = None
 
     def as_broadcastable_tensor_dict(self) -> Dict[str, Any]:
         tensor_dict = {
             "input_tokens": self.input_tokens,
             "input_positions": self.input_positions,
+            "seq_lens": self.seq_lens,
+            "query_lens": self.query_lens,
             "lora_requests": self.lora_requests,
             "lora_mapping": self.lora_mapping,
             "multi_modal_kwargs": self.multi_modal_kwargs,
@@ -116,6 +123,8 @@ class ModelInputForGPU(ModelRunnerInputBase):
             "finished_requests_ids": self.finished_requests_ids,
         }
         _add_attn_metadata_broadcastable_dict(tensor_dict, self.attn_metadata)
+        _add_kv_transfer_metadata_broadcastable_dict(tensor_dict,
+                                                     self.kv_transfer_metadata)
         return tensor_dict
 
     @classmethod
@@ -127,6 +136,7 @@ class ModelInputForGPU(ModelRunnerInputBase):
         if attn_backend is not None:
             tensor_dict = _init_attn_metadata_from_tensor_dict(
                 attn_backend, tensor_dict)
+        _init_kv_transfer_metadata_from_tensor_dict(tensor_dict)
         return cls(**tensor_dict)
 
     # Exclude `async_callback` to be able to pickle this object
@@ -156,6 +166,8 @@ class ModelInputForGPUWithSamplingMetadata(ModelInputForGPU):
         tensor_dict = {
             "input_tokens": self.input_tokens,
             "input_positions": self.input_positions,
+            "seq_lens": self.seq_lens,
+            "query_lens": self.query_lens,
             "lora_requests": self.lora_requests,
             "lora_mapping": self.lora_mapping,
             "multi_modal_kwargs": self.multi_modal_kwargs,
@@ -166,6 +178,8 @@ class ModelInputForGPUWithSamplingMetadata(ModelInputForGPU):
             "finished_requests_ids": self.finished_requests_ids,
         }
         _add_attn_metadata_broadcastable_dict(tensor_dict, self.attn_metadata)
+        _add_kv_transfer_metadata_broadcastable_dict(tensor_dict,
+                                                     self.kv_transfer_metadata)
         _add_sampling_metadata_broadcastable_dict(tensor_dict,
                                                   self.sampling_metadata)
         return tensor_dict
@@ -180,6 +194,7 @@ class ModelInputForGPUWithSamplingMetadata(ModelInputForGPU):
         if attn_backend is not None:
             tensor_dict = _init_attn_metadata_from_tensor_dict(
                 attn_backend, tensor_dict)
+        _init_kv_transfer_metadata_from_tensor_dict(tensor_dict)
         return cls(**tensor_dict)
 
 
@@ -1100,6 +1115,10 @@ class GPUModelRunnerBase(ModelRunnerBase[TModelInputForGPU]):
               SamplingMetadataCache() \
                 if self.parallel_config.pipeline_parallel_size == 1 else None
 
+        self.kv_transfer_metadata_cache: KVTransferMetadataCache = (
+            KVTransferMetadataCache()
+            if self.vllm_config.kv_transfer_config is not None else None)
+
         if hasattr(self, "_builder_cls"):
             # multi-step model runner does not have `_builder_cls`
             self.builder = self._builder_cls(weakref.proxy(self))
@@ -1668,8 +1687,21 @@ class ModelRunner(GPUModelRunnerBase[ModelInputForGPUWithSamplingMetadata]):
             sampling_metadata = None
         is_prompt = (seq_group_metadata_list[0].is_prompt
                      if seq_group_metadata_list else None)
+
+        if self.vllm_config.kv_transfer_config is not None:
+            kv_transfer_metadata = KVTransferMetadata.prepare(
+                seq_group_metadata_list,
+                self.kv_transfer_metadata_cache,
+            )
+            # attach seq_group_metadata_list for rebuilding model_input
+            # on the driver side
+            kv_transfer_metadata.seq_group_metadata_list = \
+                seq_group_metadata_list
+        else:
+            kv_transfer_metadata = None
         return dataclasses.replace(model_input,
                                    sampling_metadata=sampling_metadata,
+                                   kv_transfer_metadata=kv_transfer_metadata,
                                    is_prompt=is_prompt,
                                    virtual_engine=virtual_engine)
 
@@ -1734,6 +1766,8 @@ class ModelRunner(GPUModelRunnerBase[ModelInputForGPUWithSamplingMetadata]):
         # NOTE: The receive operation is blocking
         bypass_model_exec = False
         if self.need_recv_kv(model_input, kv_caches):
+            # attach runner for rebuilding model_input
+            model_input.kv_transfer_metadata.runner = weakref.proxy(self)
             hidden_or_intermediate_states, bypass_model_exec, model_input = \
                 get_kv_transfer_group().recv_kv_caches_and_hidden_states(
                     # model is used to know which layer the current worker
diff --git a/vllm/worker/model_runner_base.py b/vllm/worker/model_runner_base.py
index 935325cb2..f58af7d4d 100644
--- a/vllm/worker/model_runner_base.py
+++ b/vllm/worker/model_runner_base.py
@@ -16,6 +16,8 @@ from vllm.sequence import IntermediateTensors, SequenceGroupMetadata
 if TYPE_CHECKING:
     from vllm.attention import AttentionMetadata
     from vllm.attention.backends.abstract import AttentionBackend
+    from vllm.distributed.kv_transfer.kv_transfer_metadata import (
+        KVTransferMetadata)
     from vllm.model_executor import SamplingMetadata
 
 logger = init_logger(__name__)
@@ -46,7 +48,7 @@ def _init_attn_metadata_from_tensor_dict(
     valid_attn_kwargs = {}
     for field in dataclasses.fields(attn_backend.get_metadata_cls()):
         if field.name in tensor_dict:
-            if field.name == "input_positions":
+            if field.name in ["input_positions", "seq_lens"]:
                 valid_attn_kwargs[field.name] = tensor_dict[field.name]
             else:
                 valid_attn_kwargs[field.name] = tensor_dict.pop(field.name)
@@ -89,6 +91,34 @@ def _add_sampling_metadata_broadcastable_dict(
             sampling_metadata.selected_token_indices)
 
 
+def _init_kv_transfer_metadata_from_tensor_dict(
+        tensor_dict: Dict[str, Any]) -> Dict[str, Any]:
+    """
+    Helper method to initialize KVTransferMetadata based on broadcastable
+    KVTransferMetadata fields.
+    """
+    from vllm.distributed.kv_transfer.kv_transfer_metadata import (
+        KVTransferMetadata)
+
+    kv_transfer_metadata = tensor_dict.pop("kv_transfer_metadata", None)
+    if kv_transfer_metadata is not None:
+        seq_groups = kv_transfer_metadata
+        tensor_dict["kv_transfer_metadata"] = KVTransferMetadata(
+            seq_groups=seq_groups)
+    return tensor_dict
+
+
+def _add_kv_transfer_metadata_broadcastable_dict(
+        tensor_dict: Dict[str, Any],
+        kv_transfer_metadata: Optional["KVTransferMetadata"]) -> None:
+    """
+    Helper method to update tensor_dict with broadcastable
+    KVTransferMetadata fields.
+    """
+    if kv_transfer_metadata is not None:
+        tensor_dict["kv_transfer_metadata"] = kv_transfer_metadata.seq_groups
+
+
 def _init_frozen_model_input_from_tensor_dict(
         frozen_model_input_cls: Type["ModelRunnerInputBase"],
         tensor_dict: Dict[str, Any]) -> Dict[str, Any]:
