---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: mutating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-model-aibrix-ai-v1alpha1-modeladapter
  failurePolicy: Fail
  name: mmodeladapter.kb.io
  rules:
  - apiGroups:
    - model.aibrix.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - modeladapters
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-model-aibrix-ai-v1alpha1-modeladapter
  failurePolicy: Fail
  name: vmodeladapter.kb.io
  rules:
  - apiGroups:
    - model.aibrix.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - modeladapters
  sideEffects: None
