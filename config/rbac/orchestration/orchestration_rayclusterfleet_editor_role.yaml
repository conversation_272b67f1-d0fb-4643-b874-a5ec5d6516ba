# permissions for end users to edit rayclusterfleets.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: aibrix
    app.kubernetes.io/managed-by: kustomize
  name: orchestration-rayclusterfleet-editor-role
rules:
- apiGroups:
  - orchestration.aibrix.ai
  resources:
  - rayclusterfleets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - orchestration.aibrix.ai
  resources:
  - rayclusterfleets/status
  verbs:
  - get
