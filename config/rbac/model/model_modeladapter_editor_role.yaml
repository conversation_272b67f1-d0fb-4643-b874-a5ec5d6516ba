# permissions for end users to edit modeladapters.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: aibrix
    app.kubernetes.io/managed-by: kustomize
  name: model-modeladapter-editor-role
rules:
- apiGroups:
  - model.aibrix.ai
  resources:
  - modeladapters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - model.aibrix.ai
  resources:
  - modeladapters/status
  verbs:
  - get
