apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../crd/orchestration
  - ../../rbac/orchestration
  - ../../rbac/controller-manager
  - ../../manager
  - ../../dependency/kuberay-operator

# Adds namespace to all resources.
namespace: aibrix-system

# Value of this field is prepended to the
# names of all resources, e.g. a deployment named
# "wordpress" becomes "alices-wordpress".
# Note that it should also match with the prefix (text before '-') of the namespace
# field above.
namePrefix: aibrix-orchestration-

images:
  - name: controller
    newName: aibrix/controller-manager
    newTag: v0.3.0
  - name: quay.io/kuberay/operator
    newName: aibrix/kuberay-operator
    newTag: v1.2.1-patch

patches:
  - path: patch.yaml
    target:
      group: apps
      version: v1
      kind: Deployment
      name: controller-manager
  - patch: |
      apiVersion: rbac.authorization.k8s.io/v1
      kind: ClusterRoleBinding
      metadata:
        name: kuberay-operator
      subjects:
        - kind: ServiceAccount
          name: aibrix-orchestration-kuberay-operator
          namespace: aibrix-system
  - patch: |
      apiVersion: rbac.authorization.k8s.io/v1
      kind: RoleBinding
      metadata:
        name: kuberay-operator-leader-election
      subjects:
        - kind: ServiceAccount
          name: aibrix-orchestration-kuberay-operator-leader-election
          namespace: aibrix-system
          

labels:
  - pairs:
      app.kubernetes.io/name: aibrix
      app.kubernetes.io/component: aibrix-distributed-inference-controller
      app.kubernetes.io/version: nightly
      app.kubernetes.io/managed-by: kubectl
