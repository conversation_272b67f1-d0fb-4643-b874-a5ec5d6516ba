apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: system
spec:
  template:
    spec:
      containers:
        - name: manager
          args:
            - --leader-elect
            - --leader-election-id=aibrix-model-adapter-controller
            - --health-probe-bind-address=:8081
            - --metrics-bind-address=0
            - --controllers=model-adapter-controller
          # following patch is from config/default/manager_webhook_patch.yaml
          ports:
            - containerPort: 9443
              name: webhook-server
              protocol: TCP
          volumeMounts:
            - mountPath: /tmp/k8s-webhook-server/serving-certs
              name: cert
              readOnly: true
      volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: webhook-server-cert
