---
# Source: kuberay-operator/templates/leader_election_role.yaml
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels: 
    app.kubernetes.io/name: kuberay-operator
    helm.sh/chart: kuberay-operator-1.2.1
    app.kubernetes.io/instance: kuberay-operator
    app.kubernetes.io/managed-by: Helm
  name: kuberay-operator-leader-election
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - configmaps/status
  verbs:
  - get
  - update
  - patch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - get
  - list
  - update
