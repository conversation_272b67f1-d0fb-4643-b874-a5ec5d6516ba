---
# Source: kuberay-operator/templates/rolebinding.yaml
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels:
    app.kubernetes.io/name: kuberay-operator
    helm.sh/chart: kuberay-operator-1.2.1
    app.kubernetes.io/instance: kuberay-operator
    app.kubernetes.io/managed-by: Helm
  name: kuberay-operator
subjects:
- kind: ServiceAccount
  name: kuberay-operator
  namespace: aibrix-system
roleRef:
  kind: ClusterRole
  name: kuberay-operator
  apiGroup: rbac.authorization.k8s.io
