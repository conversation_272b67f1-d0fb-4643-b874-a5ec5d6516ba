---
# Source: kuberay-operator/templates/leader_election_role_binding.yaml
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels: 
    app.kubernetes.io/name: kuberay-operator
    helm.sh/chart: kuberay-operator-1.2.1
    app.kubernetes.io/instance: kuberay-operator
    app.kubernetes.io/managed-by: Helm
  name: kuberay-operator-leader-election
subjects:
- kind: ServiceAccount
  name: kuberay-operator
  namespace: aibrix-system
roleRef:
  kind: Role
  name: kuberay-operator-leader-election
  apiGroup: rbac.authorization.k8s.io
