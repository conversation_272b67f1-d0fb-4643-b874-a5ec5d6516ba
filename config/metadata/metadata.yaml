apiVersion: v1
kind: Service
metadata:
  name: metadata-service
  namespace: aibrix-system
spec:
  selector:
    app: metadata-service
  ports:
    - protocol: TCP
      port: 8090
      targetPort: 8090
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metadata-service
  namespace: aibrix-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metadata-service
  template:
    metadata:
      labels:
        app: metadata-service
    spec:
      initContainers:
        - name: init-c
          image: busybox
          command: ['sh', '-c', 'until echo "ping" | nc aibrix-redis-master 6379 -w 1  | grep -c PONG; do echo waiting for service aibrix-redis-master; sleep 2; done']
      containers:
        - name: metadata-service
          image: metadata-service:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8090
          resources:
            limits:
              cpu: 500m
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 64Mi
          env:
            - name: REDIS_HOST
              value: aibrix-redis-master
            - name: REDIS_PORT
              value: "6379" 
            # TODO: cache is shared across all control plane so add feature flags to enable metric pull
            # for now setting to a 1 hr for metadata service
            - name: AIBRIX_POD_METRIC_REFRESH_INTERVAL_MS
              value: "3600000"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
      # TODO: separate this part
      serviceAccountName: aibrix-gateway-plugins
