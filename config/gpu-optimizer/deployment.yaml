apiVersion: apps/v1
kind: Deployment
metadata:
  name: gpu-optimizer
  namespace: system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gpu-optimizer
  template:
    metadata:
      labels:
        app: gpu-optimizer
    spec:
      serviceAccountName: gpu-optimizer-sa
      automountServiceAccountToken: true
      containers:
      - name: gpu-optimizer
        image: aibrix/runtime:nightly
        command: ["python", "-m", "aibrix.gpu_optimizer.app"]
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: 500m
            memory: 256Mi
          requests:
            cpu: 10m
            memory: 64Mi
        env:
          - name: REDIS_HOST
            value: aibrix-redis-master.aibrix-system.svc.cluster.local