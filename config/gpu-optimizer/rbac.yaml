apiVersion: v1
kind: ServiceAccount
metadata:
  name: gpu-optimizer-sa
  namespace: system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: gpu-optimizer-clusterrole
rules:
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: gpu-optimizer-clusterrole-binding
subjects:
  - kind: ServiceAccount
    name: gpu-optimizer-sa
    namespace: system
roleRef:
  kind: ClusterRole
  name: gpu-optimizer-clusterrole
  apiGroup: rbac.authorization.k8s.io