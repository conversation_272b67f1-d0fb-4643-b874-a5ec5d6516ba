---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: podautoscalers.autoscaling.aibrix.ai
spec:
  group: autoscaling.aibrix.ai
  names:
    kind: PodAutoscaler
    listKind: PodAutoscalerList
    plural: podautoscalers
    singular: podautoscaler
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              maxReplicas:
                format: int32
                type: integer
              metricsSources:
                items:
                  properties:
                    endpoint:
                      type: string
                    metricSourceType:
                      type: string
                    path:
                      type: string
                    port:
                      type: string
                    protocolType:
                      type: string
                    targetMetric:
                      type: string
                    targetValue:
                      type: string
                  required:
                  - metricSourceType
                  - path
                  - protocolType
                  - targetMetric
                  - targetValue
                  type: object
                type: array
              minReplicas:
                format: int32
                type: integer
              scaleTargetRef:
                properties:
                  apiVersion:
                    type: string
                  fieldPath:
                    type: string
                  kind:
                    type: string
                  name:
                    type: string
                  namespace:
                    type: string
                  resourceVersion:
                    type: string
                  uid:
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              scalingStrategy:
                type: string
            required:
            - maxReplicas
            - scaleTargetRef
            - scalingStrategy
            type: object
          status:
            properties:
              actualScale:
                format: int32
                type: integer
              conditions:
                items:
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              desiredScale:
                format: int32
                type: integer
              lastScaleTime:
                format: date-time
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
