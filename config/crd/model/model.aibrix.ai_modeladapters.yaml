---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: modeladapters.model.aibrix.ai
spec:
  group: model.aibrix.ai
  names:
    kind: ModelAdapter
    listKind: ModelAdapterList
    plural: modeladapters
    singular: modeladapter
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              additionalConfig:
                additionalProperties:
                  type: string
                type: object
              artifactURL:
                type: string
              baseModel:
                type: string
              credentialsSecretRef:
                properties:
                  name:
                    default: ""
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              podSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              replicas:
                default: 1
                format: int32
                type: integer
              schedulerName:
                default: default
                type: string
            type: object
          status:
            properties:
              conditions:
                items:
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              instances:
                items:
                  type: string
                type: array
              phase:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
