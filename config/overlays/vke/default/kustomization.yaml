apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../default

patches:
  - path: envoy_proxy_patch.yaml
  - path: gateway_plugins_patch.yaml

images:
- name: quay.io/kuberay/operator
  newName: aibrix-cn-beijing.cr.volces.com/aibrix/kuberay-operator
  newTag: v1.2.1-patch
- name: busybox
  newName: aibrix-cn-beijing.cr.volces.com/aibrix/busybox
  newTag: stable
- name: redis
  newName: aibrix-cn-beijing.cr.volces.com/aibrix/redis
  newTag: latest
- name: aibrix/gateway-plugins
  newName: aibrix-cn-beijing.cr.volces.com/aibrix/gateway-plugins
  newTag: v0.3.0
- name: aibrix/metadata-service
  newName: aibrix-cn-beijing.cr.volces.com/aibrix/metadata-service
  newTag: v0.3.0
- name: aibrix/controller-manager
  newName: aibrix-cn-beijing.cr.volces.com/aibrix/controller-manager
  newTag: v0.3.0
- name: aibrix/runtime
  newName: aibrix-cn-beijing.cr.volces.com/aibrix/runtime
  newTag: v0.3.0
