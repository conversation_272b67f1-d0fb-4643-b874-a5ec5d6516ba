apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: aibrix-system

namePrefix: aibrix-

resources:
- ../../../manager

patches:
- patch: |-  # Use the '|' and '-' for inline patching
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: controller-manager
    spec:
      template:
        spec:
          containers:
            - name: manager
              args:
                - --leader-elect
                - --health-probe-bind-address=:8081
                - --metrics-bind-address=0
                - -v=4
  target:
    kind: Deployment
    name: controller-manager
    namespace: system
    version: v1

apiVersion: kustomize.config.k8s.io/v1beta1