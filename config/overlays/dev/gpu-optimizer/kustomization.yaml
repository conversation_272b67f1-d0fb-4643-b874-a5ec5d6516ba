apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: aibrix-system

namePrefix: aibrix-

resources:
- ../../../gpu-optimizer

patches:
- patch: |-  # Use the '|' and '-' for inline patching
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: gpu-optimizer
    spec:
      template:
        spec:
          containers:
            - name: gpu-optimizer
              command: ["python", "-m", "aibrix.gpu_optimizer.app", "--debug"]
  target:
    kind: Deployment
    name: gpu-optimizer
    namespace: system
    version: v1

apiVersion: kustomize.config.k8s.io/v1beta1