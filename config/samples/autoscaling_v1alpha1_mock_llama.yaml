apiVersion: autoscaling.aibrix.ai/v1alpha1
kind: PodAutoscaler
metadata:
  name: podautoscaler-example-mock-llama
  labels:
    app.kubernetes.io/name: aibrix
    app.kubernetes.io/managed-by: kustomize
    autoscaling.aibrix.ai/max-scale-up-rate: "2"
    autoscaling.aibrix.ai/max-scale-down-rate: "2"
    kpa.autoscaling.aibrix.ai/stable-window: "60s"
    kpa.autoscaling.aibrix.ai/scale-down-delay: "60s"
  namespace: aibrix-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: llama2-70b
  minReplicas: 1
  maxReplicas: 10
  targetMetric: "avg_prompt_throughput_toks_per_s"
  targetValue: "20"
  scalingStrategy: "KPA"