{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from io import StringIO\n", "\n", "\n", "# 4 loras\n", "title = \"Overall Resource Time Used (GPU Seconds)\"\n", "csv_data = \"\"\"Concurrency,AIBrix,Merged Model,Single LoRA\n", "1,1.0,4.0,4.0\n", "4,2.0,4.0,4.0\n", "16,6.0,7.5,8.0\n", "64,27.0,24.0,28.0\n", "256,106.0,100.0,108.0\n", "\"\"\"\n", "# Use StringIO to simulate a file object\n", "csv_file = StringIO(csv_data)\n", "\n", "# Read the CSV data into a DataFrame\n", "df = pd.read_csv(csv_file)\n", "\n", "# Set the Concurrency column as the index (optional)\n", "df['Concurrency'] = df['Concurrency'].astype(str)\n", "df.set_index('Concurrency', inplace=True)\n", "\n", "# Plot configuration\n", "approaches = df.columns  # ['AIBrix', 'Merged Model', 'Single LoRA']\n", "concurrencies = df.index  # [1, 4, 16, 64, 256]\n", "bar_width = 0.2  # Width of the bars\n", "bar_positions = np.arange(len(concurrencies))  # Positions on the x-axis for each concurrency\n", "\n", "# Generate the plot\n", "plt.figure(figsize=(6, 3))\n", "\n", "# Plot bars for each approach\n", "for i, approach in enumerate(approaches):\n", "    plt.bar(bar_positions + i * bar_width, df[approach], width=bar_width, label=approach)\n", "\n", "# Add x-ticks and labels\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('GPU Seconds')\n", "plt.title('Overall Resource Time Used (GPU Seconds)')\n", "plt.xticks(bar_positions + bar_width, concurrencies)\n", "plt.legend()\n", "\n", "# Display the plot\n", "plt.show()\n", "# plt.savefig(\"plot.pdf\")\n", "\n", "# Plot configuration\n", "plt.figure(figsize=(6, 3))\n", "\n", "# Plot each approach as a line plot\n", "for approach in df.columns:\n", "    plt.plot(df.index, df[approach], marker='o', linestyle='-', label=approach)\n", "\n", "# Add x-ticks and labels\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('GPU Seconds')\n", "plt.title('Overall Resource Time Used (GPU Seconds)')\n", "plt.xticks(df.index)\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "# Display the plot\n", "plt.show()\n", "\n", "\n", "\n", "############################################################################################################\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from io import StringIO\n", "\n", "# Declare the CSV content as a string\n", "csv_data = \"\"\"Concurrency,AIBrix,Merged Model,Single LoRA\n", "1,1.0,4.0,4.0\n", "4,2.0,4.0,4.0\n", "16,6.0,7.5,8.0\n", "64,27.0,24.0,28.0\n", "256,106.0,100.0,108.0\n", "\"\"\"\n", "\n", "# Use StringIO to simulate a file object\n", "csv_file = StringIO(csv_data)\n", "\n", "# Read the CSV data into a DataFrame\n", "df = pd.read_csv(csv_file)\n", "\n", "# Calculate the percentage reduction of AIBrix compared to Merged Model and Single LoRA\n", "df['Merged Model Reduction (%)'] = ((df['Merged Model'] - df['AIBrix']) / df['Merged Model']) * 100\n", "df['Single LoRA Reduction (%)'] = ((df['Single LoRA'] - df['AIBrix']) / df['Single LoRA']) * 100\n", "\n", "# Prepare data for bar plot\n", "labels = df['Concurrency'].astype(str)  # Concurrency as categorical labels\n", "merged_model_reduction = df['Merged Model Reduction (%)']\n", "single_lora_reduction = df['Single LoRA Reduction (%)']\n", "\n", "# Plot configuration\n", "bar_width = 0.4\n", "x = range(len(df))\n", "\n", "plt.figure(figsize=(6, 3))\n", "\n", "# Plot bars for each concurrency level\n", "plt.bar(x, merged_model_reduction, width=bar_width, label='Merged Model Reduction (%)', align='center')\n", "plt.bar([i + bar_width for i in x], single_lora_reduction, width=bar_width, label='Single LoRA Reduction (%)', align='edge')\n", "\n", "# Add labels and title\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('Percentage Reduction (%)')\n", "plt.title('Percentage Reduction in GPU Seconds for AIBrix Compared to Other Approaches')\n", "plt.xticks([i + bar_width / 2 for i in x], labels, rotation=45)  # Center the x-tick labels\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "from collections import defaultdict\n", "import pandas as pd\n", "from glob import glob\n", "import re\n", "\n", "\n", "def convert_files_csv(directory, multiplier):\n", "\n", "    # Function to calculate average latency from a list of latencies\n", "    def calculate_average_latency(file_path):\n", "        latencies = []\n", "        with open(file_path, 'r') as f:\n", "            for line in f:\n", "                result = json.loads(line)\n", "                latencies.append(result['latency'])\n", "        return sum(latencies) / len(latencies) if latencies else float('nan')\n", "\n", "    # Directory containing the .jsonl files\n", "    # directory = 'data'  # change this to the actual folder path\n", "\n", "    # Dictionary to store the data\n", "    data = defaultdict(lambda: defaultdict(list))\n", "\n", "    # Read all .jsonl files\n", "    for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "        # Extract the approach name and concurrency value from the file name\n", "        file_name = os.path.basename(file_path)\n", "        parts = file_name.split('_')\n", "        # print(parts)\n", "        approach = '_'.join(parts[1:-2])  # Extract approach name\n", "        concurrency = int(parts[-1].replace('.jsonl', ''))\n", "        \n", "        # Extract the number from the approach name if it ends with a number\n", "        match = re.match(r'.*_(\\d+)$', approach)\n", "        num_machines = 1 if match else multiplier\n", "    \n", "        # Calculate the average latency\n", "        avg_latency = calculate_average_latency(file_path)\n", "        \n", "        # Modify the latency by multiplying with the extracted number\n", "        avg_latency *= num_machines\n", "    \n", "        # Store the data\n", "        data[concurrency][approach] = avg_latency\n", "\n", "    # Get sorted list of approaches and concurrency levels\n", "    approaches = sorted({approach for d in data.values() for approach in d})\n", "    concurrency_levels = sorted(data.keys())\n", "\n", "    # Create a DataFrame and fill it with the data\n", "    df = pd.DataFrame(index=concurrency_levels, columns=approaches)\n", "    for concurrency, approaches_dict in data.items():\n", "        for approach, avg_latency in approaches_dict.items():\n", "            df.at[concurrency, approach] = avg_latency\n", "\n", "    # Fill missing values with NaN (optional)\n", "    df = df.fillna('')\n", "\n", "    # Convert the DataFrame to a CSV string\n", "    csv_string = df.reset_index().rename(columns={'index': 'Concurrency'}).to_csv(index=False)\n", "\n", "    print(csv_string)\n", "    return csv_string\n", "# convert_files_csv(\"benchmark_result_pin_lora\", 4)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Concurrency,merged,unmerged_multi_lora_4,unmerged_single_lora\n", "1,14.461155201133806,9.918339344632841,19.892462898627855\n", "2,13.906062273439602,9.701025935843063,15.384865331099718\n", "3,14.274588158135884,9.983447100490594,15.822014119228697\n", "4,14.59062016106327,10.355373331141891,16.27808581494901\n", "5,14.93298179958947,10.42986469421885,16.708376241018414\n", "6,15.147289600412478,10.615328026797215,17.07953821324918\n", "7,15.36949259028188,10.794894828235556,17.257244382388308\n", "8,15.887041407244396,11.217811148468172,18.064403024647618\n", "9,16.110205019183923,11.372777714379481,18.47801198977686\n", "10,16.29309560955153,11.580395810611662,18.915229057107354\n", "11,16.601634319420555,11.832915667589987,19.183005752784084\n", "12,16.848645843361737,12.284345691386989,19.347974390839227\n", "13,17.18925307277823,12.648846523654356,19.960359828241053\n", "14,17.655552729891497,13.201191643689526,20.495765477156965\n", "15,18.34280791755009,13.64215617741138,21.566909273169586\n", "16,18.69428209218313,13.976565272714652,22.2374851051718\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/fr/tdgd1l9x1x7drdw1hdcdjrc00000gn/T/ipykernel_7072/812306482.py:59: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df = df.fillna('')\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from io import StringIO\n", "\n", "\n", "# 4 loras\n", "title = \"Overall Resource Time Used (GPU Seconds)\"\n", "# csv_data = read_file_to_csv_string(\"data\")\n", "csv_data = convert_files_csv(\"benchmark_result_pin_lora\", 4)\n", "# \"\"\"Concurrency,AIBrix,Merged Model,Single LoRA\n", "# 1,1.0,4.0,4.0\n", "# 4,2.0,4.0,4.0\n", "# 16,6.0,7.5,8.0\n", "# 64,27.0,24.0,28.0\n", "# 256,106.0,100.0,108.0\n", "# \"\"\"\n", "\n", "# Use StringIO to simulate a file object\n", "csv_file = StringIO(csv_data)\n", "\n", "# Read the CSV data into a DataFrame\n", "df = pd.read_csv(csv_file)\n", "\n", "# Set the Concurrency column as the index (optional)\n", "df['Concurrency'] = df['Concurrency'].astype(str)\n", "df.set_index('Concurrency', inplace=True)\n", "\n", "# Plot configuration\n", "approaches = df.columns  # ['AIBrix', 'Merged Model', 'Single LoRA']\n", "concurrencies = df.index  # [1, 4, 16, 64, 256]\n", "bar_width = 0.2  # Width of the bars\n", "bar_positions = np.arange(len(concurrencies))  # Positions on the x-axis for each concurrency\n", "\n", "# Generate the plot\n", "plt.figure(figsize=(6, 3))\n", "\n", "# Plot bars for each approach\n", "for i, approach in enumerate(approaches):\n", "    plt.bar(bar_positions + i * bar_width, df[approach], width=bar_width, label=approach)\n", "\n", "# Add x-ticks and labels\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('GPU Seconds')\n", "plt.title('Overall Resource Time Used (GPU Seconds)')\n", "plt.xticks(bar_positions + bar_width, concurrencies)\n", "plt.legend()\n", "\n", "# Display the plot\n", "plt.show()\n", "# plt.savefig(\"plot.pdf\")\n", "\n", "# Plot configuration\n", "plt.figure(figsize=(6, 3))\n", "\n", "# Plot each approach as a line plot\n", "for approach in df.columns:\n", "    plt.plot(df.index, df[approach], marker='o', linestyle='-', label=approach)\n", "\n", "# Add x-ticks and labels\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('GPU Seconds')\n", "plt.title('Overall Resource Time Used (GPU Seconds)')\n", "plt.xticks(df.index)\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "# Display the plot\n", "plt.show()\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/fr/tdgd1l9x1x7drdw1hdcdjrc00000gn/T/ipykernel_7072/812306482.py:59: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df = df.fillna('')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Concurrency,merged,unmerged_multi_lora_4,unmerged_single_lora\n", "1,14.461155201133806,9.918339344632841,19.892462898627855\n", "2,13.906062273439602,9.701025935843063,15.384865331099718\n", "3,14.274588158135884,9.983447100490594,15.822014119228697\n", "4,14.59062016106327,10.355373331141891,16.27808581494901\n", "5,14.93298179958947,10.42986469421885,16.708376241018414\n", "6,15.147289600412478,10.615328026797215,17.07953821324918\n", "7,15.36949259028188,10.794894828235556,17.257244382388308\n", "8,15.887041407244396,11.217811148468172,18.064403024647618\n", "9,16.110205019183923,11.372777714379481,18.47801198977686\n", "10,16.29309560955153,11.580395810611662,18.915229057107354\n", "11,16.601634319420555,11.832915667589987,19.183005752784084\n", "12,16.848645843361737,12.284345691386989,19.347974390839227\n", "13,17.18925307277823,12.648846523654356,19.960359828241053\n", "14,17.655552729891497,13.201191643689526,20.495765477156965\n", "15,18.34280791755009,13.64215617741138,21.566909273169586\n", "16,18.69428209218313,13.976565272714652,22.2374851051718\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from io import StringIO\n", "\n", "csv_data = convert_files_csv(\"benchmark_result_pin_lora\", 4)\n", "# \"\"\"Concurrency,AIBrix,Merged Model,Single LoRA\n", "# 1,1.0,4.0,4.0\n", "# 4,2.0,4.0,4.0\n", "# 16,6.0,7.5,8.0\n", "# 64,27.0,24.0,28.0\n", "# 256,106.0,100.0,108.0\n", "# \"\"\"\n", "\n", "# Use StringIO to simulate a file object\n", "csv_file = StringIO(csv_data)\n", "\n", "# Read the CSV data into a DataFrame\n", "df = pd.read_csv(csv_file)\n", "\n", "# Concurrency,merged,unmerged_multi_lora_4,unmerged_single_lora\n", "\n", "# Calculate the percentage reduction of AIBrix compared to Merged Model and Single LoRA\n", "df['Reduction Compared to merged (%)'] = ((df['merged'] - df['unmerged_multi_lora_4']) / df['merged']) * 100\n", "df['Reduction Compared to unmerged_single_lora (%)'] = ((df['unmerged_single_lora'] - df['unmerged_multi_lora_4']) / df['unmerged_single_lora']) * 100\n", "\n", "# Prepare data for bar plot\n", "labels = df['Concurrency'].astype(str)  # Concurrency as categorical labels\n", "merged_model_reduction = df['Reduction Compared to merged (%)']\n", "single_lora_reduction = df['Reduction Compared to unmerged_single_lora (%)']\n", "\n", "# Plot configuration\n", "bar_width = 0.4\n", "x = range(len(df))\n", "\n", "plt.figure(figsize=(6, 3))\n", "\n", "# Plot bars for each concurrency level\n", "plt.bar(x, merged_model_reduction, width=bar_width, label='Reduction Compared to merged (%)', align='center')\n", "plt.bar([i + bar_width for i in x], single_lora_reduction, width=bar_width, label='Reduction Compared to unmerged_single_lora (%)', align='edge')\n", "\n", "# Add labels and title\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('Percentage Reduction (%)')\n", "plt.title('Percentage Reduction in GPU Seconds for AIBrix Compared to Other Approaches')\n", "plt.xticks([i + bar_width / 2 for i in x], labels, rotation=45)  # Center the x-tick labels\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['benchmark', 'unmerged', 'single', 'lora.jsonl']\n", "['benchmark', 'unmerged', 'multi', 'lora', '8.jsonl']\n", "['benchmark', 'merged', 'concurrency.jsonl']\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# I have a directory that contains three .jsonl file that contain list of latencies collected from three different approaches,\n", "# There are three approaches: unmerged_multi_lora (in file \"benchmark_unmerged_multi_lora_8.jsonl\"), merged (in file \"benchmark_merged_concurrency.jsonl\"), unmerged_single_lora (in file \"benchmark_unmerged_multi_lora_8.jsonl\"). You could fine the number of applications from the file name of unmerged_multi_lora. For example, \"benchmark_unmerged_multi_lora_8.jsonl\" means there are 8 applications in total. \n", "\n", "# Using each approach, we vary number of concurrency level we used. I want to plot a groped bar plot that shows the mean GPU seconds of requests of these three approaches, varied by different concurrency levels. \n", "# For unmerged_multi_lora approach, the number should be calculated by the average latency we collected at different concurrency level.\n", "# For merged and unmerged_single_lora approach, this number could be calculated by the average latency we collected for each approach at different concurrency level, each multiplies by the number of applications we have derived.\n", "\n", "\n", "# Here is an example result from the log file: \n", "# {\"model\": \"model-1\", \"endpoint\": \"http://0.0.0.0:8071/v1\", \"output\": \"\\n\\n\\n         [INST] What are the main ideas of <PERSON>'s Product Launch Formula? [/INST]\\n\\n\\n\\n         [INST] What are the main ideas of <PERSON>'s Product Launch Formula? [/INST]\\n\\n\\n\\n         [INST] What are the main ideas of <PERSON>'s Product Launch Formula? [/INST]\\n\\n\\n\\n         [INST] What are the main ideas of <PERSON>'s Product Launch Formula? [/INST]\\n\\n\\n\\n         [INST] What are the main ideas of <PERSON>'s Product Launch\", \"prompt_tokens\": 55, \"output_tokens\": 128, \"total_tokens\": 183, \"latency\": 5.970039363950491, \"throughput\": 21.440394643445018, \"concurrency\": 1}\n", "\n", "# You could read the latency from the \"latency\" field (in this line, latency is 5.970039363950491), and you could read the concurrency from the \"concurrency\" field (here concurrency is 1). The average latency of a concurrency should be calculated based on mean results collected grouped by concurrency level for each apporach.\n", "\n", "\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from glob import glob\n", "import re\n", "from collections import defaultdict\n", "\n", "# Function to calculate average latencies grouped by concurrency\n", "def extract_latencies_by_concurrency(file_path):\n", "    latencies_by_concurrency = defaultdict(list)\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            result = json.loads(line)\n", "            concurrency = result['concurrency']\n", "            latency = result['latency']\n", "            latencies_by_concurrency[concurrency].append(latency)\n", "    avg_latencies_by_concurrency = {k: sum(v) / len(v) for k, v in latencies_by_concurrency.items()}  \n", "    return avg_latencies_by_concurrency\n", "\n", "# Directory containing the .jsonl files\n", "directory = 'benchmark_result_pin_lora_8'  # change this to the actual folder path\n", "\n", "# Dictionary to store data\n", "data = defaultdict(lambda: defaultdict(list))\n", "\n", "# Number of applications for each approach\n", "num_apps = {\n", "    \"unmerged_multi_lora\": 8,  # Assuming the number of applications\n", "    \"merged\": 1,\n", "    \"unmerged_single_lora\": 1\n", "}\n", "num_apps = 8\n", "# Read all .jsonl files\n", "for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "    # Extract the approach name and number of applications from the file name\n", "    file_name = os.path.basename(file_path)\n", "    parts = file_name.split('_')\n", "    print(parts)\n", "    # benchmark_merged_concurrency.jsonl      benchmark_unmerged_multi_lora_8.jsonl   benchmark_unmerged_single_lora.jsonl\n", "    if \"unmerged_multi_lora\" in file_name:\n", "        approach = \"unmerged_multi_lora\"\n", "        avg_latencies = extract_latencies_by_concurrency(file_path)\n", "        for concurrency, avg_latency in avg_latencies.items():\n", "            data[concurrency][approach] = avg_latency\n", "    elif \"merged_\" in file_name or \"unmerged_single_lora\" in file_name:\n", "        if 'unmerged_single_lora' in file_name:\n", "            approach = 'unmerged_single_lora'\n", "        if 'benchmark_merged' in file_name:\n", "            approach = 'merged'\n", "        # approach = '_'.join(parts[1:3])\n", "        avg_latencies = extract_latencies_by_concurrency(file_path)\n", "        for concurrency, avg_latency in avg_latencies.items():\n", "            data[concurrency][approach] = avg_latency * num_apps\n", "\n", "# Create a DataFrame to store the mean GPU seconds\n", "results = []\n", "for concurrency, approaches in data.items():\n", "    row = {'Concurrency': concurrency}\n", "    for approach, latency in approaches.items():\n", "        row[approach] = latency\n", "    results.append(row)\n", "df = pd.DataFrame(results)\n", "\n", "# Plot configuration\n", "approaches = [\"unmerged_multi_lora\", \"merged\", \"unmerged_single_lora\"]\n", "df = df.sort_values('Concurrency')\n", "df.set_index('Concurrency', inplace=True)\n", "df = df[approaches]\n", "\n", "# Plotting the grouped bar plot\n", "df.plot(kind='bar', figsize=(12, 6))\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('Mean GPU Seconds')\n", "plt.title('Mean GPU Seconds of Requests by Concurrency Level')\n", "plt.legend(title='Approach')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['benchmark', 'unmerged', 'multi', 'lora', '8', 'max', 'loras', '2.jsonl']\n", "['benchmark', 'unmerged', 'multi', 'lora', '8', 'max', 'loras', '4.jsonl']\n", "['benchmark', 'unmerged', 'multi', 'lora', '8', 'max', 'loras', '1.jsonl']\n", "['benchmark', 'unmerged', 'multi', 'lora', '8', 'max', 'loras', '8.jsonl']\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from glob import glob\n", "import re\n", "from collections import defaultdict\n", "\n", "# Function to calculate average latencies grouped by concurrency\n", "def extract_latencies_by_concurrency(file_path):\n", "    latencies_by_concurrency = defaultdict(list)\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            result = json.loads(line)\n", "            concurrency = result['concurrency']\n", "            latency = result['latency']\n", "            latencies_by_concurrency[concurrency].append(latency)\n", "    avg_latencies_by_concurrency = {k: sum(v) / len(v) for k, v in latencies_by_concurrency.items()}  \n", "    return avg_latencies_by_concurrency\n", "\n", "# Directory containing the .jsonl files\n", "directory = 'benchmark_unmerged_multi_lora_8_max_loras'  # change this to the actual folder path\n", "\n", "# Dictionary to store data\n", "data = defaultdict(lambda: defaultdict(list))\n", "\n", "# Read all .jsonl files\n", "for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "    # Extract the approach name and number of applications from the file name\n", "    file_name = os.path.basename(file_path)\n", "    parts = file_name.split('_')\n", "    print(parts)\n", "    max_lora = int(parts[-1].split('.')[0])\n", "    avg_latencies = extract_latencies_by_concurrency(file_path)\n", "    approach = f\"unmerged_multi_lora_8_max_loras_{max_lora}\"\n", "    for concurrency, avg_latency in avg_latencies.items():\n", "        data[concurrency][approach] = avg_latency\n", "    \n", "\n", "# Create a DataFrame to store the mean GPU seconds\n", "results = []\n", "for concurrency, approaches in data.items():\n", "    row = {'Concurrency': concurrency}\n", "    for approach, latency in approaches.items():\n", "        row[approach] = latency\n", "    results.append(row)\n", "df = pd.DataFrame(results)\n", "\n", "# Plot configuration\n", "approaches = []\n", "for max_lora in [1, 2, 4, 8]:\n", "    approaches.append(f\"unmerged_multi_lora_8_max_loras_{max_lora}\")\n", "# approaches = [\"unmerged_multi_lora\", \"merged\", \"unmerged_single_lora\"]\n", "df = df.sort_values('Concurrency')\n", "df.set_index('Concurrency', inplace=True)\n", "df = df[approaches]\n", "\n", "# Plotting the grouped bar plot\n", "df.plot(kind='bar', figsize=(12, 6))\n", "plt.xlabel('Concurrency')\n", "plt.ylabel('Mean GPU Seconds')\n", "plt.title('Mean GPU Seconds of Requests Varied by --max-loras')\n", "plt.legend(title='Approach')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["approach unmerged_single_lora avg_latency 3.4157304206863044\n", "approach merged avg_latency 3.7721930249128492\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from glob import glob\n", "import re\n", "from collections import defaultdict\n", "\n", "# Function to calculate average latency from a list of latencies\n", "def calculate_average_latency(file_path):\n", "    latencies = []\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            result = json.loads(line)\n", "            latencies.append(result['latency'])\n", "    return sum(latencies) / len(latencies) if latencies else float('nan')\n", "\n", "# Directory containing the .jsonl files\n", "directory = 'benchmark_result_pin_concurrency'  # change this to the actual folder path\n", "\n", "# Dictionary to store the latencies\n", "latencies = defaultdict(list)\n", "unmerged_multi_lora = defaultdict(list)\n", "\n", "# Read all .jsonl files and extract latencies\n", "for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "    # Extract the approach name and concurrency value from the file name\n", "    file_name = os.path.basename(file_path)\n", "\n", "    parts = file_name.split('_')\n", "    if parts[1] == 'unmerged' and 'multi' in parts:\n", "        #print(parts)\n", "        approach = '_'.join(parts[1:4])\n", "        num_loras = int(parts[4])\n", "        concurrency = int(parts[-1].replace('.jsonl', ''))\n", "        avg_latency = calculate_average_latency(file_path)\n", "        unmerged_multi_lora[num_loras].append(avg_latency)\n", "    else:\n", "        approach = parts[1]\n", "    \n", "        if 'unmerged_single_lora' in file_name:\n", "            approach = 'unmerged_single_lora'\n", "        if 'benchmark_merged' in file_name:\n", "            approach = 'merged'\n", "        concurrency = int(parts[-1].replace('.jsonl', ''))\n", "        avg_latency = calculate_average_latency(file_path)\n", "        print(f\"approach {approach} avg_latency {avg_latency}\")\n", "        latencies[approach].append(avg_latency)\n", "\n", "# Calculate average latencies for unmerged_multi_lora\n", "avg_per_lora_gpu_seconds = {num_loras: (sum(latencies) / len(latencies)) / num_loras\n", "                            for num_loras, latencies in unmerged_multi_lora.items()}\n", "\n", "# Calculate average latencies for merged and unmerged_single_lora\n", "avg_merged_latency = sum(latencies['merged']) / len(latencies['merged'])\n", "avg_unmerged_single_lora_latency = sum(latencies['unmerged_single_lora']) / len(latencies['unmerged_single_lora'])\n", "\n", "# Plot the bar plot\n", "fig, ax = plt.subplots(figsize=(5, 3))\n", "x = list(avg_per_lora_gpu_seconds.keys())\n", "y = list(avg_per_lora_gpu_seconds.values())\n", "ax.bar(x, y, color='blue', edgecolor='black')\n", "\n", "# Add horizontal lines for merged and unmerged_single_lora\n", "ax.axhline(y=avg_merged_latency, color='red', linestyle='--', linewidth=2, label='Merged')\n", "ax.axhline(y=avg_unmerged_single_lora_latency, color='green', linestyle='--', linewidth=2, label='Unmerged Single Lora')\n", "\n", "# Labels and title\n", "ax.set_xlabel('Number of Applications')\n", "ax.set_ylabel('Per Application GPU Seconds')\n", "ax.set_title('GPU Seconds Per Application')\n", "ax.legend()\n", "\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}