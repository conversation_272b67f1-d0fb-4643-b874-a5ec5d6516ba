{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from io import StringIO\n", "\n", "# CSV data dictionary\n", "title = \"E2E Latency Distribution (seconds)\"\n", "csv_data_dict = {\n", "    4: \"\"\"Time,AIBrix,Round-<PERSON>\n", "    0,0.9,0.9\n", "    1,0.8,0.9\n", "    2,0.7,0.9\n", "    3,0.8,1.3\n", "    4,1.0,2\n", "    \"\"\",\n", "    8: \"\"\"Time,AIBrix,Round-<PERSON>\n", "    0,1.9,1.9\n", "    1,1.7,2.9\n", "    2,1.7,1.9\n", "    3,1.2,3\n", "    4,1.8,2\n", "    \"\"\",\n", "    16: \"\"\"Time,AIBrix,Round-<PERSON>\n", "    0,3.2,4\n", "    1,3.2,2.9\n", "    2,3,3.5\n", "    3,2.8,2.9\n", "    4,2.5,3.8\n", "    \"\"\",\n", "}\n", "\n", "# Step 1: Parse the CSV strings into DataFrames\n", "dfs = []\n", "for qps, csv_str in csv_data_dict.items():\n", "    df = pd.read_csv(StringIO(csv_str))\n", "    df['QPS'] = qps\n", "    dfs.append(df)\n", "\n", "# Step 2: <PERSON><PERSON><PERSON> the DataFrames\n", "combined_df = pd.concat(dfs)\n", "combined_df['QPS'] = combined_df['QPS'].astype(str)\n", "\n", "# Step 3: Melt the combined DataFrame for easier plotting\n", "melted_df = pd.melt(combined_df, id_vars=['Time', 'QPS'], value_vars=['AIBrix', 'Round-Robin'], var_name='Approach', value_name='Latency')\n", "\n", "# Step 4: Create grouped box plot for latency distribution\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(data=melted_df, x='QPS', y='Latency', hue='Approach')\n", "plt.xlabel('QPS')\n", "plt.ylabel('Latency (seconds)')\n", "plt.title(title)\n", "plt.legend(title='Approach')\n", "plt.grid(True)\n", "plt.show()\n", "\n", "# Step 5: Calculate median and 99th percentile latencies\n", "summary_df = combined_df.groupby(['QPS']).agg({\n", "    'AIBrix': ['median', lambda x: np.percentile(x, 99)],\n", "    'Round-Robin': ['median', lambda x: np.percentile(x, 99)]\n", "}).reset_index()\n", "summary_df.columns = ['QPS', 'AIBrix Median', 'AIBrix 99th Percentile', 'Round-<PERSON>', 'Round-<PERSON> 99th Percentile']\n", "\n", "# Create line plot for median and 99th percentile latencies\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Median plot\n", "plt.plot(summary_df['QPS'], summary_df['AIBrix Median'], marker='o', linestyle='-', label='AIBrix Median')\n", "plt.plot(summary_df['QPS'], summary_df['Round-<PERSON> Median'], marker='o', linestyle='-', label='Round-<PERSON> Median')\n", "\n", "# 99th Percentile plot\n", "plt.plot(summary_df['QPS'], summary_df['AIBrix 99th Percentile'], marker='o', linestyle='--', label='AIBrix 99th Percentile')\n", "plt.plot(summary_df['QPS'], summary_df['Round-<PERSON> 99th Percentile'], marker='o', linestyle='--', label='Round-<PERSON> 99th Percentile')\n", "\n", "plt.xlabel('QPS')\n", "plt.ylabel('Latency (seconds)')\n", "plt.title('Median and 99th Percentile Latency for Different Approaches')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAhgAAAE8CAYAAACCUcitAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy80BEi2AAAACXBIWXMAAA9hAAAPYQGoP6dpAABZq0lEQVR4nO3dd1gU1/s28HvpHUSlCiiKCghiTUCDGLvGaDRii0hsidiNRo0aRSVYYtRo7CgxSqwRv0ksoAkaa2zEArEiEBVQUarAws77hy/7c11ABndZgftzXVy6Z87MeXYYdp6dOeeMRBAEAUREREQqpKXpAIiIiKj6YYJBREREKscEg4iIiFSOCQYRERGpHBMMIiIiUjkmGERERKRyTDCIiIhI5ZhgEBERkcoxwSAiIiKVY4JBRKJJJBLMnz9f7e3ExMRAIpEgJiZGXubn54dmzZqpvW0AuHfvHiQSCcLDwyulvZL07NkTo0eP1lj7lSkwMBD169cXtc7hw4dhYmKCR48eqScoqjAmGKRy4eHhkEgkpf6cPXsWAPDkyRMsW7YMvr6+qFu3LiwsLPDuu+9i165dFd5maVR5Ujp9+jTmz5+PZ8+eqWR7mla/fn35ftTS0oKFhQU8PDwwZswYnDt3TmXtREREYOXKlSrbniq9rbGdOnUKUVFRmDFjhqZDeWt1794djRo1QmhoqKZDoVfoaDoAqr4WLFiABg0aKJU3atQIAHDmzBnMnj0bPXv2xJw5c6Cjo4N9+/Zh0KBBiIuLQ3BwsOhtVobTp08jODgYgYGBsLCwqLR21cnLywtffPEFACArKwvx8fHYs2cPNm3ahClTpuC7775TqP/8+XPo6Ij7+IiIiMC1a9cwefLkcq/j6+uL58+fQ09PT1RbYpUWm5OTE54/fw5dXV21tl+aZcuWoVOnTpV6fFdFn332GaZNm4bg4GCYmppqOhz6/5hgkNr06NEDrVu3LnW5u7s7bt26BScnJ3lZUFAQOnfujCVLluDLL7+EsbGxqG1Sxdjb2+OTTz5RKFuyZAmGDBmCFStWwMXFBWPHjpUvMzAwUGs8eXl50NPTg5aWltrbKotEItFY+2lpafj999+xfv16jbRflfTv3x8TJkzAnj17MGLECE2HQ/8fb5GQxjRo0EAhuQBefKD37dsX+fn5uHv3bqXGc+XKFQQGBsLZ2RkGBgawsbHBiBEj8OTJE3md+fPnY/r06fL4i28t3Lt3T15n+/btaNWqFQwNDWFpaYlBgwYhOTlZoa3iWzZxcXHo2LEjjIyMYG9vj6VLlyrFlZeXh/nz56Nx48YwMDCAra0t+vXrhzt37kAQBNSvXx99+vQpcT1zc3N89tlnFdofhoaG+Omnn2BpaYmQkBC8/ODlV/tgZGVlYfLkyahfvz709fVhZWWFLl264NKlS/L3+/vvvyMxMVG+z4rvtRf3s9i5cyfmzJkDe3t7GBkZITMzs8Q+GMUuXrwIHx8fGBoaokGDBkon4uLbai//bl5ur3ibZcVWWh+MP/74A++99x6MjY1hYWGBPn36ID4+XqHO/PnzIZFIcPv2bfnVLnNzc3z66afIzc197f7//fffUVhYiM6dOyuUS6VSBAcHw8XFBQYGBqhduzbat2+P6OhohXr//vsvPv74Y1haWsLAwACtW7fG//73P6V2nj17hilTpsh/d/Xq1UNAQAAeP34sr5OWloaRI0fC2toaBgYGaN68OX788UeF7RTvq2+//RYbN25Ew4YNoa+vjzZt2uD8+fNK7UZGRqJZs2YwMDBAs2bNsH///hL3w86dO9GqVSuYmprCzMwMHh4eWLVqlUIdKysreHp64sCBA2XvVKpUvIJBapORkaHwIQW8ODHVrl27zPVSUlIAAHXq1FHZNssjOjoad+/exaeffgobGxtcv34dGzduxPXr13H27FlIJBL069cPN2/exM8//4wVK1bIY6xbty4AICQkBHPnzoW/vz9GjRqFR48eYfXq1fD19cXly5cVbqk8ffoU3bt3R79+/eDv74+9e/dixowZ8PDwQI8ePQAARUVF+OCDD3Ds2DEMGjQIkyZNQlZWFqKjo3Ht2jU0bNgQn3zyCZYuXYr09HRYWlrKt//rr78iMzNT6cqEGCYmJvjoo48QFhaGuLg4uLu7l1jv888/x969ezF+/Hi4ubnhyZMnOHnyJOLj49GyZUvMnj0bGRkZ+O+//7BixQr5tl+2cOFC6OnpYdq0acjPzy/ztsjTp0/Rs2dP+Pv7Y/Dgwdi9ezfGjh0LPT090d9gyxPby44ePYoePXrA2dkZ8+fPx/Pnz7F69Wq0a9cOly5dUuqk6O/vjwYNGiA0NBSXLl3C5s2bYWVlhSVLlpQZ1+nTp1G7dm2lJHz+/PkIDQ3FqFGj0LZtW2RmZuLChQu4dOkSunTpAgC4fv062rVrB3t7e8ycORPGxsbYvXs3+vbti3379uGjjz4CAGRnZ+O9995DfHw8RowYgZYtW+Lx48f43//+h//++w916tTB8+fP4efnh9u3b2P8+PFo0KAB9uzZg8DAQDx79gyTJk1SiC8iIgJZWVn47LPPIJFIsHTpUvTr1w93796V32qKiopC//794ebmhtDQUDx58gSffvop6tWrp7Ct6OhoDB48GJ06dZLvr/j4eJw6dUqp3VatWiEyMrLMfUqVTCBSsa1btwoASvzR19cvc90nT54IVlZWwnvvvaeybQqCIHTo0EFwd3cvs05ubq5S2c8//ywAEE6cOCEvW7ZsmQBASEhIUKh77949QVtbWwgJCVEov3r1qqCjo6NQ3qFDBwGAsG3bNnlZfn6+YGNjI/Tv319etmXLFgGA8N133ynFJpPJBEEQhBs3bggAhHXr1iks//DDD4X69evL65XGyclJ6NWrV6nLV6xYIQAQDhw4IC8DIMybN0/+2tzcXBg3blyZ7fTq1UtwcnJSKv/zzz8FAIKzs7PS76B42Z9//ikvK953y5cvl5fl5+cLXl5egpWVlVBQUCAIwv8dM6/+nkraZmmxJSQkCACErVu3ysuK23ny5Im87J9//hG0tLSEgIAAedm8efMEAMKIESMUtvnRRx8JtWvXVmrrVe3btxdatWqlVN68efMyf1+CIAidOnUSPDw8hLy8PHmZTCYTfHx8BBcXF3nZ119/LQAQfvnlF6VtFB83K1euFAAI27dvly8rKCgQvL29BRMTEyEzM1MQhP/bV7Vr1xbS09PldQ8cOCAAEH799Vd5mZeXl2Brays8e/ZMXhYVFSUAUPg9TJo0STAzMxMKCwvLfL+CIAjffPONAEBITU19bV2qHLxFQmrzww8/IDo6WuHn0KFDpdaXyWQYOnQonj17htWrV6tkm2IYGhrK/5+Xl4fHjx/j3XffBQD5pf6y/PLLL5DJZPD398fjx4/lPzY2NnBxccGff/6pUN/ExETh6oKenh7atm2rcGto3759qFOnDiZMmKDUnkQiAQA0btwY77zzDnbs2CFflp6ejkOHDmHo0KHyehVV/G0+Kyur1DoWFhY4d+4cHjx4UOF2hg8frvA7KIuOjo7CrR89PT189tlnSEtLw8WLFyscw+s8fPgQsbGxCAwMVLha5OnpiS5duuDgwYNK63z++ecKr9977z08efIEmZmZZbb15MkT1KpVS6ncwsIC169fx61bt0pcLz09HX/88Qf8/f2RlZUlPw6fPHmCbt264datW7h//z6AF8dX8+bN5Vc0XlZ83Bw8eBA2NjYYPHiwfJmuri4mTpyI7OxsHD9+XGG9gQMHKsT93nvvAYD8uC7eh8OHD4e5ubm8XpcuXeDm5qb0XnNycpRu/5SkuM1Xr3CS5vAWCalN27ZtRXXInDBhAg4fPoxt27ahefPmKtmmGOnp6QgODsbOnTuRlpamsCwjI+O169+6dQuCIMDFxaXE5a+ORKhXr57Syb9WrVq4cuWK/PWdO3fQpEmT147YCAgIwPjx45GYmAgnJyfs2bMHUqkUw4YNe23cr5OdnQ0AZfbOX7p0KYYPHw4HBwe0atUKPXv2REBAAJydncvdTkmjg0pjZ2en1AG4cePGAF70BShODFUtMTERANCkSROlZa6urjhy5AhycnIUYnN0dFSoV3wifPr0KczMzMpsT3ip30uxBQsWoE+fPmjcuDGaNWuG7t27Y9iwYfD09AQA3L59G4IgYO7cuZg7d26J201LS4O9vT3u3LmD/v37lxlDYmIiXFxcoKWl+H3U1dVVvvxlZb3fl+uX9HfSpEkThWQ+KCgIu3fvRo8ePWBvb4+uXbvC398f3bt3V1q3eF+9aUJNqsMEg94KwcHBWLt2LRYvXqySk2JF+Pv74/Tp05g+fTq8vLxgYmICmUyG7t27QyaTvXZ9mUwGiUSCQ4cOQVtbW2n5q/f1S6oDlHxSeZ1BgwZhypQp2LFjB7766its374drVu3LvFEKNa1a9cAlD0U2N/fH++99x7279+PqKgoLFu2DEuWLMEvv/wi70/yOuW9elFepZ1oioqKVNrO61T091y7dm35Sfllvr6+uHPnDg4cOICoqChs3rwZK1aswPr16zFq1Cj5sTpt2jR069atxG2rc9irKo9rKysrxMbG4siRIzh06BAOHTqErVu3IiAgQKmTafG+KqnvFmkGEwzSuB9++AHz58/H5MmTNTah0NOnT3Hs2DEEBwfj66+/lpeXdBm6tBNXw4YNIQgCGjRoIP82/aYaNmyIc+fOQSqVljkXg6WlJXr16oUdO3Zg6NChOHXqlEomjsrOzsb+/fvh4OAg/8ZaGltbWwQFBSEoKAhpaWlo2bIlQkJC5AmGKr9ZPnjwQOlKwc2bNwFA3smy+JvzqxOivfqNW0xsxR0ub9y4obTs33//RZ06dZSurFRU06ZNsW/fvhKXWVpa4tNPP8Wnn36K7Oxs+Pr6Yv78+Rg1apT8qpGurq7SCJRXNWzYUJ5AlsbJyQlXrlyBTCZTuIrx77//ypeLUVy/pL+tkvarnp4eevfujd69e0MmkyEoKAgbNmzA3LlzFRKlhIQE1KlTR97hmjSPfTBIo3bt2oWJEydi6NChSpM5Vabib12vfssq6SRdfAJ59cTVr18/aGtrIzg4WGk7giAoDHctr/79++Px48dYs2aN0rJX2xg2bBji4uIwffp0aGtrY9CgQaLbe9nz588xbNgwpKenY/bs2WVeEXj1FpKVlRXs7OyQn58vLzM2Ni7XrabyKCwsxIYNG+SvCwoKsGHDBtStWxetWrUC8OLkCQAnTpxQiHXjxo1K2ytvbLa2tvDy8sKPP/6o8Pu/du0aoqKi0LNnz4q+JSXe3t54+vSp0nDtV48jExMTNGrUSL6vrays4Ofnhw0bNuDhw4dK2315Su3+/fvjn3/+KXGIaPHx1bNnT6SkpCjMsFtYWIjVq1fDxMQEHTp0EPW+Xt6HL+/z6OhoxMXFlfletbS05LeCXj62gBfDlr29vUXFQurFKxikNocOHZJ/y3mZj48PnJ2d8ffffyMgIAC1a9dGp06dFDopvlxPzDbL8ujRIyxatEipvEGDBhg6dCh8fX2xdOlSSKVS2NvbIyoqCgkJCUr1i09gs2fPxqBBg6Crq4vevXujYcOGWLRoEWbNmoV79+6hb9++MDU1RUJCAvbv348xY8Zg2rRpZcb4qoCAAGzbtg1Tp07F33//jffeew85OTk4evQogoKCFOa/6NWrF2rXro09e/agR48esLKyKnc79+/fx/bt2wG8uGoRFxeHPXv2ICUlBV988UWZc2lkZWWhXr16+Pjjj9G8eXOYmJjg6NGjOH/+PJYvX66w33bt2oWpU6eiTZs2MDExQe/evUXtj2J2dnZYsmQJ7t27h8aNG2PXrl2IjY3Fxo0b5Vd63N3d8e6772LWrFnyIbw7d+5EYWGh0vbExLZs2TL06NED3t7eGDlypHyYqrm5uUqfz9KrVy/o6Ojg6NGjGDNmjLzczc0Nfn5+aNWqFSwtLXHhwgX5EOFiP/zwA9q3bw8PDw+MHj0azs7OSE1NxZkzZ/Dff//hn3/+AQBMnz4de/fuxYABAzBixAi0atUK6enp+N///of169ejefPmGDNmDDZs2IDAwEBcvHgR9evXx969e+VXySoyc2ZoaCh69eqF9u3bY8SIEUhPT8fq1avh7u4u7/MDAKNGjUJ6ejref/991KtXD4mJiVi9ejW8vLwUrqilpaXhypUrGDduXEV2NamLRsauULVW1pBSvDTkr7z1xNYtSfHQxpJ+OnXqJAiCIPz333/CRx99JFhYWAjm5ubCgAEDhAcPHigNyRQEQVi4cKFgb28vaGlpKQ2F3Ldvn9C+fXvB2NhYMDY2Fpo2bSqMGzdOuHHjhkI8JQ2bHT58uNJwydzcXGH27NlCgwYNBF1dXcHGxkb4+OOPhTt37iitHxQUJAAQIiIiytwfL3NycpLvC4lEIpiZmQnu7u7C6NGjhXPnzpW4zsv7JD8/X5g+fbrQvHlzwdTUVDA2NhaaN28urF27VmGd7OxsYciQIYKFhYXCcMTiYaN79uxRaqe0Yaru7u7ChQsXBG9vb8HAwEBwcnIS1qxZo7T+nTt3hM6dOwv6+vqCtbW18NVXXwnR0dFK2ywttpKGqQqCIBw9elRo166dYGhoKJiZmQm9e/cW4uLiFOoUD1N99OiRQnlpw2dL8uGHH8qPz2KLFi0S2rZtK1hYWAiGhoZC06ZNhZCQEPnw3Jffe0BAgGBjYyPo6uoK9vb2wgcffCDs3btXod6TJ0+E8ePHC/b29oKenp5Qr149Yfjw4cLjx4/ldVJTU4VPP/1UqFOnjqCnpyd4eHgo7ZPifbVs2TKl91HS39C+ffsEV1dXQV9fX3BzcxN++eUXpeN/7969QteuXQUrKytBT09PcHR0FD777DPh4cOHCttat26dYGRkJB8yS28HiSBUoOcNEb2VpkyZgrCwMKSkpMDIyEjT4dAb+uuvv+Dn54d///231NFJBLRo0QJ+fn7yidLo7cAEg6iayMvLg4ODAz744ANs3bpV0+GQivTo0QP16tXDpk2bNB3KW+nw4cP4+OOPcffuXVG3BUn9mGAQVXFpaWk4evQo9u7di8jISFy6dAleXl6aDouIajh28iSq4uLi4jB06FBYWVnh+++/Z3JBRG8FXsEgIiIileM8GERERKRyTDCIiIhI5WpcHwyZTIYHDx7A1NSUD8UhIiISQRAEZGVlwc7OTukBeK+qcQnGgwcP4ODgoOkwiIiIqqzk5GTUq1evzDo1LsEontY2OTn5tY9Kpv8jlUoRFRWFrl27lvnQLaI3xWONKguPNfEyMzPh4OBQrinia1yCUXxbxMzMjAmGCFKpFEZGRjAzM+MfIqkVjzWqLDzWKq48XQzYyZOIiIhUjgkGERERqRwTDCIiIlK5t6YPxuLFizFr1ixMmjQJK1euLLXenj17MHfuXNy7dw8uLi5YsmQJevbsqdJYBEFAYWEhioqKVLrdqkwqlUJHRwd5eXk1er9oa2tDR0eHQ5yJiF7jrUgwzp8/jw0bNsDT07PMeqdPn8bgwYMRGhqKDz74ABEREejbty8uXbqEZs2aqSSWgoICPHz4ELm5uSrZXnUhCAJsbGyQnJxc40+uRkZGsLW1hZ6enqZDISJ6a2k8wcjOzsbQoUOxadMmLFq0qMy6q1atQvfu3TF9+nQAwMKFCxEdHY01a9Zg/fr1bxyLTCZDQkICtLW1YWdnBz09vRp/Mi0mk8mQnZ0NExOT106uUl0JgoCCggI8evQICQkJcHFxqbH7gojodTSeYIwbNw69evVC586dX5tgnDlzBlOnTlUo69atGyIjI0tdJz8/H/n5+fLXmZmZAF5c8pdKpUp1i4qKYG9vDyMjI5HvpHorPrnq6+vX6KRLX18f2traSEpKQm5uLvT19TUdUrVT/Hf56t8nkaq9ybGWl5eH5ORkVYckioODAwwMDCq1TTH7SqMJxs6dO3Hp0iWcP3++XPVTUlJgbW2tUGZtbY2UlJRS1wkNDUVwcLBSeVRUlFISoaOjAxsbG+Tm5qKwsLBcMdU0WVlZmg5B4woKCvD8+XMcP36cx4kaRUdHazoEqiEqcqylpqYiIiJCDdGU35AhQ5TOieompvuAxhKM5ORkTJo0CdHR0WrNwGbNmqVw1aN4FrKuXbsqTbRVnJGamJhUelb4tiuef57PcHlxnBgaGsLX15fHiRpIpVJER0ejS5cunPyI1OpNjrW8vDy0b9++wm0nJSVh8eLFmDlzJhwdHSu0DU1cwSi+C1AeGkswLl68iLS0NLRs2VJeVlRUhBMnTmDNmjXIz8+Htra2wjo2NjZITU1VKEtNTYWNjU2p7ejr65d4GVtXV1fpgCoqKoJEIoGWlhbvrb9CJpMBgHz/1GRaWlqQSCQlHkOkOty/VFkqcqzp6urCzc2twm3q6Lw4/To7O6Nx48YV3k5lE7OfNHam6NSpE65evYrY2Fj5T+vWrTF06FDExsYqJRcA4O3tjWPHjimURUdHw9vbu7LCJjWIiYmBRCLBs2fPNB0KERGpiMYSDFNTUzRr1kzhx9jYGLVr15YPOQ0ICMCsWbPk60yaNAmHDx/G8uXL8e+//2L+/Pm4cOECxo8fr6m3UanOnDkDbW1t9OrVS9OhEBERlemtvtadlJSEhw8fyl/7+PggIiICGzduRPPmzbF3715ERkaqbA6Mt11YWBgmTJiAEydO4MGDB2pvr6CgQO1tEBFR9fRWJRgxMTEKs3jGxMQgPDxcoc6AAQNw48YN5Ofn49q1ayqfxfNtlZ2djV27dmHs2LHo1auXwn4pvsXw+++/w9PTEwYGBnj33Xdx7do1eZ3w8HBYWFggMjISLi4uMDAwQLdu3RSGWc2fPx9eXl7YvHkzGjRoIO88lJSUhL59+6JevXqwsLCAv7+/Ql+YO3fuoE+fPrC2toaJiQnatGmDo0ePKsSfn5+PGTNmwMHBAfr6+mjUqBHCwsIU6ly8eBGtW7eGkZERfHx8cOPGDVXuQiIiqkRvVYJBpdu9ezeaNm2KJk2a4JNPPsGWLVsgCIJCnenTp2P58uU4f/486tati969eyuMWc7NzUVISAi2bduGU6dO4dmzZxg0aJDCNm7fvo19+/bhl19+QWxsLGQyGfr06YOnT5/it99+w5EjR3D37l0MHDhQvk52djZ69uyJY8eO4fLly+jevTt69+6NpKQkeZ2AgAD8/PPP+P777xEfH48NGzbAxMREoe3Zs2dj+fLluHDhAnR0dDBixAhV7kIiIqpMQg2TkZEhABAyMjKUlj1//lyIi4sTnj9/roHIyubj4yOsXLlSEARBkEqlQp06dYQ///xTEARB+PPPPwUAws6dO+X1nzx5IhgaGgq7du0SBEEQtm7dKgAQzp49K68THx8vABDOnTsnCIIgzJs3T9DV1RXS0tLkdaKiogRtbW3h3r17wtOnT4WioiLh+vXrAgDh77//LjVed3d3YfXq1YIgCMKNGzcEAEJ0dHSJdYvjP3r0qLzs999/FwC8lb+Lt/k4qQ4KCgqEyMhIoaCgQNOhUDWnyWPtxo0bQocOHYQbN25Uettvoqxz6Kt4BaMKuHHjBv7++28MHjwYwIvhTQMHDlS6xfDyaBpLS0s0adIE8fHx8jIdHR20adNG/rpp06awsLBQqOPk5IS6devKX8fHx8PBwQEODg7yMjc3N4X1srOzMW3aNLi6usLCwgImJiaIj4+XX8EoHhXUoUOHMt/ny8+isbW1BQCkpaW9Zu8QEdHbSONThdPrhYWFobCwEHZ2dvIyQRCgr6+PNWvWqLQtY2Nj0etMmzYN0dHR+Pbbb9GoUSMYGhri448/lncSNTQ0LNd2Xh5fXTyZV/H8G0REVLXwCsZbrrCwENu2bcPy5csV5gz5559/YGdnh59//lle9+zZs/L/P336FDdv3oSrq6vCti5cuCB/fePGDTx79kyhzqtcXV2RnJys0Bk0Li4Oz549k08yc+rUKQQGBuKjjz6Ch4cHbGxscO/ePXl9Dw8PyGQyHD9+/I32BRERVR28gvGW++233/D06VOMHDkS5ubmCsv69++PsLAwLFu2DACwYMEC1K5dG9bW1pg9ezbq1KmDvn37yuvr6upiwoQJ+P7776Gjo4Px48fj3XffRdu2bUttv3PnzvDw8MCwYcOwcOFC6OvrY/z48ejQoQNat24NAHBxccEvv/yC3r17QyKRYO7cuQpXHurXr4/hw4djxIgR+P7779G8eXMkJiYiLS0N/v7+KtxbRETiZGZm4tatW/KZNStLYmKiwr+VzdzcXO3PMWGC8ZYLCwtD586dlZIL4EWCsXTpUly5cgUAsHjxYkyaNAm3bt2Cl5cXfv31V+jp6cnrGxkZYcaMGRgyZAju37+P9957T6kfx6skEgkOHDiA8ePHo1evXtDS0kL37t2xevVqeZ3vvvsOI0aMgI+PD+rUqYMZM2YozVe/bt06fPXVVwgKCsKTJ0/g6OiIr7766k12DRHRG0lLS8O2H8Nf+zmoTiEhIRppV19PF9t+2q7WJEMiCK+MdazmMjMzYW5ujoyMjBIfdpaQkKAwB0RVEBMTg44dO+Lp06ewsLAosU54eDgmT55c4em4ZTIZMjMzYWZmVuOfRVJVj5OqQiqV4uDBg+jZsyefRUJqFRcXh6CgIHzulgU74yJNh1NpHuRoY32cKTZu3Cj6OShlnUNfxSsYRERUo9kZF6G+ac1JMCpLzf4qSkRERGrBBKMa8PPzgyAIpd4eAYDAwEA+rZSIiCoNEwwiIiJSOSYYREREpHJMMIiIiEjlmGAQERGRyjHBICIiIpXjPBjllJqaioyMjEprrzKmcSUiIlIXJhjlkJqaik+GBUBakF9pberq6WP7T9ve2iSjfv36mDx5MiZPnqzpUIiI6C3EBKMcMjIyIC3Ix3PnDpAZKD8TRNW08jKAu8eRkZEhOsE4c+YM2rdvj+7du+P333+Xl9+7dw8NGjTA5cuX4eXlJX9dTFdXF46OjggMDMTs2bPlj0svzfnz5yv0aHciIqoZmGCIIDMwh8y4jqbDKFNYWBgmTJiAsLAwPHjwAHZ2dmXWP3r0KNzd3ZGfn4+TJ09i1KhRsLW1xciRI0usX1BQAAMDA9StW1cd4RMRUTXBTp7VSHZ2Nnbt2oWxY8eiV69eCA8Pf+06tWvXho2NDZycnDB06FC0a9cOly5dki8PDAxE37598c0338DV1RWurq4AXtwiWblyJYAXD1vT09PDX3/9JV9v6dKlsLKyQmpqqkrfIxERVQ0aTTDWrVsHT09PmJmZwczMDN7e3jh06FCp9cPDwyGRSBR++DTL/7N79240bdoUTZo0wSeffIItW7ZAzMNyL1y4gIsXL+Kdd95RKD927Bhu3LiBX375Bf/73/+U1vPz88PkyZMxbNgwZGRk4PLly5g7dy42b9781vYhISIi9dLoLZJ69eph8eLFcHFxgSAI+PHHH9GnTx9cvnwZ7u7uJa5jZmaGGzduyF+/rq9ATRIWFoZPPvkEANC9e3dkZGTg+PHj8PPzK3UdHx8faGlpoaCgAFKpFGPGjEFAQIBCHWNjY2zatAl5eXmlPp530aJFiI6OxpgxY3Dt2jUMHz4cH374ocreGxERVS0aTTB69+6t8DokJATr1q3D2bNnS00wJBIJbGxsKiO8KuXGjRv4+++/sX//fgCAjo4OBg4ciLCwsDITjF27dsHV1RVSqRTXrl3DhAkTUKtWLSxevFhex8PDA3p6esjLyyt1O3p6etixYwc8PT3h5OSEFStWqOy9ERFR1fPWdPIsKirCnj17kJOTA29v71LrZWdnw8nJCTKZDC1btsQ333xTajICAPn5+cjP/7/hpZmZmQAAqVQKqVSqUFcqlUIQBMhkMshkMnn5y/+vTK/GUZbNmzejsLBQoVOnIAjQ19fH999/L99O8TaLX9vb28PZ2RkA0KRJE9y+fRtff/01vv76axgYGEAQBBgZGclvtRTvn1f/DwAnT54EAKSnp+Px48cwNDR8wz3wdpLJZBAEAVKpFNra2poOp9op/rt89e+TSNUKCwsBAA9yatbfcfH7LSwsFP13Jqa+xhOMq1evwtvbG3l5eTAxMcH+/fvh5uZWYt0mTZpgy5Yt8PT0REZGBr799lv4+Pjg+vXrqFevXonrhIaGIjg4WKk8KioKRkZGCmU6OjqwsbFBdnY2CgoK5OU5OTlv8A4rLicnR54QlaWwsBDbtm3DokWL0LFjR4Vln3zyCbZu3YrOnTsrbDM7O7vENqRSKQoLC/H48WOYmZnJX2dlZQGA/F+ZTIa8vDz5ugkJCfjiiy+watUq7N+/H8OGDUNkZCS0tKpfP+KCggI8f/4cJ06ckH9AkepFR0drOgSq5oo7oa+PM9VwJJpx8uRJ3Lp1S9Q6ubm55a6r8QSjSZMmiI2NRUZGBvbu3Yvhw4fj+PHjJSYZ3t7eClc3fHx84Orqig0bNmDhwoUlbn/WrFmYOnWq/HVmZiYcHBzQtWtXpf4EeXl5SE5OhomJiULn0eL5HrTyKmcmz+J2jI2NS+3z8LLIyEg8e/YMQUFBMDdXnKfj448/xs8//4y+ffsqbNPExATAi/ecm5uLwsJCXL16FRs3bkTHjh3lCZuuri50dHRgamqKrKwsmJqaQiKRQEtLCwYGBjAzM0NRURGCgoLQtWtXjB07Fn379kXz5s2xefNmTJs2TYV75u2Ql5cHQ0ND+Pr6spOxGkilUkRHR6NLly7Q1dXVdDhUjcXHxyMiIgKfu2XBzrhI0+FUmgc52lgfZ4r27dvDxcVF1Lrl+dJbTOMJhp6eHho1agQAaNWqFc6fP49Vq1Zhw4YNr11XV1cXLVq0wO3bt0uto6+vD319/RLXffXDq6ioSH7yfPmbd61ataCrpw/cPV7et/XGdPX0UatWrXJdASi+QlGrVi2lZR9//DGWLVsmv2JR/N6Kt9u1a1cAgLa2NmxtbdGzZ0+EhITIl788Yqf49cvLtLS0sGjRIiQmJuK3336DlpYW7O3tsXHjRgwePBjdunVD8+bN33yHvEW0tLQgkUhKPIZIdbh/Sd10dF6cAu2Mi1DftOYkGMV0dHRE/42Jqa/xBONVMplMoc9EWYqKinD16lX07NlTrTFZW1tj+0/b3tpnkfz666+lLmvbtq1C/4li9evXL9cQ1uK5NF7tC3Lv3j35/4v7bLysX79+5f49EhFR9aPRBGPWrFno0aMHHB0dkZWVhYiICMTExODIkSMAgICAANjb2yM0NBQAsGDBArz77rto1KgRnj17hmXLliExMRGjRo1Se6zW1tac04GIiKicNJpgpKWlISAgAA8fPoS5uTk8PT1x5MgRdOnSBQCQlJSkcIvg6dOnGD16NFJSUlCrVi20atUKp0+fLrVTKBEREWmGRhOMsLCwMpfHxMQovF6xYgXnVyAiIqoCqt8YQiIiItI4JhhERESkckwwiIiISOWYYBAREZHKMcEgIiIilRM9iiQhIQF//fUXEhMTkZubi7p166JFixbw9vau1tMmp6amvrUTbREREb1typ1g7NixA6tWrcKFCxdgbW0NOzs7GBoaIj09HXfu3IGBgQGGDh2KGTNmwMnJSZ0xV7rU1FQEDPsE+QWV93RHfT1dbPtpe7VIMvz8/ODl5YWVK1eqtZ358+cjMjISsbGxpdYJDAzEs2fPEBkZqdZYiIhqunIlGC1atICenh4CAwOxb98+ODg4KCzPz8/HmTNnsHPnTrRu3Rpr167FgAED1BKwJmRkZCC/QFppD8QpfhBNRkZGuROMwMBA/PjjjwBezC9fr149DBgwAAsWLKgSV5bq16+PxMREAIChoSEaNmyISZMmqXyW1lWrVpVrinQiInoz5UowFi9ejG7dupW6XF9fH35+fvDz80NISIjCcyqqk7f9gTjdu3fH1q1bIZVKcfHiRQwfPhwSiQRLlizRdGjlsmDBAowePRq5ubnYs2cPRo8eDXt7e/To0UNlbbz6tFkiIlKPcnXyLCu5eFXt2rXRqlWrCgdEFaevrw8bGxs4ODigb9++6Ny5M6KjowG8uMo0ceJEWFlZwcDAAO3bt8f58+fl64aHh8PCwkJhe5GRkfKnqAIvEs2WLVvip59+Qv369WFubo5BgwYhKytLXicnJwcBAQEwMTGBra0tli9fXu74TU1NYWNjA2dnZ8yYMQOWlpby+IEXU8f36dMHJiYmMDMzg7+/P1JTU5W2s2HDBjg4OMDIyAj+/v4KfWcCAwPlj64HXty+mThxIr788ktYWlrCxsYG8+fPL3fMRERUMtGjSC5duoSrV6/KXx84cAB9+/bFV199hYKCApUGRxV37do1nD59Gnp6egCAL7/8Evv27cOPP/6IS5cuoVGjRujWrRvS09NFbffOnTuIjIzEb7/9ht9++w3Hjx/H4sWL5cunT5+O48eP48CBA4iKikJMTAwuXbokqg2ZTIZ9+/bh6dOn8vhlMhn69OmD9PR0HD9+HNHR0bh79y4GDhyosO7t27exe/du/Prrrzh8+DAuX76MoKCgMtv78ccfYWxsjHPnzmHp0qVYsGCBQmJDRETiiR5F8tlnn2HmzJnw8PDA3bt3MWjQIHz00UfYs2cPcnNz1d6Rj0r322+/wcTEBIWFhcjPz4eWlhbWrFmDnJwcrFu3DuHh4fLbDZs2bUJ0dDTCwsIwffr0crchk8kQHh4OU1NTAMCwYcNw7NgxhISEIDs7G2FhYdi+fTs6deoE4MXJu169euXa9owZMzBnzhzk5+ejsLAQlpaW8j4Yx44dw9WrV5GQkCDvA7Rt2za4u7vj/PnzaNOmDQAgLy8P27Ztg729PQBg9erV6NWrF5YvXw4bG5sS2/X09MS8efMAAC4uLlizZg2OHTsmf+geUU1Q0ZFy+fn5SElJUUNE5WdjYwN9fX3R6yUlJakhGiomOsG4efMmvLy8AAB79uyBr68vIiIicOrUKQwaNIgJhgZ17NgR69atQ05ODlasWAEdHR30798fV65cgVQqRbt27eR1dXV10bZtW8THx4tqo379+vLkAgBsbW2RlpYG4MXVjYKCArzzzjvy5ZaWlmjSpIn89TfffINvvvlG/jouLg6Ojo4AXlz9CAwMxMOHDzF9+nQEBQWhUaNGAID4+Hg4ODgodDB2c3ODhYUF4uPj5QmGo6OjPLkAAG9vb8hkMty4caPMBONlL78nopogNTUVnwwLgLQgX9OhUDUiOsEQBAEymQwAcPToUXzwwQcAAAcHBzx+/Fi10ZEoxsbG8hPyli1b0Lx5c4SFhclPvmXR0tJSGl0hlSoPy9XV1VV4LZFI5MdDeXz++efw9/eXv7azs5P/v06dOmjUqBEaNWqEPXv2wMPDA61bt4abm1u5t18Rb/qeiKq6jIwMSAvy8dy5A2QGIjtCywqhlZ+tnsDKG4K+CaAl/uHg2hn/weC+uFu4VH6ifyOtW7fGokWL0LlzZxw/fhzr1q0D8GICruowZ0N1oaWlha+++gpTp07F7du3oaenh1OnTsnnKJFKpTh//jwmT54MAKhbty6ysrKQk5MDY2NjAChzPomSNGzYELq6ujh37pz8qsTTp09x8+ZNdOjQAcCLKxqWlpav3ZaDgwMGDhyIWbNm4cCBA3B1dUVycjKSk5PlVzHi4uLw7NkzhQQkKSkJDx48kCcuZ8+ehZaWlsJVFCIqmczAHDLjOuLXM319nbeR1vNnmg6hWhOdYKxcuRJDhw5FZGQkZs+eLf/GvHfvXvj4+Kg8wLfJgxztKtXOgAEDMH36dKxbtw5jx47F9OnTYWlpCUdHRyxduhS5ubkYOXIkAOCdd96BkZERvvrqK0ycOBHnzp1DeHi4qPZMTEwwcuRITJ8+HbVr14aVlRVmz54NLa2KzUg/adIkNGvWDBcuXEDnzp3h4eGBoUOHYuXKlSgsLERQUBA6dOiA1q1by9cxMDDA8OHD8e233yIzMxMTJ06Ev79/qbdHiIhIPUQnGJ6engqjSIotW7YM2tqVcwKubObm5tDX08X6uMpL0/X1dN94zgYdHR2MHz8eS5cuRUJCAmQyGYYNG4asrCy0bt0aR44cQa1atQC8uLKwfft2TJ8+HZs2bUKnTp0wf/58jBkzRlSby5YtQ3Z2Nnr37g1TU1N88cUXFZ5i3c3NDV27dsXXX3+NgwcP4sCBA5gwYQJ8fX2hpaWF7t27Y/Xq1QrrNGrUCP369UPPnj2Rnp6ODz74AGvXrq1Q+0REVHESoYZNa5iZmQlzc3NkZGTAzMxMYVleXh4SEhLQoEEDpdkva/qzSGQyGTIzM2FmZlbhKxLVRVnHCb05qVSKgwcPomfPnkr9Y0g9bt68iTFjxiDH7cMK3SKpqnQe34ZhwgksaPPsrZ5EUdXuZWnj6/MW2LhxIxo3bixq3bLOoa8q1xWMWrVqKUy4VBax8ypUFdbW1m/VCZ+IiOhtVq4E4+Whp0+ePMGiRYvQrVs3eHt7AwDOnDmDI0eOYO7cuWoJkoiIiKqWciUYw4cPl/+/f//+WLBgAcaPHy8vmzhxItasWYOjR49iypQpqo+SiIiIqhTRN9OPHDmC7t27K5V3794dR48eFbWtdevWwdPTE2ZmZjAzM4O3tzcOHTpU5jp79uxB06ZNYWBgAA8PDxw8eFBUm0RERKR+ohOM2rVr48CBA0rlBw4cQO3atUVtq169eli8eDEuXryICxcu4P3330efPn1w/fr1EuufPn0agwcPxsiRI3H58mX07dsXffv2xbVr18S+jTLVsH6vJBKPDyKi1xM9TDU4OBijRo1CTEyMfEroc+fO4fDhw9i0aZOobfXu3VvhdUhICNatW4ezZ8/C3d1dqf6qVavQvXt3+bMzFi5ciOjoaKxZswbr168X+1aUFPdYz83NhaGh4Rtvj6qn3NxcAMozgBIR0f8RnWAEBgbC1dUV33//PX755RcAgKurK06ePKnwDAqxioqKsGfPHuTk5Mg7j77qzJkzmDp1qkJZt27dEBkZWep28/PzkZ//f/PrZ2ZmAngxFK6kqbBNTU2RmpoKmUwGIyOjco+eqe4EQUBBQQGeP39eY/eJIAjIzc3Fo0ePYGZmBplMxinF1aD477Kkv8/XycvLQ3JysqpDEsXBwaHKDV8uLCwEUPNmtpTkZ2k6BI0qLCwU/Xcmpr74ydvxYtbHHTt2VGRVJVevXoW3tzfy8vJgYmKC/fv3l/rsiZSUFKWhotbW1mU+yS80NBTBwcFK5VFRUTAyMipxHVNTU+Tk5NT4+R5ImUwmQ1ZWFm7duqXpUKq96Oho0eukpqYiIiJCDdGU35AhQ6rckPbU1FQAgGHCCQ1HQpXp5MmToj/Liq/glkeFEgyZTIbbt28jLS1N6Rucr6+vqG01adIEsbGxyMjIwN69ezF8+HAcP35cZQ+4mjVrlsJVj8zMTDg4OKBr165lThJSVFSEwsJC3m///woLC3H69Gn4+PhAR6dCh02VJ5FIoKOjU21nrH1bSKVSREdHo0uXLqJvQ+Xl5aF9+/YVbjspKQmLFy/GzJkz5c/TEasqXsG4desWIiIi8LyBL2SGFpoOp9JoP0uGwYPLmg5DY9q3bw8XFxdR6xTfBSgP0WeKs2fPYsiQIUhMTFQ6+UokEhQViZsNTU9PT/48k1atWuH8+fNYtWoVNmzYoFTXxsZGnmkXS01NLfM5E/r6+tDX11cq19XVLfPDi/fXFUmlUhQWFsLExIT7hirF6/5GS1vnTb6cFCfPzs7Oomc4rMqK37fM0KJGzeRZ024JvUpHR6dCf2PlJfoewOeff47WrVvj2rVrSE9Px9OnT+U/qpjFUyaTKfSZeJm3tzeOHTumUBYdHV1qnw0iIiLSDNFXMG7duoW9e/fKrzq8iVmzZqFHjx5wdHREVlYWIiIiEBMTgyNHjgAAAgICYG9vj9DQUAAvnq7ZoUMHLF++HL169cLOnTtx4cIFbNy48Y1jISIiItURnWC88847uH37tkoSjLS0NAQEBODhw4cwNzeHp6cnjhw5gi5dugB4cT/05Y6WPj4+iIiIwJw5c/DVV1/BxcUFkZGRaNas2RvHQkRvj8zMTNy6davS+/skJiYq/FvZ3raHHNYUD3LE96sqKAIe52m2P1YdgyLoVSCEirzfihD91zthwgR88cUXSElJgYeHh9L9GE9Pz3JvKywsrMzlMTExSmUDBgzAgAEDyt0GEVUtaWlp2PZj+Gs/H9QpJCREI+3q6+li20/bmWRUEkFHDxIIWB9nqulQKp2+ni7Mzc3V2oboBKN///4AgBEjRsjLJBIJBEGoUCdPIqKXZWRkQFpYhM/dsmBnXHM+Tx7kaGN9nCkyMjKYYFQSQdcIAiSYOXMmnJ2dRa2bn59f5hQJlcHGxqbEQQzlURlXy0QnGAkJCeqIg4hIgZ1xEeqb1pwEgzTH0dGxQqOGPDw81BBN9SE6wXByclJHHERERFSNVKgH1Z07d7By5UrEx8cDANzc3DBp0iQ0bNhQpcERERFR1VShx7W7ubnh77//hqenJzw9PXHu3Dm4u7tXaGpfIiIiqn5EX8GYOXMmpkyZgsWLFyuVz5gxQz7ElIiIqhatvAxNh1Cpatr7rWyiE4z4+Hjs3r1bqXzEiBFYuXKlKmIiIqJKZG5uDl09feDucU2HUum0dXTUPlyzphKdYNStWxexsbFKD0iJjY2FlZWVygIjIqLKYW1tje0/bUNGRuV/o09MTERISAhmz55d6YMICgsLcfHiRZ671ER0gjF69GiMGTMGd+/ehY+PDwDg1KlTWLJkicJTS4mIqOqwtrbW6PwbTk5Olf6AOalUKvpx5VR+ohOMuXPnwtTUFMuXL8esWbMAAHZ2dpg/fz4mTpyo8gCJiIio6hGdYEgkEkyZMgVTpkxBVlYWAMDUtOZNs0pERESlq9BMnoWFhXBxcVFILG7dugVdXV3Ur19flfERERFRFSR6HozAwECcPn1aqfzcuXMIDAxURUxERERUxYlOMC5fvox27doplb/77ruIjY1VRUxERERUxYlOMCQSibzvxcsyMjL4JFUiIiICUIEEw9fXF6GhoQrJRFFREUJDQ9G+fXuVBkdERERVk+hOnkuWLIGvry+aNGmC9957DwDw119/ITMzE3/88YfKAyQiIqKqR/QVDDc3N1y5cgX+/v5IS0tDVlYWAgIC8O+//6JZs2bqiJGIiIiqmAo9rt3Ozg7ffPONqmMhIiKiakL0FQzgxS2RTz75BD4+Prh//z4A4KeffsLJkydVGhwRERFVTaITjH379qFbt24wNDTEpUuXkJ+fD+DFKBJe1SAiIiKgAgnGokWLsH79emzatAm6urry8nbt2uHSpUuithUaGoo2bdrA1NQUVlZW6Nu3L27cuFHmOuHh4ZBIJAo/BgYGYt8GERERqZHoBOPGjRvw9fVVKjc3N8ezZ89Ebev48eMYN24czp49i+joaEilUnTt2hU5OTllrmdmZoaHDx/KfxITE0W1S0REROolupOnjY0Nbt++rfTMkZMnT8LZ2VnUtg4fPqzwOjw8HFZWVrh48WKJSUwxiUQCGxsbUW0RERFR5RGdYIwePRqTJk3Cli1bIJFI8ODBA5w5cwbTpk3D3Llz3yiYjIwMAIClpWWZ9bKzs+Hk5ASZTIaWLVvim2++gbu7e4l18/Pz5f1EACAzMxMAIJVKIZVK3yjemqR4X3GfkboVFhZqOgSNKiwsrFJ/Z3l5eUhOTq7w+klJSQCAu3fvVvh37+DgUKFb5fxcE0/MvhKdYMycORMymQydOnVCbm4ufH19oa+vj2nTpmHChAliNycnk8kwefJktGvXrsz5NJo0aYItW7bA09MTGRkZ+Pbbb+Hj44Pr16+jXr16SvVDQ0MRHBysVB4VFQUjI6MKx1tTRUdHazoEquZSU1M1HYJGnTx5Erdu3dJ0GOWWmpqKiIiIN97O4sWLK7zukCFDYG1tXeH1+blWfrm5ueWuKxEEQahIIwUFBbh9+zays7Ph5uYGExOTimxGbuzYsTh06BBOnjxZYqJQGqlUCldXVwwePBgLFy5UWl7SFQwHBwc8fvwYZmZmbxRzTSKVShEdHY0uXboodO4lUrX4+HhMnDgRC9o8Q33TmvN8o3tZ2vj6vAXWrl0LFxcXTYdTbm96BUMV3uQKBj/XxMnMzESdOnWQkZHx2nNohSbaAgA9PT24ubkhMzMTR48eRZMmTeDq6lqhbY0fPx6//fYbTpw4ISq5AABdXV20aNECt2/fLnG5vr4+9PX1S1yPB5R43G+kbjo6Ff5YqhZ0dHSq1N+Yrq4u3NzcNB3GG+HnWvmJ2U+iR5H4+/tjzZo1AIDnz5+jTZs28Pf3h6enJ/bt2ydqW4IgYPz48di/fz/++OMPNGjQQGw4KCoqwtWrV2Frayt6XSIiIlIP0QnGiRMn5A85279/P2QyGZ49e4bvv/8eixYtErWtcePGYfv27YiIiICpqSlSUlKQkpKC58+fy+sEBARg1qxZ8tcLFixAVFQU7t69i0uXLuGTTz5BYmIiRo0aJfatEBERkZqITjAyMjLkozwOHz6M/v37w8jICL169RLdMWndunXIyMiAn58fbG1t5T+7du2S10lKSsLDhw/lr58+fYrRo0fD1dUVPXv2RGZmJk6fPl3lL9ERERFVJ6Jvdjo4OODMmTOwtLTE4cOHsXPnTgAvTvxiO9mUp39pTEyMwusVK1ZgxYoVotohIiKiyiU6wZg8eTKGDh0KExMTODk5wc/PD8CLWyceHh6qjo+IiIiqINEJRlBQEN555x0kJSWhS5cu0NJ6cZfF2dlZdB8MIiIiqp4qNB6sVatWaNWqlUJZr169VBIQERERVX3l6uS5ePFihZEdZTl37hx+//33NwqKiIiIqrZyJRhxcXFwdHREUFAQDh06hEePHsmXFRYW4sqVK1i7di18fHwwcOBAmJqaqi1gIiIievuV6xbJtm3b8M8//2DNmjUYMmQIMjMzoa2tDX19ffm85C1atMCoUaMQGBhYoSlbiYiIqPoodx+M5s2bY9OmTdiwYQOuXLmCxMREPH/+HHXq1IGXlxfq1KmjzjiJiIioChHdyVNLSwteXl7w8vJSQzhERERUHYieyZOIiIjodZhgEBERkcoxwSAiIiKVY4JBREREKic6wdi6dat8aCoRERFRSUQnGDNnzoSNjQ1GjhyJ06dPqyMmIiIiquJEJxj379/Hjz/+iMePH8PPzw9NmzbFkiVLkJKSoo74iIiIqAoSnWDo6Ojgo48+woEDB5CcnIzRo0djx44dcHR0xIcffogDBw5AJpOpI1YiIiKqIt6ok6e1tTXat28Pb29vaGlp4erVqxg+fDgaNmyImJgYFYVIREREVU2FEozU1FR8++23cHd3h5+fHzIzM/Hbb78hISEB9+/fh7+/P4YPH67qWImIiKiKEJ1g9O7dGw4ODggPD8fo0aNx//59/Pzzz+jcuTMAwNjYGF988QWSk5NVHiwRERFVDaKfRWJlZYXjx4/D29u71Dp169ZFQkLCGwVGBABFRUW4cuUK0tPTYWlpCU9PT2hra2s6LCIieg3RCUZYWNhr60gkEjg5OVUoIKJiJ06cwNq1axVGKNnY2CAoKAi+vr4ajIyIiF5H9C2SiRMn4vvvv1cqX7NmDSZPnixqW6GhoWjTpg1MTU1hZWWFvn374saNG69db8+ePWjatCkMDAzg4eGBgwcPimqX3n4nTpzAvHnz4OzsjB9++AEHDx7EDz/8AGdnZ8ybNw8nTpzQdIhERFQG0QnGvn370K5dO6VyHx8f7N27V9S2jh8/jnHjxuHs2bOIjo6GVCpF165dkZOTU+o6p0+fxuDBgzFy5EhcvnwZffv2Rd++fXHt2jWxb4XeUkVFRVi7di28vb2xaNEiuLu7w8jICO7u7li0aBG8vb2xbt06FBUVaTpUIiIqhehbJE+ePIG5ublSuZmZGR4/fixqW4cPH1Z4HR4eDisrK1y8eLHUS+CrVq1C9+7dMX36dADAwoULER0djTVr1mD9+vVK9fPz85Gfny9/nZmZCQCQSqWQSqWi4q3JivdVZeyzf/75BykpKZg1axaKioqUEomBAwdi0qRJuHz5Mpo3b672eKhyFRYWAgAe5NSsvjbF77ewsJCfTZWkMj/Xqgsx+0p0gtGoUSMcPnwY48ePVyg/dOgQnJ2dxW5OQUZGBgDA0tKy1DpnzpzB1KlTFcq6deuGyMjIEuuHhoYiODhYqTwqKgpGRkYVD7aGio6OVnsb//77LwDg5s2buHfvntLygoICAMCxY8dw//59tcdDlSs1NRUAsD7OVMORaMbJkydx69YtTYdRo1TG51p1IeZZZKITjKlTp2L8+PF49OgR3n//fQAvPuiXL1+OlStXit2cnEwmw+TJk9GuXTs0a9as1HopKSmwtrZWKLO2ti51qvJZs2YpJCSZmZlwcHBA165dYWZmVuF4axqpVIro6Gh06dIFurq6am3L3t4ehw4dQuPGjeHm5qa0PC4uDgDQqVMnXsGohuLj4xEREYHP3bJgZ1xzboM9yNHG+jhTtG/fHi4uLpoOp0aozM+16qL4LkB5iE4wRowYgfz8fISEhGDhwoUAgPr162PdunUICAgQuzm5cePG4dq1azh58mSFt1ESfX196OvrK5Xr6urygKqAythvLVq0gI2NDXbt2oVFixZBS+v/ugrJZDLs2rULtra2aNGiBYesVkM6Oi8+luyMi1DftOYkGMV0dHT42VTJeD4oPzH7qUIzeY4dOxb//fcfUlNTkZmZibt3775RcjF+/Hj89ttv+PPPP1GvXr0y69rY2MgvoRZLTU2FjY1Nhdunt4u2tjaCgoJw5swZzJkzB9evX0dubi6uX7+OOXPm4MyZMxg7diyTCyKit5joKxgvq1u37hs1LggCJkyYgP379yMmJgYNGjR47Tre3t44duyYwpDY6OjoMif+oqrH19cXwcHBWLt2LcaNGycvt7W1RXBwMOfBICJ6y4lOMFJTUzFt2jQcO3YMaWlpEARBYbmYoYPjxo1DREQEDhw4AFNTU3k/CnNzcxgaGgIAAgICYG9vj9DQUADApEmT0KFDByxfvhy9evXCzp07ceHCBWzcuFHsW6G3nK+vL9q1a8eZPImIqiDRCUZgYCCSkpIwd+5c2NraQiKRVLjxdevWAQD8/PwUyrdu3YrAwEAAQFJSksI9eB8fH0RERGDOnDn46quv4OLigsjIyDI7hlLVpa2tjRYtWmg6jConLy8PSUlJGo3B0dERBgYGFV6/IsNUC4qAx3maTUDrGBRBrwIh1LRhuVT9iU4wTp48ib/++gteXl5v3PirVz9KUtJj3wcMGIABAwa8cftE1VVSUhLGjBmj0Rg2btyIxo0bi17P3NwcujraNXKYqr6ebonzDBFVRaITDAcHh3IlBkSkOY6Ojm902zAxMREhISGYPXt2hZ8r5OjoWKH1rKysEDA8EK1atZKPKCmv/Pz8UoesVxYbG5sSR66Vh7m5udIwfKKqSnSCsXLlSsycORMbNmxA/fr11RASEb0pAwODCl09eJWTk5NKtiOWmZkZXFxcKjR00MPDQw0REZFYohOMgQMHIjc3Fw0bNoSRkZHSB0B6errKgiMiIqKqqUJXMIiIiIjKIjrBGD58uDriIKISpKamyp/RU5kSExMV/q1MhYWFoqYjJqK3U4Um2rpz5w62bt2KO3fuYNWqVbCyssKhQ4fg6OgId3d3VcdIVCOlpqbik2EBkBbkv76ymoSEhGikXW0dHbz//vuwt7fXSPtE9OZEJxjHjx9Hjx490K5dO5w4cQIhISGwsrLCP//8g7CwMOzdu1cdcRLVOBkZGZAW5OO5cwfIDGrO0EWtvAwY3j2OjIwMJhhEVZjoBGPmzJlYtGgRpk6dClPT/xun/v7772PNmjUqDY6IAJmBOWTGdTQdBhGRKKIfdnb16lV89NFHSuVWVlZ4/PixSoIiIiKiqk10gmFhYYGHDx8qlV++fJmXM4mIiAhABRKMQYMGYcaMGUhJSYFEIoFMJsOpU6cwbdq0N3pkOxEREVUfovtgfPPNNxg3bhwcHBxQVFQENzc3FBUVYciQIZgzZ446YiSq0bQz/oPW82fiVhKKICnIVUs85Q5BzwiQiH+Al6QgWw3REFFlE51g6OnpYdOmTfj6669x9epVZGdno0WLFnBxcVFHfEQ1lrm5ObS0tGFw/5KmQ6l0EomED/0iquJEJxgLFizAtGnT4ODgAAcHB3n58+fPsWzZMnz99dcqDZCoprK2tsbatT8gOTlZ9LpSqVTjna7r1KlToWeJFBUVITExEVZWVmqIiogqi+gEIzg4GJ9//jmMjIwUynNzcxEcHMwEg0iFmjZtiqZNm2o6jEollUpx8OBBTYdBRG9IdCdPQRAgkUiUyv/55x9YWlqqJCgiIiKq2sp9BaNWrVqQSCSQSCRo3LixQpJRVFSE7OxsfP7552oJkoiIiKqWcicYK1euhCAIGDFiBIKDgxU6YOnp6aF+/frw9vZWS5BERERUtZQ7wSh+imqDBg3g4+NToc5bREREVDOI7uTZoUMH+f/z8vJQUFCgsNzMzOzNoyIiIqIqTXQnz9zcXIwfPx5WVlYwNjZGrVq1FH7EOHHiBHr37g07OztIJBJERkaWWT8mJkbeD+Tln5SUFLFvg4iIiNRIdIIxffp0/PHHH1i3bh309fWxefNmBAcHw87ODtu2bRO1rZycHDRv3hw//PCDqPVu3LiBhw8fyn84Xp6IiOjtIvoWya+//opt27bBz88Pn376Kd577z00atQITk5O2LFjB4YOHVrubfXo0QM9evQQGwKsrKxgYWEhej0iIiKqHKITjPT0dDg7OwN40d8iPT0dANC+fXuMHTtWtdGVwsvLC/n5+WjWrBnmz5+Pdu3alVo3Pz8f+fn58teZmZkAXkzmI5VK1R5rdVG8r7jPSN14rFFl4bEmnph9JTrBcHZ2RkJCAhwdHdG0aVPs3r0bbdu2xa+//qr2qwq2trZYv349Wrdujfz8fGzevBl+fn44d+4cWrZsWeI6oaGhCA4OViqPiopSmo2UXi86OlrTIVANwWONKguPtfLLzS3/QxQlgiAIYja+YsUKaGtrY+LEiTh69Ch69+4NQRAglUrx3XffYdKkSaIDBl483Gj//v3o27evqPU6dOgAR0dH/PTTTyUuL+kKhoODAx4/fswRLyJIpVJER0ejS5cuHKJMasVjjSoLjzXxMjMzUadOHWRkZLz2HCr6CsaUKVPk/+/cuTP+/fdfXLx4EY0aNYKnp6f4aN9Q27ZtcfLkyVKX6+vrQ19fX6lcV1eXB1QFcL9RZeGxRpWFx1r5idlPokeRvMrJyQn9+vWDpaUlxowZ86abEy02Nha2traV3i4RERGVTvQVjNI8efIEYWFh2LhxY7nXyc7Oxu3bt+WvExISEBsbC0tLSzg6OmLWrFm4f/++fPjrypUr0aBBA7i7uyMvLw+bN2/GH3/8gaioKFW9DSIiIlIBlSUYFXHhwgV07NhR/nrq1KkAXkxLHh4ejocPHyIpKUm+vKCgAF988QXu378PIyMjeHp64ujRowrbICIiIs3TaILh5+eHsvqYhoeHK7z+8ssv8eWXX6o5KiIiInpTb9wHg4iIiOhV5b6C0a9fvzKXP3v27E1jISIiomqi3AmGubn5a5cHBAS8cUBERERU9ZU7wdi6das64yAiIqJqhH0wiIiISOU0OoqEqpbMzEzcunULOjriDpv8/HykpKSoKarysbGxKXFG1/IwNzeHtbW1iiMiIqremGBQuaSlpWHbj+EICwvTdCiVTl9PF9t+2s4kg4hIBCYYVC4ZGRmQFhbhc7cs2BkXiVq3oAh4nKetpsjKp45BEfQqEMKDHG2sjzNFRkZGjUkwioqKcOXKFaSnp8PS0hKenp7Q1tbs74+Iqh4mGCSKnXER6puKSzAAoDHEr0OV78SJE1i7dq3CLS0bGxsEBQXB19dXg5ERUVXDTp5EBOBFcjFv3jw4Ozvjhx9+wMGDB/HDDz/A2dkZ8+bNw4kTJzQdIhFVIUwwiAhFRUVYu3YtvL29sWjRIri7u8PIyAju7u5YtGgRvL29sW7dOhQV8UoUEZUPEwwiwpUrV5CSkoKhQ4dCS0vxY0FLSwtDhw7Fw4cPceXKFQ1FSERVDRMMIkJ6ejoAoEGDBiUuLy4vrkdE9DpMMIgIlpaWAICEhIQSlxeXF9cjInodJhhEBE9PT9jY2GDHjh2QyWQKy2QyGXbs2AFbW1t4enpqKEIiqmqYYBARtLW1ERQUhDNnzmDOnDm4fv06cnNzcf36dcyZMwdnzpzB2LFjOR8GEZUb58EgIgCAr68vgoODsXbtWowbN05ebmtri+DgYM6DQUSiMMEgIjlfX1+0a9eOM3kS0RtjgkFECrS1tdGiRQtNh0FEVRz7YBAREZHKMcEgIiIildNognHixAn07t0bdnZ2kEgkiIyMfO06MTExaNmyJfT19dGoUSOEh4erPU4iIiISR6MJRk5ODpo3b44ffvihXPUTEhLQq1cvdOzYEbGxsZg8eTJGjRqFI0eOqDlSIiIiEkOjnTx79OiBHj16lLv++vXr0aBBAyxfvhwA4OrqipMnT2LFihXo1q2busIkIiIikarUKJIzZ86gc+fOCmXdunXD5MmTS10nPz8f+fn58teZmZkAAKlUCqlUqpY4S5OXl4fk5ORKbfNVDg4OMDAwEL1eYWGhGqKpOgoLCyv9eKmpivcz9zepG4818cTsqyqVYKSkpMDa2lqhzNraGpmZmXj+/DkMDQ2V1gkNDUVwcLBSeVRUFIyMjNQWa0lSU1MRERFRqW2+asiQIUr7sDxSU1PVEE3VcfLkSdy6dUvTYdQo0dHRmg6Baggea+WXm5tb7rpVKsGoiFmzZmHq1Kny15mZmXBwcEDXrl1hZmZWqbHk5eWhffv2FV4/KSkJixcvxsyZM+Ho6FihbVT0CkZ8fLzGkyNNat++PVxcXDQdRo0glUoRHR2NLl26QFdXV9PhUDXGY0284rsA5VGlEgwbGxulb9KpqakwMzMr8eoFAOjr60NfX1+pXFdXt9IPKF1dXbi5uVV4fR2dF78uZ2dnNG7cWFVhiWq7ptLR0eEHUCXTxN8o1Uw81spPzH6qUvNgeHt749ixYwpl0dHR8Pb21lBEREREVBKNJhjZ2dmIjY1FbGwsgBfDUGNjY5GUlATgxe2NgIAAef3PP/8cd+/exZdffol///0Xa9euxe7duzFlyhRNhE9ERESl0Oh17wsXLqBjx47y18V9JYYPH47w8HA8fPhQnmwAQIMGDfD7779jypQpWLVqFerVq4fNmzdX+hDV1NRUZGRkVGqbAJCYmKjwb2Uq/j3881gXD3LEPfhKKgOe5Wv2YpmFvgy6FQjh0fMqdZGPiOitodEEw8/PD4IglLq8pFk6/fz8cPnyZTVGVbbU1FR8MiwA0oL811dWk5CQEA21LGBfgrGG2tYcbS0tmJubazoMIqIqpWb33KuAjIwMSAvy8dy5A2QGNeeko5WXAcO7xxEYGAh7e3tR60qlUjx+/FhNkZVPnTp1KtyJy8HBoUJDe4mIajImGBUkMzCHzLiOpsOodG3btn2jkTBERFQz8AYzERERqRwTDCIiIlI5JhhERESkcuyDUUHaGf9B6/kzTYdRaSQF2ZoOgYiIqhAmGCKZm5tDS0sbBvcvaTqUSieRSDhck4iIyoUJhkjW1tZYu/YHjTx2/eHDh9iyZQtGjBgBW1vbSm27qKgIiYmJsLKyqtR2iYioamKCUQFNmzZF06ZNK73dmzdvYsuWLXj33Xcr/WFnUqkUBw8erNQ2iYio6mInTyIiIlI5JhhERESkcrxFUony8vIUHt4mlioedubo6AgDA4MKr09ERFQeTDAqUVJSEsaMGfPG23mTh51t3Lix0vtvEBFRzcMEoxI5Ojpi48aNGo+BiIhI3ZhgVCIDAwNePSAiohqBnTyJiIhI5ZhgEBERkcoxwSAiIiKVY4JBREREKscEg4iIiFSOCQYRERGpHBMMIiIiUrkaNw+GIAgAgMzMTA1HUrVIpVLk5uYiMzMTurq6mg6HqjEea1RZeKyJV3zuLD6XlqXGJRhZWVkAAAcHBw1HQkREVDVlZWXB3Ny8zDoSoTxpSDUik8nw4MEDmJqaQiKRaDqcKiMzMxMODg5ITk6GmZmZpsOhaozHGlUWHmviCYKArKws2NnZQUur7F4WNe4KhpaWFurVq6fpMKosMzMz/iFSpeCxRpWFx5o4r7tyUYydPImIiEjlmGAQERGRyjHBoHLR19fHvHnzoK+vr+lQqJrjsUaVhceaetW4Tp5ERESkfryCQURERCrHBIOIiIhUjgkGERERqRwTDCIiIlI5JhhUbosXL4ZEIsHkyZM1HQpVM0VFRZg7dy4aNGgAQ0NDNGzYEAsXLizX8w6IynLixAn07t0bdnZ2kEgkiIyMVKoTHx+PDz/8EObm5jA2NkabNm2QlJRU+cFWMzVuJk+qmPPnz2PDhg3w9PTUdChUDS1ZsgTr1q3Djz/+CHd3d1y4cAGffvopzM3NMXHiRE2HR1VYTk4OmjdvjhEjRqBfv35Ky+/cuYP27dtj5MiRCA4OhpmZGa5fvw4DAwMNRFu9cJgqvVZ2djZatmyJtWvXYtGiRfDy8sLKlSs1HRZVIx988AGsra0RFhYmL+vfvz8MDQ2xfft2DUZG1YlEIsH+/fvRt29fedmgQYOgq6uLn376SXOBVVO8RUKvNW7cOPTq1QudO3fWdChUTfn4+ODYsWO4efMmAOCff/7ByZMn0aNHDw1HRtWZTCbD77//jsaNG6Nbt26wsrLCO++8U+JtFBKPt0ioTDt37sSlS5dw/vx5TYdC1djMmTORmZmJpk2bQltbG0VFRQgJCcHQoUM1HRpVY2lpacjOzsbixYuxaNEiLFmyBIcPH0a/fv3w559/okOHDpoOsUpjgkGlSk5OxqRJkxAdHc37kaRWu3fvxo4dOxAREQF3d3fExsZi8uTJsLOzw/DhwzUdHlVTMpkMANCnTx9MmTIFAODl5YXTp09j/fr1TDDeEBMMKtXFixeRlpaGli1bysuKiopw4sQJrFmzBvn5+dDW1tZghFRdTJ8+HTNnzsSgQYMAAB4eHkhMTERoaCgTDFKbOnXqQEdHB25ubgrlrq6uOHnypIaiqj6YYFCpOnXqhKtXryqUffrpp2jatClmzJjB5IJUJjc3F1pail3CtLW15d8widRBT08Pbdq0wY0bNxTKb968CScnJw1FVX0wwaBSmZqaolmzZgplxsbGqF27tlI50Zvo3bs3QkJC4OjoCHd3d1y+fBnfffcdRowYoenQqIrLzs7G7du35a8TEhIQGxsLS0tLODo6Yvr06Rg4cCB8fX3RsWNHHD58GL/++itiYmI0F3Q1wWGqJIqfnx+HqZLKZWVlYe7cudi/fz/S0tJgZ2eHwYMH4+uvv4aenp6mw6MqLCYmBh07dlQqHz58OMLDwwEAW7ZsQWhoKP777z80adIEwcHB6NOnTyVHWv0wwSAiIiKV4zwYREREpHJMMIiIiEjlmGAQERGRyjHBICIiIpVjgkFEREQqxwSDiIiIVI4JBhEREakcEwwiIiJSOSYYREREpHJMMIioUiQnJ2PEiBGws7ODnp4enJycMGnSJDx58kRex8/PDxKJBBKJBAYGBnBzc8PatWvly4uKirB48WI0bdoUhoaGsLS0xDvvvIPNmzdr4i0RURmYYBCR2t29exetW7fGrVu38PPPP+P27dtYv349jh07Bm9vb6Snp8vrjh49Gg8fPkRcXBz8/f0xbtw4/PzzzwCA4OBgrFixAgsXLkRcXBz+/PNPjBkzBs+ePdPQOyOi0vBZJESkdj169MC1a9dw8+ZNGBoaystTUlLQsGFDBAQEYN26dSU+TK9x48Zo1aoVfv75Z3h5eeGjjz7CvHnzNPAuiEgMXsEgIrVKT0/HkSNHEBQUpJBcAICNjQ2GDh2KXbt2obTvOoaGhigoKJDX/+OPP/Do0SO1x01Eb4YJBhGp1a1btyAIAlxdXUtc7urqiqdPnyolDUVFRdi+fTuuXLmC999/HwDw3Xff4dGjR7CxsYGnpyc+//xzHDp0SO3vgYjEY4JBRJXidXdj9fT0AABr166FiYkJDA0NMXr0aEyZMgVjx44FALi5ueHatWs4e/YsRowYgbS0NPTu3RujRo1Se/xEJA4TDCJSq0aNGkEikSA+Pr7E5fHx8ahbty4sLCwAAEOHDkVsbCwSEhKQk5OD7777Dlpa//dRpaWlhTZt2mDy5Mn45ZdfEB4ejrCwMCQkJFTG2yGicmKCQURqVbt2bXTp0gVr167F8+fPFZalpKRgx44dCAwMlJeZm5ujUaNGsLe3V0gsSuPm5gYAyMnJUWncRPRmmGAQkdqtWbMG+fn56NatG06cOIHk5GQcPnwYXbp0QePGjfH111+Xazsff/wxVqxYgXPnziExMRExMTEYN24cGjdujKZNm6r5XRCRGEwwiEjtXFxccP78eTg7O8Pf3x9OTk7o0aMHGjdujFOnTsHExKRc2+nWrRt+/fVX9O7dG40bN8bw4cPRtGlTREVFQUdHR83vgojE4DwYRKQR8+bNw3fffYfo6Gi8++67mg6HiFSMCQYRaczWrVuRkZGBiRMnlqu/BRFVHUwwiIiISOX4lYGIiIhUjgkGERERqRwTDCIiIlI5JhhERESkckwwiIiISOWYYBAREZHKMcEgIiIilWOCQURERCrHBIOIiIhU7v8BzNCVw0RWAM4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/fr/tdgd1l9x1x7drdw1hdcdjrc00000gn/T/ipykernel_3898/1869046845.py:64: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  summary_df = combined_df.groupby(['QPS']).agg({\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from io import StringIO\n", "\n", "# CSV data dictionary\n", "title = \"E2E Latency Distribution (seconds)\"\n", "csv_data_dict = {\n", "    4: \"\"\"Time,AIBrix,Round-<PERSON>\n", "    0,0.9,0.9\n", "    1,0.8,0.9\n", "    2,0.7,0.9\n", "    3,0.8,1.3\n", "    4,1.0,2\n", "    \"\"\",\n", "    8: \"\"\"Time,AIBrix,Round-<PERSON>\n", "    0,1.9,1.9\n", "    1,1.7,2.9\n", "    2,1.7,1.9\n", "    3,1.2,3\n", "    4,1.8,2\n", "    \"\"\",\n", "    16: \"\"\"Time,AIBrix,Round-<PERSON>\n", "    0,3.2,4\n", "    1,3.2,2.9\n", "    2,3,3.5\n", "    3,2.8,2.9\n", "    4,2.5,3.8\n", "    \"\"\",\n", "}\n", "\n", "# Step 1: Parse the CSV strings into DataFrames\n", "dfs = []\n", "for qps, csv_str in csv_data_dict.items():\n", "    df = pd.read_csv(StringIO(csv_str))\n", "    df['QPS'] = qps\n", "    dfs.append(df)\n", "\n", "# Step 2: <PERSON><PERSON><PERSON> the DataFrames\n", "combined_df = pd.concat(dfs)\n", "\n", "# Convert 'QPS' to string first\n", "combined_df['QPS'] = combined_df['QPS'].astype(str)\n", "\n", "# Ensure QPS is an ordered categorical type to sort on plots\n", "sorted_qps = sorted(csv_data_dict.keys())\n", "combined_df['QPS'] = pd.Categorical(combined_df['QPS'], categories=[str(qps) for qps in sorted_qps], ordered=True)\n", "\n", "# Step 3: Melt the combined DataFrame for easier plotting\n", "melted_df = pd.melt(combined_df, id_vars=['Time', 'QPS'], value_vars=['AIBrix', 'Round-Robin'], var_name='Approach', value_name='Latency')\n", "\n", "# Step 4: Create grouped box plot for latency distribution\n", "plt.figure(figsize=(6, 3))\n", "sns.boxplot(data=melted_df, x='QPS', y='Latency', hue='Approach')\n", "plt.xlabel('QPS')\n", "plt.ylabel('Latency (seconds)')\n", "plt.title(title)\n", "plt.legend(title='Approach')\n", "plt.grid(True)\n", "plt.show()\n", "\n", "# Step 5: Calculate median and 99th percentile latencies\n", "summary_df = combined_df.groupby(['QPS']).agg({\n", "    'AIBrix': ['median', lambda x: np.percentile(x, 99)],\n", "    'Round-Robin': ['median', lambda x: np.percentile(x, 99)]\n", "}).reset_index()\n", "summary_df.columns = ['QPS', 'AIBrix Median', 'AIBrix 99th Percentile', 'Round-<PERSON>', 'Round-<PERSON> 99th Percentile']\n", "\n", "# Step 6: Ensure the 'QPS' column in summary_df is ordered correctly\n", "summary_df['QPS'] = pd.Categorical(summary_df['QPS'], categories=[str(qps) for qps in sorted_qps], ordered=True)\n", "summary_df = summary_df.sort_values('QPS')\n", "\n", "# Create line plot for median and 99th percentile latencies\n", "plt.figure(figsize=(6, 3))\n", "\n", "# Median plot\n", "plt.plot(summary_df['QPS'].astype(str), summary_df['AIBrix Median'], marker='o', linestyle='-', label='AIBrix Median')\n", "plt.plot(summary_df['QPS'].astype(str), summary_df['Round-<PERSON> Median'], marker='o', linestyle='-', label='Round-<PERSON>n')\n", "\n", "# 99th Percentile plot\n", "plt.plot(summary_df['QPS'].astype(str), summary_df['AIBrix 99th Percentile'], marker='o', linestyle='--', label='AIBrix 99th Percentile')\n", "plt.plot(summary_df['QPS'].astype(str), summary_df['Round-Robin 99th Percentile'], marker='o', linestyle='--', label='Round-Robin 99th Percentile')\n", "\n", "plt.xlabel('QPS')\n", "plt.ylabel('Latency (seconds)')\n", "plt.title('Median and 99th Percentile Latency for Different Approaches')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/cw/mdpd8d8j7g10qxq96v7_v64w0000gn/T/ipykernel_35414/1794741744.py:65: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.boxplot(data=df, x='Approach', y='Latency', order=list(approach_mapping.values()), palette=colors)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAw0AAAJOCAYAAAD1WuuWAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/TGe4hAAAACXBIWXMAAA9hAAAPYQGoP6dpAACoSElEQVR4nOzde1yP9//48ce7o86RqEgHOSQicihU20I2xhi25WzYynkO4zMJE7YxZzMjpxzmvLE5Tjkf5pRDcsphk7FQks7X749+XV9vJZnD2+F5v9266f26Xtd1Pa+r3rqe79dJoyiKghBCCCGEEEI8gp6uAxBCCCGEEEK83CRpEEIIIYQQQhRJkgYhhBBCCCFEkSRpEEIIIYQQQhRJkgYhhBBCCCFEkSRpEEIIIYQQQhRJkgYhhBBCCCFEkSRpEEIIIYQQQhRJkgYhhBBCCCFEkSRpEOIl0LVrV5ydnV/IuZydnenatav6esGCBWg0Gv78888Xcv6AgAACAgJeyLke9KKvU+QJDw9Ho9Hw77//6joUIYQQT0GSBvGfPMsHsLS0NMLDw4mOjn76wF4C+Q9J+V+mpqZUqFCBli1bEhkZSUZGxjM5z+nTpwkPD+fSpUvP5HjP0ssc24vUvn17NBoNw4YN03UoQgghxFMx0HUAQqSlpTF69GgAnXwC/bzMnj0bc3NzMjIy+Pvvv9m8eTPdu3dnypQpbNiwAUdHR7Xu3Llzyc3NfaLjnz59mtGjRxMQEPBErRTx8fHo6T3fzwuKim3Lli3P9dwvi5SUFH799VecnZ1ZtmwZEyZMQKPR6DosIYQQ4j+RpEGI5+TDDz+kdOnS6uuwsDCioqLo3Lkz7dq1Y//+/eo2Q0PD5xqLoiikp6djYmKCsbHxcz3X4xgZGen0/C/K6tWrycnJYf78+bz99tvs3LkTf39/ncRy7949zMzMdHJuIYQQrwfpniSem8zMTMLCwqhTpw5WVlaYmZnRuHFjduzYoda5dOkStra2AIwePVrt0hMeHq7WOXPmDB9++CGlSpWiRIkSeHt788svv2idK7+71J49exg0aBC2traYmZnxwQcfcPPmzQKx/f777/j7+2NhYYGlpSV169Zl6dKlAIwaNQpDQ8NC9+vVqxfW1takp6f/p3sSHBzMp59+yoEDB9i6dataXtiYhuXLl1OnTh01xho1ajB16lT1etu1awfAW2+9pd63/C5ezs7OtGjRgs2bN+Pt7Y2JiQlz5sxRtz04piFfWloavXv3xsbGBktLSzp37szt27e16jz8s8n34DEfF1thYxpu3LhBjx49KFu2LCVKlKBmzZosXLhQq86lS5fQaDR89913/Pjjj1SsWBFjY2Pq1q3LoUOHCr3fhXncdXbp0oXSpUuTlZVVYN+mTZtSpUqVYp0nKiqKJk2a8NZbb+Hu7k5UVFSBOvm/tzt37nzsvc//mW7ZsoVatWpRokQJqlWrxpo1awo9ZkxMDCEhIZQpU4by5cur22fNmoWHhwfGxsY4ODgQGhrKnTt3tI6xa9cu2rVrR4UKFTA2NsbR0ZGBAwdy//79Atdw5swZ2rdvj62tLSYmJlSpUoX//e9/BerduXOHrl27Ym1tjZWVFd26dSMtLa1Y91IIIYTuSdIgnpuUlBR++uknAgICmDhxIuHh4dy8eZNmzZpx7NgxAGxtbZk9ezYAH3zwAYsXL2bx4sW0adMGgFOnTtGgQQPi4uL48ssvmTRpEmZmZrRu3Zq1a9cWOGffvn05fvw4o0aN4vPPP+fXX3+lT58+WnUWLFjAe++9x61btxg+fDgTJkygVq1abNq0CYBOnTqRnZ3NihUrtPbLzMxk1apVtG3blhIlSvzn+9KpUyeg6G46W7du5eOPP6ZkyZJMnDiRCRMmEBAQwJ49ewDw8/OjX79+AIwYMUK9b+7u7uox4uPj+fjjj2nSpAlTp06lVq1aRcbVp08f4uLiCA8Pp3PnzkRFRdG6dWsURXmi6ytObA+6f/8+AQEBLF68mODgYL799lusrKzo2rWrmiQ9aOnSpXz77bf07t2br7/+mkuXLtGmTZtCH/L/y3V26tSJpKQkNm/erLXf9evX+eOPP+jYseNjz3Ht2jV27NjBxx9/DMDHH3/MqlWryMzM/E8x5Tt37hwdOnSgefPmjB8/HgMDA9q1a6eVgOYLCQnh9OnThIWF8eWXXwJ5421CQ0NxcHBg0qRJtG3bljlz5tC0aVOt+7dy5UrS0tL4/PPPmT59Os2aNWP69Ol07txZ6xyxsbHUr1+fP/74g549ezJ16lRat27Nr7/+WiCe9u3bc/fuXcaPH0/79u1ZsGCB2i1RCCHEK0AR4j+IjIxUAOXQoUOPrJOdna1kZGRold2+fVspW7as0r17d7Xs5s2bCqCMGjWqwDHeeecdpUaNGkp6erpalpubq/j6+iqVKlUqEE9gYKCSm5urlg8cOFDR19dX7ty5oyiKoty5c0exsLBQ6tevr9y/f1/rXA/u5+Pjo9SvX19r+5o1axRA2bFjxyOvWVEUZdSoUQqg3Lx5s9Dtt2/fVgDlgw8+UMu6dOmiODk5qa/79++vWFpaKtnZ2Y88z8qVKx8Zj5OTkwIomzZtKnRbly5d1Nf5965OnTpKZmamWv7NN98ogLJ+/Xq17FE/p4ePWVRs/v7+ir+/v/p6ypQpCqAsWbJELcvMzFR8fHwUc3NzJSUlRVEURUlISFAAxcbGRrl165Zad/369Qqg/PrrrwXO9aDiXmdOTo5Svnx5pUOHDlr7T548WdFoNMrFixeLPI+iKMp3332nmJiYqLGfPXtWAZS1a9f+p5gU5f9+pqtXr1bLkpOTFXt7e8XLy6vAMRs1aqT1+3Pjxg3FyMhIadq0qZKTk6OWz5gxQwGU+fPnq2VpaWkFrmn8+PGKRqNRLl++rJb5+fkpFhYWWmWKov1eyn8/PPieVxRF+eCDDxQbG5sC5xFCCPFykpYG8dzo6+ur/ddzc3O5desW2dnZeHt7c+TIkcfuf+vWLf744w/1E8p///2Xf//9l6SkJJo1a8a5c+f4+++/tfbp1auX1mDTxo0bk5OTw+XLl4G8T/Dv3r3Ll19+WaC14MH9OnfuzIEDB7hw4YJaFhUVhaOj41P3Szc3Nwfg7t27j6xjbW3NvXv3Cv0EubhcXFxo1qxZsev36tVLa2zF559/joGBAb/99tt/jqE4fvvtN+zs7NRP5SFvjEe/fv1ITU0lJiZGq36HDh0oWbKk+rpx48YAXLx4sVjne9x16unpERwczC+//KL1M4qKisLX1xcXF5fHniMqKor33nsPCwsLACpVqkSdOnUK7aJUnJjyOTg48MEHH6iv87syHT16lOvXr2vV7dmzJ/r6+urrbdu2kZmZyYABA7QGwvfs2RNLS0s2btyolpmYmKjf37t3j3///RdfX18UReHo0aMA3Lx5k507d9K9e3cqVKigde7CBnx/9tlnWq8bN25MUlISKSkphd4TIYQQLxdJGsRztXDhQjw9PSlRogQ2NjbY2tqyceNGkpOTH7vv+fPnURSFkSNHYmtrq/U1atQoIK8v/IMefnjJf7jM7x+enwRUr169yHN36NABY2Nj9SEvOTmZDRs2EBwc/NQz4KSmpgKoD5SFCQkJoXLlyjRv3pzy5cvTvXt3tftUcRXn4fZBlSpV0nptbm6Ovb39c5829fLly1SqVKnAjE753ZnyE758j/sZP05xrrNz587cv39f7QIXHx/P4cOH1a5lRYmLi+Po0aM0bNiQ8+fPq18BAQFs2LCh0Ifk4t57Nze3Ar9/lStXBihQ9+Gff/59fHhMhpGREa6urlr3+cqVK3Tt2pVSpUphbm6Ora2tmiznv3fzk7THvZfyPe3PTQghhG7J7EniuVmyZAldu3aldevWDBkyhDJlyqCvr8/48eO1PsF/lPwpSAcPHvzIT8zd3Ny0Xj/4yeqDlCfsl1+yZElatGhBVFQUYWFhrFq1ioyMjGL1Z3+ckydPAgVjf1CZMmU4duwYmzdv5vfff+f3338nMjKSzp07Fxgg/CgPflr8vOXk5Lywcz2rn3FRqlWrRp06dViyZAmdO3dmyZIlGBkZ0b59+8fuu2TJEgAGDhzIwIEDC2xfvXo13bp1e2axPsp//fnn5OTQpEkTbt26xbBhw6hatSpmZmb8/fffdO3a9YmnBs73In5uQgghnh9JGsRzs2rVKlxdXVmzZo3Wp6P5rQT5HvXJvaurK5DXVSUwMPCZxFSxYkUg78G9qId2yPu0uVWrVhw6dIioqCi8vLzw8PB46hgWL14M8NiuQ0ZGRrRs2ZKWLVuSm5tLSEgIc+bMYeTIkYV+4vy0zp07x1tvvaW+Tk1NJTExkXfffVctK1myZIGZdjIzM0lMTNQqe5LYnJyciI2NJTc3V6u14cyZM+r2Z6k41wl5P/9BgwaRmJjI0qVLee+997S6RRVGURSWLl3KW2+9RUhISIHtY8eOJSoqqkDSUNyY8lvfHry/Z8+eBXjsWh359zE+Pl59b0Hezy8hIUF9j504cYKzZ8+ycOFCrYHPD3eVyz9GfhIshBDi9Sbdk8Rzk//J4oOfJB44cIB9+/Zp1TM1NQUo8DBapkwZAgICmDNnToGHUqDQKVEfp2nTplhYWDB+/PgC06Y+/Iln8+bNKV26NBMnTiQmJuaZtDIsXbqUn376CR8fH955551H1ktKStJ6raenh6enJ4C6onT+vPsP37f/6scff9SaQWf27NlkZ2fTvHlztaxixYrs3LmzwH4PtzQ8SWzvvvsu169f15qtKjs7m+nTp2Nubv7M1zYoznVC3oxHGo2G/v37c/HixWL9/Pfs2cOlS5fo1q0bH374YYGvDh06sGPHDq5du/afYrp27ZrWrGEpKSksWrSIWrVqYWdnV2RsgYGBGBkZMW3aNK3f9Xnz5pGcnMx7770HFP6+VRSlwExWtra2+Pn5MX/+fK5cuaK1TVoPhBDi9SMtDeKpzJ8/v9C+9v3796dFixasWbOGDz74gPfee4+EhAR++OEHqlWrpvbrh7xuFNWqVWPFihVUrlyZUqVKUb16dapXr87MmTNp1KgRNWrUoGfPnri6uvLPP/+wb98+/vrrL44fP/5E8VpaWvL999/z6aefUrduXT755BNKlizJ8ePHSUtL0+r6Y2hoyEcffcSMGTPQ19fXGqhbHKtWrcLc3JzMzEx1Reg9e/ZQs2ZNVq5cWeS+n376Kbdu3eLtt9+mfPnyXL58menTp1OrVi21r3+tWrXQ19dn4sSJJCcnY2xszNtvv02ZMmWeKM58mZmZvPPOO7Rv3574+HhmzZpFo0aNeP/997Xi+uyzz2jbti1NmjTh+PHjbN68WWsRuyeNrVevXsyZM4euXbty+PBhnJ2dWbVqFXv27GHKlClFjv14XtcJeQ/FQUFBrFy5Emtra/WhuihRUVHo6+s/su7777/P//73P5YvX86gQYOeOKbKlSvTo0cPDh06RNmyZZk/fz7//PMPkZGRj43N1taW4cOHM3r0aIKCgnj//ffVc9WtW1dNiqpWrUrFihUZPHgwf//9N5aWlqxevbrQsQfTpk2jUaNG1K5dm169euHi4sKlS5fYuHGjOq2yEEKI14RO5mwSr7z8aR0f9XX16lUlNzdXiYiIUJycnBRjY2PFy8tL2bBhQ4HpRRVFUfbu3avUqVNHMTIyKjCt54ULF5TOnTsrdnZ2iqGhoVKuXDmlRYsWyqpVqwrE8/AUsDt27Ch06s9ffvlF8fX1VUxMTBRLS0ulXr16yrJlywpc58GDBxVAadq0abHvTf4Uk/lfJUqUUMqXL6+0aNFCmT9/vtb0sfkevierVq1SmjZtqpQpU0YxMjJSKlSooPTu3VtJTEzU2m/u3LmKq6uroq+vr3WdTk5OynvvvVdofI+acjUmJkbp1auXUrJkScXc3FwJDg5WkpKStPbNyclRhg0bppQuXVoxNTVVmjVrppw/f77AMYuK7eEpVxVFUf755x+lW7duSunSpRUjIyOlRo0aSmRkpFad/ClXv/322wLX9PDvTGGe5Drz/fzzzwqg9OrVq8hjK0reNLE2NjZK48aNi6zn4uKiTpH6JDHl/0w3b96seHp6KsbGxkrVqlWVlStXFnqdj5oOecaMGUrVqlUVQ0NDpWzZssrnn3+u3L59W6vO6dOnlcDAQMXc3FwpXbq00rNnT+X48eMKUODncvLkSeWDDz5QrK2tlRIlSihVqlRRRo4cqW5/1BTE+XEmJCQUeb+EEEK8HDSKIu3IQjzK8ePHqVWrFosWLSrWzDni9bJ+/Xpat27Nzp071aldn6UFCxbQrVs3Dh06hLe3d5F1nZ2dqV69Ohs2bHjmcQghhBCPI2MahCjC3LlzMTc3V1eoFm+WuXPn4urqSqNGjXQdihBCCKFTMqZBiEL8+uuvnD59mh9//JE+ffqoA3vFm2H58uXExsayceNGpk6d+sxnqhJCCCFeNZI0CFGIvn378s8///Duu+8yevRoXYcjXrCPP/4Yc3NzevToUejUqUIIIcSbRsY0CCGEEEIIIYokYxqEEEIIIYQQRZKkQQghhBBCCFGk135MQ25uLteuXcPCwkIGMwohhHjtKYrC3bt3cXBwQE9PPhsUQjwbr33ScO3aNRwdHXUdhhBCCPFCXb16lfLly+s6DCHEa+K1TxosLCyAvP88LS0tdRyNEEII8XylpKTg6Oio/v0TQohn4bVPGvK7JFlaWkrSIIQQ4o0hXXKFEM+SdHYUQgghhBBCFEmSBiGEEEIIIUSRJGkQQgghhBBCFOm1H9MghBBCiBcnNzeXzMxMXYchhCgGIyOjYk/NLEmDEEIIIZ6JzMxMEhISyM3N1XUoQohi0NPTw8XFBSMjo8fWlaRBCCGEEE9NURQSExPR19fH0dFRFpYT4iWXvwByYmIiFSpUeOyMa5I0CCGEEOKpZWdnk5aWhoODA6amproORwhRDLa2tly7do3s7GwMDQ2LrKvTjwFycnIYOXIkLi4umJiYULFiRcaOHYuiKGodRVEICwvD3t4eExMTAgMDOXfunA6jFkIIIcTDcnJyAIrVzUEI8XLIf7/mv3+LotOkYeLEicyePZsZM2YQFxfHxIkT+eabb5g+fbpa55tvvmHatGn88MMPHDhwADMzM5o1a0Z6eroOIxdCCCFEYWRROSFeHU/yftVp96S9e/fSqlUr3nvvPQCcnZ1ZtmwZBw8eBPJaGaZMmcJXX31Fq1atAFi0aBFly5Zl3bp1fPTRRzqLXQghhBBCiDeFTpMGX19ffvzxR86ePUvlypU5fvw4u3fvZvLkyQAkJCRw/fp1AgMD1X2srKyoX78++/btKzRpyMjIICMjQ32dkpICQFZWFllZWc/5ioQQQgjdkr914lUUHR3NW2+9xe3bt7G2ttZ1OKIQOk0avvzyS1JSUqhatSr6+vrk5OQwbtw4goODAbh+/ToAZcuW1dqvbNmy6raHjR8/ntGjRxco37JliwzMEkII8dpLS0vTdQjiOdq3bx+NGjUiKCiIjRs36joc8QbRadLw888/ExUVxdKlS/Hw8ODYsWMMGDAABwcHunTp8p+OOXz4cAYNGqS+TklJwdHRkaZNm2JpafmsQhdCCCFeSvkt7OL1NG/ePPr27cu8efO4du0aDg4Oz/V8mZmZMrhd5FF0qHz58sqMGTO0ysaOHatUqVJFURRFuXDhggIoR48e1arj5+en9OvXr1jnSE5OVgAlOTn5mcQshBBCvMx09Xfv/v37yunTp5X79++/0PO+Se7evauYm5srZ86cUTp06KCMGzdO3bZjxw4FUDZs2KDUqFFDMTY2VurXr6+cOHFCrRMZGalYWVkpa9euVdzc3BRjY2OladOmypUrV9Q6o0aNUmrWrKnMnTtXcXZ2VjQajaIoinL58mXl/fffV8zMzBQLCwulXbt2yvXr19X9zp8/r7z//vtKmTJlFDMzM8Xb21vZunWrVvzp6enK0KFDlfLlyytGRkZKxYoVlZ9++kkr/m3btil16tRRTExMFB8fH+XMmTPP5V6KPE/yvtXp7ElpaWkFFn/R19dXV5J0cXHBzs6O7du3q9tTUlI4cOAAPj4+LzRWIYQQQghd+vnnn6latSpVqlShY8eOzJ8/X2uaeoAhQ4YwadIkDh06hK2tLS1bttQa55KWlsa4ceNYtGgRe/bs4c6dOwXGiJ4/f57Vq1ezZs0ajh07Rm5uLq1ateLWrVvExMSwdetWLl68SIcOHdR9UlNTeffdd9m+fTtHjx4lKCiIli1bcuXKFbVO586dWbZsGdOmTSMuLo45c+Zgbm6ude7//e9/TJo0iT///BMDAwO6d+/+LG+heBrPP4d5tC5duijlypVTNmzYoCQkJChr1qxRSpcurQwdOlStM2HCBMXa2lpZv369Ehsbq7Rq1UpxcXEp9icZ0tIghBDiTSItDa8vX19fZcqUKYqiKEpWVpZSunRpZceOHYqi/N8n9cuXL1frJyUlKSYmJsqKFSsURclraQCU/fv3q3Xi4uIUQDlw4ICiKHktDYaGhsqNGzfUOlu2bFH09fW1WiROnTqlAMrBgwcfGa+Hh4cyffp0RVEUJT4+XgEKtD7ke7ClId/GjRsVQH6nnqNXpqVh+vTpfPjhh4SEhODu7s7gwYPp3bs3Y8eOVesMHTqUvn370qtXL+rWrUtqaiqbNm2iRIkSOoxcCCGEEOLFiY+P5+DBg3z88ccAGBgY0KFDB+bNm6dV78GeGKVKlaJKlSrExcWpZQYGBtStW1d9XbVqVaytrbXqODk5YWtrq76Oi4vD0dERR0dHtaxatWpa+6WmpjJ48GDc3d2xtrbG3NycuLg4taXh2LFj6Ovr4+/vX+R1enp6qt/b29sDcOPGjcfcHfEi6HQgtIWFBVOmTGHKlCmPrKPRaBgzZgxjxox5cYEJIYQQ5K2SGhsbS1JSEjY2Nnh6eqKvr6/rsMQbaN68eWRnZ2sNfFYUBWNjY2bMmPFMz2VmZvbE+wwePJitW7fy3Xff4ebmhomJCR9++CGZmZkAmJiYFOs4hoaG6vf5C4/ld1sXuqXTpEEIIYR4WcXExDBz5kytKb7t7OwIDQ197KelQjxL2dnZLFq0iEmTJtG0aVOtba1bt2bZsmVUrVoVgP3791OhQgUAbt++zdmzZ3F3d9c61p9//km9evWAvBaMO3fuaNV5mLu7O1evXuXq1atqa8Pp06e5c+cO1apVA2DPnj107dqVDz74AMhrebh06ZJ6jBo1apCbm0tMTIzW+lvi1aHT7klCCCHEyygmJoawsDBcXV2ZPXs2mzZtYvbs2bi6uhIWFkZMTIyuQxRvkA0bNnD79m169OhB9erVtb7atm2r1UVpzJgxbN++nZMnT9K1a1dKly5N69at1e2Ghob07duXAwcOcPjwYbp27UqDBg3UJKIwgYGB1KhRg+DgYI4cOcLBgwfp3Lkz/v7+eHt7A1CpUiV14PTx48f55JNPtFoInJ2d6dKlC927d2fdunUkJCQQHR3Nzz///OxvmHguJGkQQgghHpCTk8PMmTPx8fEhIiICDw8PTE1N8fDwICIiAh8fH2bNmkVOTo6uQxVviHnz5hEYGIiVlVWBbW3btuXPP/8kNjYWgAkTJtC/f3/q1KnD9evX+fXXX7XWWTA1NWXYsGF88sknNGzYEHNzc1asWFHk+TUaDevXr6dkyZL4+fkRGBiIq6ur1n6TJ0+mZMmS+Pr60rJlS5o1a0bt2rW1jjN79mx1LGvVqlXp2bMn9+7de5pbI14gjaI8NFfXayYlJQUrKyuSk5NlcTchhBCPdfToUfr378/s2bPx8PAosP3kyZOEhIQwdepUvLy8dBBh0XT1dy89PZ2EhARcXFxkshIdiI6O5q233uL27dtYW1sXWmfBggUMGDCAO3fuvNDYxMvrSd630tIghBBCPCApKQnIWyuoMK6urlr1hBDiTSBJgxBCCPEAGxsbABISEgrdfvHiRa16QgjxJpCkQQghhHiAp6cndnZ2LF68uMBUj7m5uSxZsgR7e3ut+eSF0LWAgAAURXlk1ySArl27Stck8Z9J0iCEEEI8QF9fn9DQUPbt28eIESM4efIkaWlpnDx5khEjRrBv3z5CQkJkvQYhxBtF1mkQQgghHuLv78+YMWOYOXMmISEharm9vT1jxoyRdRqEEG8caWkQQgghHuHhCQZlZVohxJtKkgYhhBDiIfmLu1WsWFFrcbeKFSvK4m5CiDeSJA1CCCHEA2RxNyGEKEiSBiGEEOIBsbGxXL9+nU6dOqGnp/1nUk9Pj44dO5KYmKiuwCuEEG8CSRqEEEKIB8jibuJJBQQEMGDAAF2H8UZzdnZmypQpug7jtSZJgxBCCPEAWdztzdK1a1dat26tVbZq1SpKlCjBpEmTdBPUM1bYNT4PMTExvP3225QqVQpTU1MqVapEly5dyMzMfO7nPnToEL169Xru53mTSdIghBBCPEAWd3uz/fTTTwQHBzN79my++OILncWRk5PzSs3Wdfr0aYKCgvD29mbnzp2cOHGC6dOnY2Rk9FTjf4qbcNja2mJqavqfzyMeT5IGIYQQ4gGyuNub65tvvqFv374sX76cbt26AYV/Sj9gwAACAgK0yrKzs+nTpw9WVlaULl2akSNHak3Zm5GRweDBgylXrhxmZmbUr1+f6OhodfuCBQuwtrbml19+oVq1ahgbG3PlyhWcnZ2JiIige/fuWFhYUKFCBX788Uetc1+9epX27dtjbW1NqVKlaNWqFZcuXQIgPDychQsXsn79ejQaDRqNRj1vUfsBREdHU69ePczMzLC2tqZhw4Zcvny50Hu3ZcsW7Ozs+Oabb6hevToVK1YkKCiIuXPnYmJiotbbvXs3jRs3xsTEBEdHR/r168e9e/fU7c7OzowdO5bOnTtjaWlJr1698PX1ZdiwYVrnu3nzJoaGhuzcuVPd78HuSXfu3KF3796ULVuWEiVKUL16dTZs2FDsOERBkjQIIYQQD8lf3O3ixYuEhIQQFBRESEgICQkJsrjba2rYsGGMHTuWDRs28MEHHzzx/gsXLsTAwICDBw8ydepUJk+ezE8//aRu79OnD/v27WP58uXExsbSrl07goKCOHfunFonLS2NiRMn8tNPP3Hq1CnKlCkDwKRJk/D29ubo0aOEhITw+eefEx8fD0BWVhbNmjXDwsKCXbt2sWfPHszNzQkKCiIzM5PBgwfTvn17goKCSExMJDExEV9f38ful52dTevWrfH39yc2NpZ9+/bRq1cvNBpNoddvZ2dHYmKi+hBfmAsXLhAUFETbtm2JjY1lxYoV7N69mz59+mjV++6776hZsyZHjx5l5MiRBAcHs3z5cq0kbMWKFTg4ONC4ceMC58nNzaV58+bs2bOHJUuWcPr0aSZMmKAm+sWNQzxEec0lJycrgJKcnKzrUIQQQrxisrOzlSNHjihbt25Vjhw5omRnZ+s6pMfS1d+9+/fvK6dPn1bu37//Qs/7tLp06aIYGRkpgLJ9+/ZCt7dq1UqrrH///oq/v7/62t/fX3F3d1dyc3PVsmHDhinu7u6KoijK5cuXFX19feXvv//WOs4777yjDB8+XFEURYmMjFQA5dixY1p1nJyclI4dO6qvc3NzlTJlyiizZ89WFEVRFi9erFSpUkXr3BkZGYqJiYmyefPmR17D4/ZLSkpSACU6OrrQ+/aw7OxspWvXrgqg2NnZKa1bt1amT5+u9XvYo0cPpVevXlr77dq1S9HT01N/b5ycnJTWrVtr1blx44ZiYGCg7Ny5Uy3z8fFRhg0bpnWfvv/+e0VRFGXz5s2Knp6eEh8fX2isxYnjTfEk71tpaRBCCCEeQV9fHy8vLwIDA/Hy8pIuSa8pT09PnJ2dGTVqFKmpqf/pGA0aNND6FN7Hx4dz586Rk5PDiRMnyMnJoXLlypibm6tfMTExXLhwQd3HyMio0LEyD5ZpNBrs7Oy4ceMGAMePH+f8+fNYWFioxy1VqhTp6elax37Y4/YrVaoUXbt2pVmzZrRs2ZKpU6eSmJj4yOPp6+sTGRnJX3/9xTfffEO5cuXUdU7y9zt+/DgLFizQugfNmjUjNzdXa+IBb29vrWPb2trStGlToqKigLxJCvbt20dwcHChsRw7dozy5ctTuXLlR157ceIQ2gx0HYAQQgghhC6VK1eOVatW8dZbbxEUFMTvv/+OhYUFkLc2h/JAtxjI6xL0JFJTU9HX1+fw4cMFEk9zc3P1exMTk0K7/xgaGmq91mg06iDp1NRU6tSpoz5QP8jW1rbImB63X2RkJP369WPTpk2sWLGCr776iq1bt9KgQYNHHrdcuXJ06tSJTp06MXbsWCpXrswPP/zA6NGjSU1NpXfv3vTr16/AfhUqVFC/NzMzK7A9ODiYfv36MX36dJYuXUqNGjWoUaNGoTE8OIbiUddenDiENkkahBBCCPHGc3JyIiYmRk0cNm3ahIWFBba2tpw8eVKr7rFjxwo8yB84cEDr9f79+6lUqZLaWpWTk8ONGzcK7YP/NGrXrs2KFSsoU6YMlpaWhdYpbAaj4uwH4OXlhZeXF8OHD8fHx4elS5cWmTQ8qGTJktjb26sDjGvXrs3p06dxc3Mr5tX9n1atWtGrVy82bdrE0qVL6dy58yPrenp68tdff3H27NlCWxueJo43mXRPEkIIIYQAHB0diY6O5saNGzRr1oyUlBTefvtt/vzzTxYtWsS5c+cYNWpUgSQC4MqVKwwaNIj4+HiWLVvG9OnT6d+/PwCVK1cmODiYzp07s2bNGhISEjh48CDjx49n48aNTxVzcHAwpUuXplWrVuzatYuEhASio6Pp168ff/31F5A3s1BsbCzx8fH8+++/ZGVlPXa/hIQEhg8fzr59+7h8+TJbtmzh3LlzuLu7FxrHnDlz+Pzzz9myZQsXLlzg1KlTDBs2jFOnTtGyZUsgb7D53r176dOnD8eOHePcuXOsX7++WAOQzczMaN26NSNHjiQuLo6PP/74kXX9/f3x8/Ojbdu2bN26lYSEBH7//Xc2bdr01HG8yaSlQQghhBDPzd937nP73vNf3CtfSTMjylkX3T2lKOXLlyc6Opq33nqLZs2asXnzZkaOHMnQoUNJT0+ne/fudO7cmRMnTmjt17lzZ+7fv0+9evXQ19enf//+WouNRUZG8vXXX/PFF1/w999/U7p0aRo0aECLFi3+c6wApqam7Ny5k2HDhtGmTRvu3r1LuXLleOedd9QWhJ49exIdHY23tzepqans2LGDgICAIve7f/8+Z86cYeHChSQlJWFvb09oaCi9e/cuNI569eqxe/duPvvsM65du4a5uTkeHh6sW7dOnW3M09OTmJgY/ve//9G4cWMURaFixYp06NChWNcaHBzMu+++i5+f32O7Ea1evZrBgwfz8ccfc+/ePdzc3JgwYcIzieNNpVEe7qj3mklJScHKyork5OQim9+EEEKI14Gu/u6lp6eTkJCAi4sLJUqUAPIShre/iyYj+8UtUmZsoMcfgwOeKnEQ4k1R2Pv2UaR7khBCCCGei9v3Ml9owgCQkZ37Qls2hHhTSNIghBBCCCGEKJIkDUIIIYQQQogiSdIghBBCCCGEKJIkDUIIIYQQL6mAgAAGDBjw3M+j0WhYt27dI7dfunQJjUbDsWPHnnss4uUkSYMQQggh3lhdu3ZFo9Gg0WgwNDTExcVFnV71VbBgwQI1fj09Pezt7enQoQNXrlx5pudxdHQkMTGR6tWrP9PjileHJA1CCCGEeKMFBQWRmJjIxYsX+f7775kzZw6jRo3SdVjFZmlpSWJiIn///TerV68mPj6edu3aPdNz6OvrY2dnh4GBLPH1ppKkQQghhBBvNGNjY+zs7HB0dKR169YEBgaydetWdXtGRgb9+vWjTJkylChRgkaNGnHo0CF1+4IFC7C2ttY65rp169BoNOrr8PBwatWqxeLFi3F2dsbKyoqPPvqIu3fvqnXu3btH586dMTc3x97enkmTJhUrfo1Gg52dHfb29vj6+tKjRw8OHjxISkqKWmf27NlUrFgRIyMjqlSpwuLFiwscJzExkebNm2NiYoKrqyurVq1Stz3cPSk6OhqNRsP27dvx9vbG1NQUX19f4uPjixWzePVI0iCEEEKI56KkmRHGBi/2UcPYQI+SZkb/ef+TJ0+yd+9ejIz+7xhDhw5l9erVLFy4kCNHjuDm5kazZs24devWEx37woULrFu3jg0bNrBhwwZiYmLUVYoBhgwZQkxMDOvXr2fLli1ER0dz5MiRJzrHjRs3WLt2Lfr6+ujr6wOwdu1a+vfvzxdffMHJkyfp3bs33bp1Y8eOHVr7jhw5krZt23L8+HGCg4P56KOPiIuLK/J8//vf/5g0aRJ//vknBgYGdO/e/YniFa8OaWMSQgghxHNRztqEPwYHvNDF1kqaGT3xatAbNmzA3Nyc7OxsMjIy0NPTY8aMGUDep/+zZ89mwYIFNG/eHIC5c+eydetW5s2bx5AhQ4p9ntzcXBYsWICFhQUAnTp1Yvv27YwbN47U1FTmzZvHkiVLeOeddwBYuHAh5cuXf+xxk5OTMTc3R1EU0tLSAOjXrx9mZmYAfPfdd3Tt2pWQkBAABg0axP79+/nuu+9466231OO0a9eOTz/9FICxY8eydetWpk+fzqxZsx557nHjxuHv7w/Al19+yXvvvUd6evpjVxcWrx5JGoQQQgjx3JSzNnnih/gX7a233mL27Nncu3eP77//HgMDA9q2bQvktQ5kZWXRsGFDtb6hoSH16tV77KfwD3N2dlYTBgB7e3tu3LihniczM5P69eur20uVKkWVKlUee1wLCwuOHDlCVlYWv//+O1FRUYwbN07dHhcXR69evbT2adiwIVOnTtUq8/HxKfD6cbMleXp6al0P5LV2VKhQ4bFxi1eLdE8SQgghxBvNzMwMNzc3atasyfz58zlw4ADz5s0r9v56enooiqJVlpWVVaCeoaGh1muNRkNubu5/C/qh87u5ueHu7s6gQYNo0KABn3/++VMftzgevKb8MRzP4prEy0eSBiGEEEKI/09PT48RI0bw1Vdfcf/+fXXw8J49e9Q6WVlZHDp0iGrVqgFga2vL3bt3uXfvnlrnSdczqFixIoaGhhw4cEAtu337NmfPnn3ia/jyyy9ZsWKFOh7C3d1dK36APXv2qPHn279/f4HX7u7uT3x+8XqSpEEIIYQQ4gHt2rVDX1+fmTNnYmZmxueff86QIUPYtGkTp0+fpmfPnqSlpdGjRw8A6tevj6mpKSNGjODChQssXbqUBQsWPNE5zc3N6dGjB0OGDOGPP/7g5MmTdO3aFT29J39Uc3R05IMPPiAsLAzIG2C9YMECZs+ezblz55g8eTJr1qxh8ODBWvutXLmS+fPnc/bsWUaNGsXBgwfp06fPE59fvJ4kaRBCCCGEeICBgQF9+vThm2++4d69e0yYMIG2bdvSqVMnateuzfnz59m8eTMlS5YE8sYeLFmyhN9++40aNWqwbNkywsPDn/i83377LY0bN6Zly5YEBgbSqFEj6tSp85+uYeDAgWzcuJGDBw/SunVrpk6dynfffYeHhwdz5swhMjKSgIAArX1Gjx7N8uXL8fT0ZNGiRSxbtqxAa4R4c2mUhzvhvWZSUlKwsrIiOTkZS0tLXYcjhBBCPFe6+ruXnp5OQkICLi4uMnOOEK+IJ3nfSkuDEEIIIYQQokgy5aoQQgjxCDk5OcTGxpKUlISNjQ2enp7qgllCCPEmkaRBCCGEKERMTAwzZ87k+vXrapmdnR2hoaHqYlZCCPGmkO5JQgghxENiYmIICwvD1dWV2bNns2nTJmbPno2rqythYWHExMToOkQhhHihJGkQQgghHpCTk8PMmTPx8fEhIiICDw8PTE1N8fDwICIiAh8fH2bNmkVOTo6uQxVCiBdGkgYhhBDiAbGxsVy/fp1OnToVmCNfT0+Pjh07kpiYSGxsrI4iFEKIF0+SBiGEEOIBSUlJALi4uBS63dXVVaueEEK8CSRpEEIIIR5gY2MDQEJCQqHbL168qFVPCCHeBJI0CCGEEA/w9PTEzs6OxYsXk5ubq7UtNzeXJUuWYG9vj6enp44iFEKIF0+mXBVCCCEeoK+vT2hoKGFhYYwYMYKOHTvi6urKxYsXWbJkCfv27WPMmDGyXkNx3bkKaS+wK5epDVg7vrjzvcLmzZvHihUr2LJli65DEcXw77//Uq1aNY4cOUL58uVf+Pk1iqIoL/ysL1BKSgpWVlYkJydjaWmp63CEEEK8Igpbp8He3p6QkJCXep0GXf3dS09PJyEhARcXF0qUKJFXeOcqzKgD2RkvLA4MjKHP4SdOHK5fv8748ePZuHEjf/31F1ZWVri5udGxY0e6dOmCqanpY4+xYMECBgwYwJ07d/5j8C9Oeno6rq6urFy5koYNGwIQHh7OunXrOHbsmFbdS5cu4eLiwtGjR1m3bh2jR49+qnPv2LGDS5cu0a1bNwA0Gg0ODg40adKEiRMnUqZMmUL3W7BggdY+ZcuWxc/Pj2+//ZYKFSo8VUwPio6O5q233uL27dtYW1s/k2PlK126NHXr1mXixInUqFFDLe/atSsLFy4EwMDAgPLly9OuXTvGjBnzf+8nYPDgwdy+fZt58+Y9VVz5Cn3fPoK0NAghhBCF8Pf3p1GjRrIi9NNIS3qxCQPknS8t6YmShosXL9KwYUOsra2JiIigRo0aGBsbc+LECX788UfKlSvH+++//xyDfvFWrVqFpaWlmjAU1+DBg/nss8/U13Xr1qVXr1707NkTgMzMTIyMjNTt/fv3JyUlhcjISLWsVKlSXLp0CUtLS+Lj48nNzeX48eN069aNa9eusXnz5keeP38fRVFISEggJCSEdu3aceDAgSe6jhctPj4eS0tLrl27xpAhQ3jvvfc4f/681r0KCgoiMjKSrKwsDh8+TJcuXdBoNEycOFGt061bN+rUqcO3335LqVKlXug16HRMg7OzMxqNpsBXaGgokJf9hIaGYmNjg7m5OW3btuWff/7RZchCCCHeIPr6+nh5eREYGIiXl5ckDK+pkJAQDAwM+PPPP2nfvj3u7u64urrSqlUrNm7cSMuWLQGYPHkyNWrUwMzMDEdHR0JCQkhNTQXyPlHu1q0bycnJ6vNMeHg4ABkZGQwePJhy5cphZmZG/fr1iY6OBkBRFGxtbVm1apUaT61atbC3t1df7969G2NjY9LS0h4bx71797C0tNQ6HsC6deswMzPj7t27ACxfvly9ridhbm6OnZ2d+qWvr4+FhYX6ukKFClrbTUxMMDY21irLf1DWaDTY2dnh4OBA8+bN6devH9u2beP+/fuPPH/+Pvb29vj6+tKjRw8OHjxISkqKWmf27NlUrFgRIyMjqlSpwuLFi9Vtly5dQqPRaLWm3LlzB41GQ3R0NJcuXVJbBkqWLIlGo6Fr165A3pim8ePH4+LigomJCTVr1ixwnx+lTJky2NnZUbt2bQYMGMDVq1c5c+aMVp38++To6Ejr1q0JDAxk69atWnU8PDxwcHBg7dq1xTrvs6TTpOHQoUMkJiaqX/k3pl27dgAMHDiQX3/9lZUrVxITE8O1a9do06aNLkMWQgghxGskKSmJLVu2EBoaipmZWaF1NBoNkLdOx7Rp0zh16hQLFy7kjz/+YOjQoQD4+voyZcoULC0t1eeawYMHA9CnTx/27dvH8uXLiY2NpV27dgQFBXHu3Dk0Gg1+fn5qEnH79m3i4uK4f/+++lAZExND3bp11S5SRcVhZmbGRx99pPXJPkBkZCQffvghFhYWQF4i4u3t/Qzv5NMzMTEhNzeX7OzsYtW/ceMGa9euRV9fX03o165dS//+/fniiy84efIkvXv3plu3buzYsaNYx3R0dGT16tVAXutAYmIiU6dOBWD8+PEsWrSIH374gVOnTjFw4EA6duz4RCvEJycns3z5cgCtVoaHnTx5kr179xZap169euzatavY53xWdNo9ydbWVuv1hAkTqFixIv7+/iQnJzNv3jyWLl3K22+/DeT9wru7u7N//34aNGigi5CFEEII8Ro5f/48iqJQpUoVrfLSpUuTnp4OQGhoKBMnTmTAgAHqdmdnZ77++ms+++wzZs2ahZGREVZWVuon4fmuXLlCZGQkV65cwcHBAcjr4rNp0yYiIyOJiIggICCAOXPmALBz5068vLyws7MjOjqaqlWrEh0drTWOpqg4AD799FN8fX1JTEzE3t6eGzdu8Ntvv7Ft2zYg75P15ORkNZ4HnThxAnNzc62yFzH89dy5c/zwww94e3uriU1hkpOTMTc3R1EUteWlX79+asL33Xff0bVrV0JCQgAYNGgQ+/fv57vvvtMaW/Ao+vr6arefMmXKqGMaMjIyiIiIYNu2bfj4+AB5a7bs3r2bOXPmPHacU/7A5Xv37gHw/vvvU7VqVa06GzZswNzcnOzsbDIyMtDT02PGjBkFjuXg4MDRo0cfey3P2kszpiEzM5MlS5YwaNAgNBoNhw8fJisri8DAQLVO1apVqVChAvv27Xtk0pCRkUFGxv/1n8xvrsrKyiIrK+v5XoQQQgihY/K37tk4ePAgubm5BAcHq88V27ZtY/z48Zw5c4aUlBSys7NJT08nLS3tkQOlT5w4QU5ODpUrV9Yqz8jIUNf68Pf3p3///ty8eZOYmBgCAgLUpKFHjx7s3btXbUkoThz16tXDw8ODhQsX8uWXX7JkyRKcnJzw8/MDULv/FDbwtUqVKvzyyy9aZX///TcBAQH/7UYWIT8ByM3NJT09nUaNGvHTTz8BaCUuHTt25IcffgDAwsKCI0eOkJWVxe+//05UVBTjxo1T68bFxdGrVy+t8zRs2FBtLfivzp8/T1paGk2aNNEqz8zMxMvLC8jrOnT58mUAGjduzO+//67W27VrF6ampuzfv5+IiAj1eh701ltvMXv2bO7du8f333+PgYEBbdu2LVDPxMRETZhepJcmaVi3bh137txR+41dv34dIyOjAqPWy5YtqzWTxcPGjx9f6Kj+LVu2FGvmAyGEEOJVpouHiVeZm5sbGo2G+Ph4rfL8lb9NTEyAvL7wLVq04PPPP2fcuHGUKlWK3bt306NHDzIzMx/5jJGamoq+vj6HDx8uMCYm/8G4Ro0alCpVipiYGGJiYhg3bhx2dnZMnDiRQ4cOkZWVha+v7xPF8emnnzJz5ky+/PJLIiMj6datm9rNysbGBo1Gw+3btwvEa2RkhJubm1aZgcHzeVzMTwD09PSwt7dX7zWgNebgwVnA9PT01Pjc3d25cOECn3/+uda4haLo6eX1zH+w9aQ4iXb+mJGNGzdSrlw5rW3GxsYA/Pbbb+qxHrwWyFth3tramipVqnDjxg06dOjAzp07teqYmZmp1zZ//nxq1qzJvHnz6NGjh1a9W7duFeit8yK8NEnDvHnzaN68eaFNZU9i+PDhDBo0SH2dkpKCo6MjTZs2lSlXhRBCvPYeHBAqHs/GxoYmTZowY8YM+vbt+8hxDYcPHyY3N5dJkyapD54///yzVh0jIyNycnK0yry8vMjJyeHGjRs0bty40GNrNBoaN27M+vXrOXXqFI0aNcLU1JSMjAzmzJmDt7e3Gldx4oC8T+eHDh3KtGnTOH36NF26dNGKs1q1apw+fZqmTZsW8049ew8mAA97VPnDvvzySypWrMjAgQOpXbs27u7u7NmzR+t69+zZQ7Vq1YD/6xqfmJiothA8PMVs/jiCB3+W1apVw9jYmCtXrjyyK5KTk1OxYg4NDWX8+PGsXbuWDz74oNA6enp6jBgxgkGDBvHJJ59oJSEnT558Li0/j/NSJA2XL19m27ZtrFmzRi2zs7MjMzOTO3fuaLU2/PPPP1p9BR9mbGysZnwPMjQ0xNDQ8JnGLYQQQrxs5G/dk5s1axYNGzbE29ub8PBwPD090dPT49ChQ5w5c4Y6derg5uZGVlYW06dPp2XLluzZs6dAFxNnZ2dSU1PZvn07NWvWxNTUlMqVKxMcHEznzp2ZNGkSXl5e3Lx5k+3bt+Pp6cl7770HQEBAAF988QXe3t5qC4Sfnx9RUVEMGTJEPUdx4oC8mX/atGnDkCFDaNq0aYHFwJo1a8bu3bu1xke8ihwdHfnggw8ICwtjw4YNDBkyhPbt26uznv3666+sWbNGHc9hYmJCgwYNmDBhAi4uLty4cYOvvvpK65hOTk5oNBo2bNjAu+++i4mJCRYWFgwePJiBAweSm5tLo0aNSE5OZs+ePVhaWmolKY9jampKz549GTVqFK1bt1ZbgB7Wrl07hgwZwsyZM9VB9WlpaRw+fJiIiIj/eMf+O53OnpQvMjKSMmXKqG8cgDp16mBoaMj27dvVsvj4eK5cuaIOQBFCCCGEeFoVK1bk6NGjBAYGMnz4cGrWrIm3tzfTp09n8ODBjB07lpo1azJ58mQmTpxI9erViYqKYvz48VrH8fX15bPPPqNDhw7Y2tryzTffAHnPOZ07d+aLL76gSpUqtG7dmkOHDmktSObv709OTo7WJ8gBAQEFyooTR778Lkvdu3cvdNtvv/1GcnLyU9y5l8PAgQPZuHEjBw8epHXr1kydOpXvvvsODw8P5syZQ2RkpNY9nD9/PtnZ2dSpU4cBAwbw9ddfax2vXLlyjB49mi+//JKyZcvSp08fAMaOHcvIkSMZP3487u7uBAUFsXHjRlxcXJ445j59+hAXF8fKlSsfWcfAwIA+ffrwzTffqAOo169fT4UKFR7ZavU86XxF6NzcXFxcXPj444+ZMGGC1rbPP/+c3377jQULFmBpaUnfvn0B2Lt3b7GPLytCCyGEeJPIitD/bUXo19HixYsZOHAg165dK3Tqznbt2lG7dm2GDx+ug+jEf9GgQQP69evHJ5988kyO90qtCL1t2zauXLlSaBb8/fffo6enR9u2bcnIyKBZs2bqdGJCCCGEeMlZO+Y9wKclvbhzmtq88QlDWloaiYmJTJgwgd69ez9yPYBvv/2WX3/99QVHJ/6rf//9lzZt2vDxxx/r5Pw6b2l43qSlQQghxJvkpWppEDoRHh7OuHHj8PPzY/369QXWXRAi35O8b1+KMQ1CCCGEEOLZCA8PJysri+3bt0vCIJ4ZnXdPEtpycnKIjY0lKSkJGxsbPD09C8zrLIQQQgghxIskScNLJCYmhpkzZ2otXmdnZ0doaOhjlycXQgghhBDieZHuSS+JmJgYwsLCcHV1Zfbs2WzatInZs2fj6upKWFgYMTExug5RCCGEEEK8oSRpeAnk5OQwc+ZMfHx8iIiIwMPDA1NTUzw8PIiIiMDHx4dZs2YVWGVSCCGEEEKIF0GShpdAbGws169fp1OnTuqS8Pn09PTo2LEjiYmJxMbG6ihCIYQQQgjxJpOk4SWQlJQ3f/WjVhR0dXXVqieEEEIIIcSLJAOhXwI2NjYAJCQk4OHhUWD7xYsXteoJIYQQr4rE1ERuZ9x+YecraVwSe3P7F3a+V9m8efNYsWIFW7Zs0XUoADg7OzNgwAAGDBjw3M6RmZlJ5cqVWbVqFd7e3s/tPK8jSRpeAp6entjZ2bF48WIiIiK0uijl5uayZMkS7O3t8fT01GGUQgghxJNJTE2kxboWZOZkvrBzGukbsaH1hidOHK5fv8748ePZuHEjf/31F1ZWVri5udGxY0e6dOmCqanpY4+xYMECBgwYwJ07d/5j9C9Oeno6I0eOZOXKlWpZeHg4o0ePBvK6Rzs4ONC8eXMmTJhAqVKldBXqM2VkZMTgwYMZNmwY27dv13U4rxTpnvQS0NfXJzQ0lH379jFixAhOnjxJWloaJ0+eZMSIEezbt4+QkBBZr0EIIcQr5XbG7ReaMABk5mQ+ccvGxYsX8fLyYsuWLURERHD06FH27dvH0KFD2bBhA9u2bXtO0erOqlWrsLS0pGHDhlrlHh4eJCYmcuXKFSIjI9m0aROff/65jqJ8PoKDg9m9ezenTp3SdSivFEkaXhL+/v6MGTOGixcvEhISQlBQECEhISQkJDBmzBhZp0EIIYR4TkJCQjAwMODPP/+kffv2uLu74+rqSqtWrdi4cSMtW7YEYPLkydSoUQMzMzMcHR0JCQkhNTUVgOjoaLp160ZycjIajQaNRkN4eDgAGRkZDB48mHLlymFmZkb9+vWJjo4GQFEUbG1tWbVqlRpPrVq1sLf/v5aS3bt3Y2xsTFpa2mPjuHfvHpaWllrHA1i3bh1mZmbcvXsXgOXLl6vX9SADAwPs7OwoV64cgYGBtGvXjq1bt6rbc3Jy6NGjBy4uLpiYmFClShWmTp2qdYyuXbvSunVrvvvuO+zt7bGxsSE0NJSsrCy1zo0bN2jZsiUmJia4uLgQFRVVIJYrV67QqlUrzM3NsbS0pH379vzzzz/q9vDwcGrVqsX8+fOpUKEC5ubmhISEkJOTwzfffIOdnR1lypRh3LhxWsctWbIkDRs2ZPny5QXOKR5Nuie9RPz9/WnUqJGsCC2EEEK8IElJSWoLg5mZWaF1NBoNkNdlZ9q0abi4uKgf8g0dOpRZs2bh6+vLlClTCAsLIz4+HgBzc3MA+vTpw+nTp1m+fDkODg6sXbuWoKAgTpw4QaVKlfDz8yM6OpoPP/yQ27dvExcXh4mJCWfOnKFq1arExMRQt25dtYtUUXGYmZnx0UcfERkZyYcffqheQ/5rCwsLIC8R6dSpU5H35tKlS2zevBkjIyO1LDc3l/Lly7Ny5UpsbGzYu3cvvXr1wt7envbt26v1duzYgb29PTt27OD8+fN06NCBWrVq0bNnTyAvsbh27Ro7duzA0NCQfv36cePGDa3z5CcMMTExZGdnExoaSocOHdSEC+DChQv8/vvvbNq0iQsXLvDhhx9y8eJFKleuTExMDHv37qV79+4EBgZSv359db969eqxa9euIq9faJOk4SWjr6+Pl5eXrsMQQghB3qeq8kHO6+38+fMoikKVKlW0ykuXLk16ejoAoaGhTJw4UWuArrOzM19//TWfffYZs2bNwsjICCsrKzQaDXZ2dmq9/G4+V65cwcHBAYDBgwezadMmIiMjiYiIICAggDlz5gCwc+dOvLy8sLOzIzo6mqpVqxIdHa3V46CoOAA+/fRTfH19SUxMxN7enhs3bvDbb7+p3azu3LlDcnKyGs+DTpw4gbm5OTk5Oer1T548Wd1uaGiojnuAvJkf9+3bx88//6yVNJQsWZIZM2agr69P1apVee+999i+fTs9e/bk7Nmz/P777xw8eJC6desCeYOy3d3d1f23b9/OiRMnSEhIwNHREYBFixbh4eHBoUOH1P1yc3OZP38+FhYWVKtWjbfeeov4+Hh+++039PT0qFKlChMnTmTHjh1aSYODgwOXL18ucP3i0SRpEEIIIQoRExPDzJkzuX79ulpmZ2dHaGiodBl9Axw8eJDc3FyCg4PJyMgAYNu2bYwfP54zZ86QkpJCdnY26enppKWlPXKg9IkTJ8jJyaFy5cpa5RkZGeqsiP7+/vTv35+bN28SExNDQECAmjT06NGDvXv3MnToUHXfx8VRr149PDw8WLhwIV9++SVLlizByckJPz8/AO7fvw9AiRIlCsRbpUoVfvnlF9LT01myZAnHjh2jb9++WnVmzpzJ/PnzuXLlCvfv3yczM5NatWpp1fHw8NBKsO3t7Tlx4gQAcXFxGBgYUKdOHXV71apVsba2Vl/HxcXh6OioJgwA1apVw9ramri4ODVpcHZ2VltPAMqWLYu+vr7WpDJly5bVasUAMDExUbt7ieKRMQ1CCCHEQ2JiYggLC8PV1ZXZs2ezadMmZs+ejaurK2FhYcTExOg6RPGMuLm5odFo1C5F+VxdXXFzc8PExATI66rTokULPD09Wb16NYcPH2bmzJlA3jSej5Kamoq+vj6HDx/m2LFj6ldcXJw6FqBGjRqUKlWKmJgYNWkICAggJiaGQ4cOkZWVha+v7xPF8emnn7JgwQIgr2tSt27d1G5WNjY2aDQabt8uOGDcyMgINzc3qlevzoQJE9DX19dqWVi+fDmDBw+mR48ebNmyhWPHjtGtW7cC98DQ0FDrtUajITc395H36b8q7DzFOfetW7ewtbV95vG8ziRpEEIIIR6Qk5PDzJkz8fHxISIiAg8PD0xNTfHw8CAiIgIfHx9mzZpFTk6OrkMVz4CNjQ1NmjRhxowZ3Lt375H1Dh8+TG5uLpMmTaJBgwZUrlyZa9euadUxMjIq8Hvh5eVFTk4ON27cwM3NTesrvxuTRqOhcePGrF+/nlOnTtGoUSM8PT3JyMhgzpw5eHt7q+MtihMHQMeOHbl8+TLTpk3j9OnTdOnSRSvOatWqcfr06cfen6+++orvvvtOPceePXvw9fUlJCQELy8v3NzcuHDhwmOP86CqVauSnZ3N4cOH1bL4+HitqWrd3d25evUqV69eVctOnz7NnTt3qFat2hOdrzAnT56U7uBPSJIGIYQQ4gGxsbFcv36dTp06aXVxgLwBqB07diQxMZHY2FgdRSietVmzZpGdnY23tzcrVqwgLi6O+Ph4lixZwpkzZ9DX18fNzY2srCymT5/OxYsXWbx4MT/88IPWcZydnUlNTWX79u38+++/pKWlUblyZYKDg+ncuTNr1qwhISGBgwcPqmtC5AsICGDZsmXUqlULc3Nz9PT08PPzIyoqSqs7XHHigLwxBW3atGHIkCE0bdqU8uXLa21v1qwZu3fvfuy98fHxwdPTk4iICAAqVarEn3/+yebNmzl79iwjR47k0KFDT3S/q1SpQlBQEL179+bAgQMcPnyYTz/9VG3VAQgMDKRGjRoEBwdz5MgRDh48SOfOnfH3938mi7Lt2rWLpk2bPvVx3iSSNAghhBAPSEpKAvIGeBbG1dVVq5549VWsWJGjR48SGBjI8OHDqVmzJt7e3kyfPp3BgwczduxYatasyeTJk5k4cSLVq1cnKiqK8ePHax3H19eXzz77jA4dOmBra8s333wD5HUP6ty5M1988QVVqlShdevWHDp0iAoVKqj7+vv7k5OTQ0BAgFoWEBBQoKw4ceTr0aMHmZmZdO/evdBtv/32G8nJyY+9PwMHDuSnn37i6tWr9O7dmzZt2tChQwfq169PUlISISEhjz3GwyIjI3FwcMDf3582bdrQq1cvypQpo27XaDSsX7+ekiVL4ufnR2BgIK6urqxYseKJz/Wwffv2kZycrDW7lHg8jaIoiq6DeJ5SUlKwsrIiOTkZS0tLXYcjhBDiJXf06FH69+/P7Nmz8fDwKLD95MmThISEMHXq1Jeye4Ou/u6lp6eTkJCAi4uLOsD2VVoR+nW0ePFiBg4cyLVr17SmTc3Xrl07ateuzfDhw3UQne506NCBmjVrMmLECF2HonOFvW8fRWZPEkIIIR7g6emJnZ0dixcvJiIiQquLUm5uLkuWLMHe3h5PT08dRvlqsDe3Z0PrDU+8QvPTKGlc8o1PGNLS0khMTGTChAn07t270IQB4Ntvv+XXX399wdHpVmZmJjVq1GDgwIG6DuWVIy0NQgghxEPyZ0/y8fGhY8eOuLq6cvHiRZYsWcK+ffsYM2bMSzvt6svU0iB0Izw8nHHjxuHn58f69evVReaEeNiTvG8laRBCCCEKUdg6Dfb29oSEhLy0CQNI0iCEKD7pniSEEEI8JX9/fxo1aiQrQgshBJI0CCGEEI+kr6//Ug52FkKIF02mXBVCCCGEEEIUSZIGIYQQQgghRJEkaRBCCCGEEEIUSZIGIYQQQgghRJFkILQQQgghnpusa9fIvv3iFnczKFkSQweHF3a+V9m8efNYsWIFW7Zs0XUo4gGnT5+madOmxMfHY2ZmputwVJI0CCGEEOK5yLp2jQtBzVEyM1/YOTVGRlTc9PsTJw7Xr19n/PjxbNy4kb/++gsrKyvc3Nzo2LEjXbp0wdTU9LHHWLBgAQMGDODOnTv/MfoXJz09nZEjR7Jy5Uq1LDw8nHXr1nHs2DGdxOTs7MyAAQMYMGBAkfUWLFhAt27dANBoNJQtWxY/Pz++/fZbKlSo8AIifXYCAgKoVasWU6ZMUcuqVatGgwYNmDx5MiNHjtRdcA+R7klCCCGEeC6yb99+oQkDgJKZ+cQtGxcvXsTLy4stW7YQERHB0aNH2bdvH0OHDmXDhg1s27btOUWrO6tWrcLS0pKGDRvqOpT/xNLSksTERP7++29Wr15NfHw87dq103VYz0y3bt2YPXs22dnZug5FJUmDEEIIId5oISEhGBgY8Oeff9K+fXvc3d1xdXWlVatWbNy4kZYtWwIwefJkatSogZmZGY6OjoSEhJCamgpAdHQ03bp1Izk5GY1Gg0ajITw8HICMjAwGDx5MuXLlMDMzo379+kRHRwOgKAq2trasWrVKjadWrVrY29urr3fv3o2xsTFpaWmPjePevXtYWlpqHQ9g3bp1mJmZcffuXQCWL1+uXldxXb16lfbt22NtbU2pUqVo1aoVly5dUrcfOnSIJk2aULp0aaysrPD39+fIkSPqdkVRCA8Pp0KFChgbG+Pg4EC/fv2AvE/cL1++zMCBA9X7VxSNRoOdnR329vb4+vrSo0cPDh48SEpKilpn/fr11K5dmxIlSuDq6sro0aO1HsLPnTuHn58fJUqUoFq1amzduhWNRsO6deuAvJ+pRqPRajk6duwYGo1G67p3795N48aNMTExwdHRkX79+nHv3j11+6xZs6hUqRIlSpSgbNmyfPjhhwB07dqVmJgYpk6dql5z/nGbNGnCrVu3iImJKdbP5kWQpEEIIYQQb6ykpCS2bNlCaGjoI/uP5z/A6unpMW3aNE6dOsXChQv5448/GDp0KAC+vr5MmTJF/QQ8MTGRwYMHA9CnTx/27dvH8uXLiY2NpV27dgQFBXHu3Dk0Gg1+fn5qEnH79m3i4uK4f/8+Z86cASAmJoa6deuqXaSKisPMzIyPPvqIyMhIrWuIjIzkww8/xMLCAsh70PX29i72fcrKyqJZs2ZYWFiwa9cu9uzZg7m5OUFBQWT+/9aku3fv0qVLF3bv3s3+/fupVKkS7777rpqorF69mu+//545c+Zw7tw51q1bR40aNQBYs2YN5cuXZ8yYMer9K64bN26wdu1a9PX11RXbd+3aRefOnenfvz+nT59mzpw5LFiwgHHjxgGQm5tLmzZtMDIy4sCBA/zwww8MGzas2OfMd+HCBYKCgmjbti2xsbGsWLGC3bt306dPHwD+/PNP+vXrx5gxY4iPj2fTpk34+fkBMHXqVHx8fOjZs6d6zY6OjgAYGRlRq1Ytdu3a9cQxPS8ypkEIIYR4hJycHGJjY0lKSsLGxgZPT0/1oUS8Hs6fP4+iKFSpUkWrvHTp0qSnpwMQGhrKxIkTtfraOzs78/XXX/PZZ58xa9YsjIyMsLKyUj8Bz3flyhUiIyO5cuUKDv9/nMXgwYPZtGkTkZGRREREEBAQwJw5cwDYuXMnXl5e2NnZER0dTdWqVYmOjsbf3189ZlFxAHz66af4+vqSmJiIvb09N27c4LffflO7Wd25c4fk5GQ1nuJYsWIFubm5/PTTT2oSFRkZibW1NdHR0TRt2pS3335ba58ff/wRa2trYmJiaNGiBVeuXMHOzo7AwEAMDQ2pUKEC9erVA6BUqVLo6+tjYWGhdf8eJTk5GXNzcxRFUVtg+vXrpyZ+o0eP5ssvv6RLly4AuLq6MnbsWIYOHcqoUaPYtm0bZ86cYfPmzep9iIiIoHnz5sW+JwDjx48nODhY/ZlUqlSJadOm4e/vz+zZs7ly5QpmZma0aNECCwsLnJyc1FXmraysMDIywtTUtNBrdnBw4PLly08Uz/MkSYMQQghRiJiYGGbOnMn169fVMjs7O0JDQ7Ue4MTr6eDBg+Tm5hIcHExGRgYA27ZtY/z48Zw5c4aUlBSys7NJT08nLS3tkQOlT5w4QU5ODpUrV9Yqz8jIwMbGBgB/f3/69+/PzZs3iYmJISAgQE0aevTowd69e9WWhOLEUa9ePTw8PFi4cCFffvklS5YswcnJSf2E+/79+wCUKFGi2Pfj+PHjnD9/Xm2pyJeens6FCxcA+Oeff/jqq6+Ijo7mxo0b5OTkkJaWxpUrVwBo164dU6ZMwdXVlaCgIN59911atmyJgcGjH0fNzc3V7zt27MgPP/wAgIWFBUeOHCErK4vff/+dqKgotRUhP949e/ZoleXk5Kj3KS4uDkdHR63EycfHp9j348HzxMbGEhUVpZYpikJubi4JCQk0adIEJycn9ZqDgoL44IMPijWw3sTERE2IXgaSNAghhBAPiYmJISwsDB8fH0aNGoWLiwsJCQksXryYsLAwxowZI4nDa8LNzQ2NRkN8fLxWuaurK5D34AZw6dIlWrRoweeff864ceMoVaoUu3fvpkePHmRmZj7yITA1NRV9fX0OHz5coJUq/4G4Ro0alCpVipiYGGJiYhg3bhx2dnZMnDiRQ4cOkZWVha+v7xPF8emnnzJz5ky+/PJLIiMj6datm9pCYGNjg0aj4fYTDBhPTU2lTp06Wg/H+WxtbQHo0qULSUlJTJ06FScnJ4yNjfHx8VG7Lzk6OhIfH8+2bdvYunUrISEhfPvtt8TExGBoaFjoeR+cycnS0lL9Xk9PDzc3NwDc3d25cOECn3/+OYsXL1bjHT16NG3atClwzOImS3p6eb34FUVRy7KysrTqpKam0rt3b3VsxoMqVKiAkZERR44cITo6mi1bthAWFkZ4eDiHDh3C2tq6yPPfunWLihUrFivWF0GSBiGEEOIBOTk5zJw5Ex8fHyIiItQHBw8PDyIiIhgxYgSzZs2iUaNG0lXpNWBjY0OTJk2YMWMGffv2feS4hsOHD5Obm8ukSZPU34mff/5Zq46RkRE5OTlaZV5eXuTk5HDjxg0aN25c6LE1Gg2NGzdm/fr1nDp1ikaNGmFqakpGRgZz5szB29tbjas4cUDep/JDhw5l2rRpnD59Wu2mkx9ntWrV1PUAiqN27dqsWLGCMmXKaD28P2jPnj3MmjWLd999F8gbOP3vv/9q1TExMaFly5a0bNmS0NBQqlatyokTJ6hdu3ah9y8/MXicL7/8kooVKzJw4EBq165N7dq1iY+Pf+T+7u7uXL16Ve3CBbB//36tOvnJUGJiIiVLlgQoMB1t7dq1OX36dJFxGhgYEBgYSGBgIKNGjcLa2po//vhDHVPx8DXnO3nypDpo+mUgA6GFEEKIB8TGxnL9+nU6deqkPpTl09PTo2PHjiQmJhIbG6ujCMWzNmvWLLKzs/H29mbFihXExcURHx/PkiVLOHPmDPr6+ri5uZGVlcX06dO5ePEiixcvVrvK5HN2diY1NZXt27fz77//kpaWRuXKlQkODqZz586sWbOGhIQEDh48qK4JkS8gIIBly5ZRq1YtzM3N0dPTw8/Pj6ioKK1WreLEAVCyZEnatGnDkCFDaNq0KeXLl9fa3qxZM3bv3l1gv/v373Ps2DGtrwsXLhAcHEzp0qVp1aoVu3btIiEhgejoaPr168dff/0F5PXnX7x4MXFxcRw4cIDg4GC1pQby1leYN28eJ0+e5OLFiyxZsgQTExOcnJzU+7dz507+/vvvAsnG4zg6OvLBBx8QFhYGQFhYGIsWLWL06NGcOnWKuLg4li9fzldffQVAYGAglStXpkuXLhw/fpxdu3bxv//9T+uYbm5uODo6Eh4ezrlz59i4cSOTJk3SqjNs2DD27t1Lnz59OHbsGOfOnWP9+vXqQOgNGzYwbdo0jh07xuXLl1m0aBG5ubnqGBpnZ2cOHDjApUuX+Pfff8nNzQXyWpT+/vtvAgMDn+g+PE+SNAghhBAPSEpKAsDFxaXQ7fndVvLriVdfxYoVOXr0KIGBgQwfPpyaNWvi7e3N9OnTGTx4MGPHjqVmzZpMnjyZiRMnUr16daKiohg/frzWcXx9ffnss8/o0KEDtra2fPPNN0DegOHOnTvzxRdfUKVKFVq3bs2hQ4e0FiLz9/cnJyeHgIAAtSwgIKBAWXHiyJffZal79+6Fbvvtt99ITk7WKj979ixeXl5aX71798bU1JSdO3dSoUIF2rRpg7u7Oz169CA9PV1teZg3bx63b9+mdu3adOrUiX79+lGmTBn12NbW1sydO5eGDRvi6enJtm3b+PXXX9WxHWPGjOHSpUtUrFhR/ZT/SQwcOJCNGzdy8OBBmjVrxoYNG9iyZQt169alQYMGfP/992qCoqenx9q1a7l//z716tXj008/1Rr/AGBoaMiyZcs4c+YMnp6eTJw4ka+//lqrjqenJzExMZw9e5bGjRvj5eVFWFiYOlbC2tqaNWvW8Pbbb+Pu7s4PP/zAsmXL8PDwAPIGxevr61OtWjVsbW3V8R/Lli2jadOmarwvA43yYEet11BKSgpWVlYkJyc/sjlNCCGEyHf06FH69+/P7Nmz1T/sDzp58iQhISFMnTpVnQXlZaKrv3vp6ekkJCTg4uKi9hl/lVaEfh0tXryYgQMHcu3aNYyMjApsb9euHbVr12b48OE6iO7lpNFoWLt2La1bt9ZZDJmZmVSqVImlS5c+98X3CnvfPoqMaRBCCCEe4OnpiZ2dHYsXL9Ya0wB5c7svWbIEe3t7PD09dRjlq8HQwYGKm35/4hWan4ZByZJvfMKQlpZGYmIiEyZMoHfv3oUmDADffvstv/766wuOTjzOlStXGDFixEu3WrckDUIIIcQD9PX1CQ0NJSwsjBEjRtCxY0dcXV3VPtj79u1jzJgxMgi6mAwdHN74h/gX7ZtvvmHcuHH4+fkV2Yrg7OxM3759X2Bkojjc3NyKPQD8RZLuSUIIIUQhClunwd7enpCQkJd6utWXqXuSEOLlJt2ThBBCiKfk7+9Po0aNZEVoIYRAkgYhhBDikfT19V/Kwc5CCPGiyZSrQgghhBBCiCJJ0iCEEEIIIYQokiQNQgghhBBCiCJJ0iCEEEIIIYQokiQNQgghhBBvoHnz5tG0aVNdh/FMRUdHo9FouHPnjq5DeSRnZ2emTJmi6zCemCQNQgghhHjjXb9+nf79++Pm5kaJEiUoW7YsDRs2ZPbs2aSlpRXrGAsWLMDa2vr5BvqMpKenM3LkSEaNGqWWhYeHU6tWLZ3F9LiHaY1GU+RXeHj4C4v1VfCsf54y5aoQQggh3mgXL16kYcOGWFtbExERQY0aNTA2NubEiRP8+OOPlCtXjvfff1/XYT5Tq1atwtLSkoYNG+o6lGJLTExUv1+xYgVhYWHEx8erZebm5vz5559PfNzMzEyMjIyeSYyvM2lpEEIIIcQbLSQkBAMDA/7880/at2+Pu7s7rq6utGrVio0bN9KyZUsAJk+eTI0aNTAzM8PR0ZGQkBBSU1OBvG4x3bp1Izk5ucAn3xkZGQwePJhy5cphZmZG/fr1iY6OBkBRFGxtbVm1apUaT61atbC3t1df7969G2NjY7XFo6g47t27h6WlpdbxANatW4eZmRl3794FYPny5ep1FdfVq1dp37491tbWlCpVilatWnHp0iV1+6FDh2jSpAmlS5fGysoKf39/jhw5om5XFIXw8HAqVKiAsbExDg4O9OvXD4CAgAAuX77MwIED1fv3MDs7O/XLysoKjUajVWZubq7WPXz4MN7e3piamuLr66uVXOR/Av/TTz9prYR85coVWrVqhbm5OZaWlrRv355//vlH3a9r1660bt1aK6YBAwYQEBCgvr579y7BwcGYmZlhb2/P999/T0BAAAMGDNDaLy0tje7du2NhYUGFChX48ccf1W2XLl1Co9GwfPlyfH19KVGiBNWrVycmJkatU1ir1rp169T7tmDBAkaPHs3x48fV+7lgwYIC9/RJ6Dxp+Pvvv+nYsSM2NjaYmJhQo0YNrSxRURTCwsKwt7fHxMSEwMBAzp07p8OIhRBCCPG6SEpKYsuWLYSGhmJmZlZonfwHMT09PaZNm8apU6dYuHAhf/zxB0OHDgXA19eXKVOmYGlpSWJiIomJiQwePBiAPn36sG/fPpYvX05sbCzt2rUjKCiIc+fOodFo8PPzU5OI27dvExcXx/379zlz5gwAMTEx1K1bF1NT08fGYWZmxkcffURkZKTWNURGRvLhhx9iYWEB5CUi3t7exb5PWVlZNGvWDAsLC3bt2sWePXswNzcnKCiIzMxMIO+BuUuXLuzevZv9+/dTqVIl3n33XTVRWb16Nd9//z1z5szh3LlzrFu3jho1agCwZs0aypcvz5gxY9T79zT+97//MWnSJP78808MDAzo3r271vbz58+zevVq1qxZw7Fjx8jNzaVVq1bcunWLmJgYtm7dysWLF+nQocMTnXfQoEHs2bOHX375ha1bt7Jr1y6txCnfpEmT8Pb25ujRo4SEhPD5559rJTYAQ4YM4YsvvuDo0aP4+PjQsmVLkpKSihVHhw4d+OKLL/Dw8FDv55NeSwGKDt26dUtxcnJSunbtqhw4cEC5ePGisnnzZuX8+fNqnQkTJihWVlbKunXrlOPHjyvvv/++4uLioty/f79Y50hOTlYAJTk5+XldhhBCvHAZGRnKihUrlO+//15ZsWKFkpGRoeuQxEtCV3/37t+/r5w+fbrYf59fFvv371cAZc2aNVrlNjY2ipmZmWJmZqYMHTq00H1Xrlyp2NjYqK8jIyMVKysrrTqXL19W9PX1lb///lur/J133lGGDx+uKIqiTJs2TfHw8FAURVHWrVun1K9fX2nVqpUye/ZsRVEUJTAwUBkxYsQjr+HhOA4cOKDo6+sr165dUxRFUf755x/FwMBAiY6OVhRFUW7fvq0Ays6dO7WOM2rUKKVmzZqFnmPx4sVKlSpVlNzcXLUsIyNDMTExUTZv3lzoPjk5OYqFhYXy66+/KoqiKJMmTVIqV66sZGZmFlrfyclJ+f777x95nQ8q7F4riqLs2LFDAZRt27apZRs3blQA9Xdz1KhRiqGhoXLjxg21zpYtWxR9fX3lypUratmpU6cUQDl48KCiKIrSpUsXpVWrVlrn69+/v+Lv768oiqKkpKQohoaGysqVK9Xtd+7cUUxNTZX+/ftrXWfHjh3V17m5uUqZMmXUn3dCQoICKBMmTFDrZGVlKeXLl1cmTpz4yOtfu3at8uCjfVE/z3xP8r7VaUvDxIkTcXR0JDIyknr16uHi4kLTpk2pWLEi5F01U6ZM4auvvqJVq1Z4enqyaNEirl27xrp163QZuhBC6MysWbNo1qwZM2bMYM2aNcyYMYNmzZoxa9YsXYcmxGvj4MGDHDt2DA8PDzIyMgDYtm0b77zzDuXKlcPCwoJOnTqRlJRU5EDpEydOkJOTQ+XKlTE3N1e/YmJiuHDhAgD+/v6cPn2amzdvEhMTQ0BAAAEBAURHR5OVlcXevXu1usA8Lo569erh4eHBwoULAViyZAlOTk74+fkBcP/+fQC1W05xHD9+nPPnz2NhYaFeQ6lSpUhPT1ev459//qFnz55UqlQJKysrLC0tSU1N5cqVKwC0a9eO+/fv4+rqSs+ePVm7di3Z2dlFnvfBe/bZZ58VO15PT0/1+/yuXjdu3FDLnJycsLW1VV/HxcXh6OiIo6OjWlatWjWsra2Ji4sr1jkvXrxIVlYW9erVU8usrKyoUqVKkfHld7N6MD4AHx8f9XsDAwO8vb2LHcvzoNOk4ZdffsHb25t27dpRpkwZvLy8mDt3rro9ISGB69evExgYqJZZWVlRv3599u3bp4uQhRBCp2bNmsXy5cuxtLRkyJAhrF27liFDhmBpacny5cslcRDiCbm5uaHRaAp0DXF1dcXNzQ0TExMgr595ixYt8PT0ZPXq1Rw+fJiZM2cCqN1zCpOamoq+vj6HDx/m2LFj6ldcXBxTp04FoEaNGpQqVYqYmBitpCEmJoZDhw6RlZWFr6/vE8Xx6aefqn3YIyMj6datm9rNysbGBo1Gw+3bt4t9n1JTU6lTp47WNRw7doyzZ8/yySefANClSxeOHTvG1KlT2bt3L8eOHcPGxkaNy9HRkfj4eGbNmoWJiQkhISH4+fmRlZX1yPM+eK4xY8YUO15DQ0P1+/zrzs3NVcse1RWtKHp6eiiKolVWVOzFjQ/yYnwwvhcZS3HpdPakixcvMnv2bAYNGsSIESM4dOgQ/fr1w8jIiC5dunD9+nUAypYtq7Vf2bJl1W0Py8jIUD8RAEhJSQHybuTzvplCCPE8ZWZmsnLlSkqWLElUVBRxcXH8+eef2NnZERUVRXBwMCtXrqRLly4yE8gbTP7WPRkbGxuaNGnCjBkz6Nu37yMfJg8fPkxubi6TJk1CTy/vM9eff/5Zq46RkRE5OTlaZV5eXuTk5HDjxg0aN25c6LE1Gg2NGzdm/fr1nDp1ikaNGmFqakpGRgZz5szB29tbjas4cQB07NiRoUOHMm3aNE6fPk2XLl204qxWrRqnT58u9joNtWvXZsWKFZQpUwZLS8tC6+zZs4dZs2bx7rvvAnkDp//991+tOiYmJrRs2ZKWLVsSGhpK1apVOXHiBLVr1y70/rm5uRUrvqfl7u7O1atXuXr1qtracPr0ae7cuUO1atUAsLW15eTJk1r7HTt2TE0AXF1dMTQ05NChQ1SoUAGA5ORkzp49q7byPIn9+/er+2VnZ3P48GH69OmjxnL37l3u3bun/m4cO3ZMa//C7ufT0GnSkJubi7e3NxEREUDeG+vkyZP88MMPWr/cT2L8+PGMHj26QPmWLVvUAURCCPEqOnz4MDk5OTg6OvLRRx+pH4oAWFpa4uzsTGxsLBMnTqROnTo6jFToUnHXFBD/Z9asWTRs2BBvb2/Cw8Px9PRET0+PQ4cOcebMGerUqYObmxtZWVlMnz6dli1bsmfPHn744Qet4zg7O5Oamsr27dupWbMmpqamVK5cmeDgYDp37sykSZPw8vLi5s2bbN++HU9PT9577z0gb/agL774Am9vb3UWID8/P6KiohgyZIh6juLEAVCyZEnatGnDkCFDaNq0KeXLl9fa3qxZM3bv3l1gVp/79+8XePi0sLAgODiYb7/9llatWjFmzBjKly/P5cuXWbNmDUOHDqV8+fJUqlSJxYsX4+3tTUpKCkOGDFFbaiBvRp+cnBzq16+PqakpS5YswcTEBCcnJ/X+7dy5k48++ghjY2NKly79ZD/IpxAYGEiNGjUIDg5mypQpZGdnExISgr+/vzpg/O233+bbb79l0aJF+Pj4sGTJEk6ePImXl5d6n7p06cKQIUMoVaoUZcqUYdSoUejp6RU6G9TjzJw5k0qVKuHu7s7333/P7du31QHd+fdwxIgR9OvXjwMHDhSYHcnZ2ZmEhASOHTtG+fLlsbCwwNjY+D/fI50mDfb29mr2ls/d3Z3Vq1cDeVNrQV4fuQenHvvnn38euVjF8OHDGTRokPo6JSUFR0dHmjZt+sjMWAghXgX5/YZPnDhBgwYN+OSTT3BxcSEhIYGlS5eyf/9+AKytrdVP+sSb58Fk8mVw91Y66akvrvWjhLkhFqWK31cfoGLFihw9epSIiAiGDx/OX3/9hbGxMdWqVWPw4MGEhIRgamrK5MmTmThxIsOHD8fPz4/x48fTuXNn9Ti+vr589tlndOjQgaSkJEaNGkV4eDiRkZF8/fXXfPHFF/z999+ULl2aBg0a0KJFC3Vff39/cnJytMYuBAQEsH79eq2ymjVrPjaOfD169GDp0qUFZg7K3+bt7U1ycjJWVlZq+dmzZ9WH4HzvvPMO27ZtY+fOnQwbNow2bdpw9+5dypUrxzvvvKM+X82bN49evXpRu3ZtHB0diYiIUGeQgrz/myZMmMCgQYPIycmhRo0a/Prrr9jY2AAwZswYevfuTcWKFcnIyCjQ/eZ50mg0rF+/nr59++Ln54eenh5BQUFMnz5drdOsWTNGjhzJ0KFDSU9Pp3v37nTu3JkTJ06odSZPnsxnn31GixYtsLS0ZOjQoVy9evWJxo/kmzBhAhMmTODYsWO4ubnxyy+/qIlUqVKlWLJkCUOGDGHu3Lm88847hIeH06tXL3X/tm3bsmbNGt566y3u3LlDZGQkXbt2/e/3SHmRP5GHfPLJJ1y9epVdu3apZQMHDuTAgQPs3bsXRVFwcHBg8ODBfPHFF0Def4ZlypRhwYIFfPTRR489R0pKClZWViQnJ0vSIIR4peWPWahYsSLz5s1TuyZAXsttjx49uHDhAiEhIcX6/1G8nnT1dy89PZ2EhAStee/v3konKmw/OdnF76v9tPQN9Age0+CJE4fX0eLFixk4cCDXrl0rtMtiu3btqF27NsOHD9dBdG+Ge/fuUa5cOSZNmkSPHj2Ktc+lS5dwcXHh6NGjz32F7sLet4+i04HQAwcOZP/+/URERHD+/HmWLl3Kjz/+SGhoKJCX9Q0YMICvv/6aX375hRMnTtC5c2ccHBwKLK4hhBCvO1dXVyBvBpCHB8zl5uaqM2/k1xNC19JTs15owgCQk537Qls2XkZpaWlcuHCBCRMm0Lt370eOcfr222+1FkQTT+/o0aMsW7aMCxcucOTIEYKDgwFo1aqVjiN7ejpNGurWrcvatWtZtmwZ1atXZ+zYsUyZMkW9wQBDhw6lb9++9OrVi7p165KamsqmTZv+UzOPEEK8yvK7ndy9e5e2bdvyyy+/8O+///LLL7/Qtm1bdQGll617ihDixfrmm2+oWrUqdnZ2RbYiODs707dv3xcY2Zvhu+++o2bNmgQGBnLv3j127dr1QsdnPC867Z70Ikj3JCHE6+Lo0aP079+fwMBAduzYoTUrhr6+Pm+99Rbbtm1j6tSpBfokizfHy9Q96eaVu/wcceiFxZCv/Yi62FaweOHnFeJV8yTdk3Q6EFoIIUTxeXp6YmdnR1paGr///ju//PIL165dw8HBgffff5/w8HDs7e21Fg0SQgghngWddk8SQghRfPr6+oSGhrJv3z7Cw8OpVq0avXr1olq1aoSHh7Nv3z5CQkLQ19fXdahCCPFS6dq16xONhz19+jTly5fn3r17WuUNGjRQZ/l800jSIIQQrxB/f3/GjBnDxYsXCQkJISgoiJCQEBISEhgzZgz+/v66DlGIV1p0dDStWrXC3t4eMzMzatWqRVRUVKF1H5wKNV94eDgajQaNRoOBgQHOzs4MHDiQ1NTU5xz5f3Pp0iU0Gk2BtRmelLOzs3rdJiYmODs70759e/74449nE+gLVq1aNRo0aMDkyZO1yr/66iu+/PLLJ1q9+XUhSYMQQrxi/P39WbZsGVOnTiUsLIypU6eydOlSSRiEeAb27t2Lp6cnq1evJjY2lm7dutG5c2c2bNgA5K1jsHz5cq19jhw5om4H8PDwIDExkUuXLjFx4kR+/PFHder4h2VmZj6/i3mGunbtSnh4eJF1xowZQ2JiIvHx8SxatAhra2sCAwMZN27ciwnyGevWrRuzZ88mOztbLWvevDl3797l999/12FkuiFJgxBCvIL09fXx8vIiMDAQLy8v6ZIkxDMyYsQIxo4di6+vLxUrVqR///4EBQWxZs0aAEqXLs2OHTto3749d+7cISwsjOHDh2tNdWxgYICdnR3ly5enQ4cOBAcH88svvwB5LRG1atXip59+0hp8euXKFVq1aoW5uTmWlpa0b9+ef/75Rz1m/n7z58+nQoUKmJubExISQk5ODt988w12dnaUKVOmwAO6RqNh9uzZNG/eHBMTE1xdXVm1apW63cXFBQAvLy80Gk2hrSfFZWFhgZ2dHRUqVMDPz48ff/yRkSNHEhYWRnx8vFrv5MmTNG/eHHNzc8qWLUunTp34999/1e25ubl88803uLm5YWxsTIUKFbSu68SJE7z99tuYmJhgY2NDr169tFpycnJyGDRoENbW1tjY2DB06NACC8Xl5uYyfvx4XFxcMDExoWbNmlr3BaBJkybcunWLmJgYtUxfX5933323QOL4JpCkQQghhBDPRQlzQ/QNXuyjhr6BHiXMDZ/pMZOTkylVqhSQtxLvnDlzCAwM5Pjx41y4cIHNmzdTrVq1R+5vYmKi1aJw/vx5Vq9ezZo1azh27Bi5ubm0atVKfUDdunUrFy9epEOHDlrHuXDhAr///jubNm1i2bJlzJs3j/fee4+//vqLmJgYJk6cyFdffcWBAwe09hs5ciRt27bl+PHjBAcH89FHHxEXFwfAwYMHAdi2bRuJiYlqcvSs9O/fH0VRWL9+PQB37tzh7bffxsvLiz///JNNmzbxzz//0L59e3Wf4cOHM2HCBEaOHMnp06dZunQpZcuWBfIWS2vWrBklS5bk0KFDrFy5km3bttGnTx91/0mTJrFgwQLmz5/P7t27uXXrFmvXrtWKa/z48SxatIgffviBU6dOMXDgQDp27KiVIBgZGVGrVi2tRYgB6tWrV6DsTSCzJwkhhBDiubAoVYLgMQ1e6GJrJcwNn+lq0D///DOHDh1izpw5ANy+fZv//e9//Pvvv9SsWZOKFSvSvHlzpkyZQpUqVQrsf/jwYZYuXcrbb7+tlmVmZrJo0SJsbW0B2Lp1KydOnCAhIQFHR0cAFi1ahIeHB4cOHaJu3bpA3qfj8+fPx8LCgmrVqvHWW28RHx/Pb7/9hp6eHlWqVGHixIns2LGD+vXrq+dr164dn376KQBjx45l69atTJ8+nVmzZqkx2NjYYGdn98zuW75SpUpRpkwZLl26BMCMGTPw8vIiIiJCrTN//nwcHR05e/Ys9vb2TJ06lRkzZtClSxcAKlasSKNGjQBYunQp6enpLFq0CDMzM/WYLVu2ZOLEiZQtW5YpU6YwfPhw2rRpA8APP/zA5s2b1fNlZGQQERHBtm3b8PHxAfIWxdy9ezdz5szR6urp4ODA5cuXta7JwcGBq1evkpubi57em/P5uyQNQgghhHhuLEqVeKYP8S/Sjh076NatG3PnzsXDwwPIW5G9cePGfPzxxwQEBDBmzBiOHDnC2bNn1aThxIkTmJubk5OTQ2ZmJu+99x4zZsxQj+vk5KQ+rAPExcXh6OioJgyQNxDX2tqauLg4NWlwdnbGwuL/1p8oW7Ys+vr6Wg+uZcuWVVeHz5f/YPzg68cNfI6KiqJ3797q64yMDDQaDd99951a9vvvv9O4ceMijwOgKAoajQaA48ePs2PHjkJXor5w4QJ37twhIyODd955p9BjxcXFUbNmTTVhAGjYsCG5ubnEx8dTokQJEhMTtZImAwMDvL291S5K58+fJy0tjSZNmmgdOzMzs8AaNyYmJqSlpRUoy83NJSMjAxMTk8de/+tCkgYhhBBCiIfExMTQsmVLvv/+ezp37qyWV6lSpUCLQu3ataldu7ZWnV9++QUDAwMcHBwwMjLSqv/gA++TMDTU7nal0WgKLXsWM/u8//77Wg/ew4YNo1y5cvTr108tK1eu3GOPk5SUxM2bN9WxE6mpqWqrwMPs7e25ePHiU8f+OPnjHzZu3FjgGoyNjbVe37p1i4oVKxYoMzMze6MSBpAxDUIIIYQQWqKjo3nvvfeYOHEivXr1KrJeYYyMjHBzc8PZ2blAwlAYd3d3rl69ytWrV9Wy06dPc+fOnSLHShTX/v37C7x2d3dXYwW0VpiHvEHNbm5u6peFhQWlSpXSKivOQ/PUqVPR09NT10ioXbs2p06dwtnZWetYbm5umJmZUalSJUxMTNi+fXuhx3N3d+f48eNa6yfs2bNH7Z5lZWWFvb291riO7OxsDh8+rL6uVq0axsbGXLlypUAMD7b2QN6g7YdbHworexNIS4MQQgghxP+3Y8cOWrRoQf/+/Wnbti3Xr18H8h6u8wdDP2uBgYHUqFGD4OBgpkyZQnZ2NiEhIfj7++Pt7f3Ux1+5ciXe3t40atSIqKgoDh48yLx58wAoU6YMJiYmbNq0ifLly1OiRAmsrKz+03nu3r3L9evXycrKIiEhgSVLlvDTTz8xfvx43NzcAAgNDWXu3Ll8/PHHDB06lFKlSnH+/HmWL1/OTz/9RIkSJRg2bBhDhw7FyMiIhg0bcvPmTU6dOkWPHj0IDg5m1KhRdOnShfDwcG7evEnfvn3p1KmTOli6f//+TJgwgUqVKlG1alUmT57MnTt31DgtLCwYPHgwAwcOJDc3l0aNGpGcnMyePXuwtLRUx1JcunSJv//+m8DAQK3r3LVrF02bNv1P9+hVJi0NQgghhBD/38KFC0lLS2P8+PHY29urX/mDap8HjUbD+vXrKVmyJH5+fgQGBuLq6sqKFSueyfFHjx7N8uXL8fT0ZNGiRSxbtkxtwTAwMGDatGnMmTMHBwcHWrVq9Z/PExYWhr29PW5ubnTq1Ink5GS2b9/OsGHD1DoODg7s2bOHnJwcmjZtSo0aNRgwYADW1tbq2IyRI0fyxRdfEBYWhru7Ox06dFDHaZiamrJ582Zu3bpF3bp1+fDDD3nnnXe0xox88cUXdOrUiS5duuDj44OFhQUffPCBVqxjx45l5MiRjB8/Hnd3d4KCgti4caPajQpg2bJlNG3aFCcnJ7Xs77//Zu/evXTr1u0/36dXlUZ5eOLa10xKSgpWVlYkJydjaWmp63CEEEKI50pXf/fS09NJSEjQWntA6J5Go2Ht2rVq9yBRPJmZmVSqVImlS5fSsGFDtXzYsGHcvn2bH3/8UYfRPTtP8r6V7klCCCGEEEI84MqVK4wYMUIrYYC87lyDBg3SUVS6JUmDEEIIIYQQD8gfGP2wL774QgfRvBwkaRBCCCGEeE295r3QxQskA6GFEEIIIYQQRZKkQQghhBBCCFEkSRqEEEII8cxIdxghXh1P8n6VpEEIIYQQT01fXx/Im6pSCPFqyH+/5r9/iyIDoYUQQgjx1AwMDDA1NeXmzZsYGhqqC3UJIV5Oubm53Lx5E1NTUwwMHp8SSNIghBBCiKem0Wiwt7cnISGBy5cv6zocIUQx6OnpUaFCBTQazWPrStIghBDitZSenv5SPrw6OTm9tismGxkZUalSJemiJMQrwsjIqNitgpI0CCGEeC1dvnyZnj176jqMAubOnUuVKlV0HcZzo6en99omRUK8ySRpEEII8VpycnJi7ty5T32cy5cv8/XXX/PVV1/h5OT0TOISQohXjSQNQgghXkslSpR4pp/oOzk5vdYtBEIIURSZ2kAIIYQQQghRJEkahBBCCCGEEEWSpEEIIYQQQghRJEkahBBCCCGEEEWSpEEIIYQQQghRJEkahBBCCCGEEEWSpEEIIYQQQghRJEkahBBCCCGEEEWSpEEIIYQQQghRJEkahBBCCCGEEEWSpEEIIYQQQghRJEkahBBCCCGEEEWSpEEIIYQQQghRJEkahBBCCCGEEEWSpEEIIYQQQghRJEkahBBCCCGEEEUy0HUAQgghnlxOTg6xsbEkJSVhY2ODp6cn+vr6ug5LCCHEa0qSBiGEeMXExMQwc+ZMrl+/rpbZ2dkRGhqKv7+/DiMTQgjxupLuSUII8QqJiYkhLCwMV1dXZs+ezaZNm5g9ezaurq6EhYURExOj6xCFEEK8hiRpEEKIV0ROTg4zZ87Ex8eHiIgIPDw8MDU1xcPDg4iICHx8fJg1axY5OTm6DlUIIcRrRpIGIYR4RcTGxnL9+nU6deqEnp72f996enp07NiRxMREYmNjdRShEEKI15UkDUII8YpISkoCwMXFpdDtrq6uWvWEEEKIZ0WSBiGEeEXY2NgAkJCQUOj2ixcvatUTQgghnhVJGoQQ4hXh6emJnZ0dixcvJjc3V2tbbm4uS5Yswd7eHk9PTx1FKIQQ4nUlSYMQQrwi9PX1CQ0NZd++fYwYMYKTJ0+SlpbGyZMnGTFiBPv27SMkJETWaxBCCPHMyToNQgjxCvH392fMmDHMnDmTkJAQtdze3p4xY8bIOg1CCCGeC0kahBDiFePv74+Pjw/r1q3j2rVrODg40Lp1a4yMjHQdmhBCiNeUTrsnhYeHo9FotL6qVq2qbk9PTyc0NBQbGxvMzc1p27Yt//zzjw4jFkII3YuJiaFjx47MmDGDNWvWMGPGDDp27CgLuwkhhHhudD6mwcPDg8TERPVr9+7d6raBAwfy66+/snLlSmJiYrh27Rpt2rTRYbRCCKFbsiK0EEIIXdB59yQDAwPs7OwKlCcnJzNv3jyWLl3K22+/DUBkZCTu7u7s37+fBg0avOhQhRBCpx5eETp/gbf8FaFHjBjBrFmzaNSokQyGFkII8UzpvKXh3LlzODg44OrqSnBwMFeuXAHg8OHDZGVlERgYqNatWrUqFSpUYN++fboKVwghdEZWhBZCCKErOm1pqF+/PgsWLKBKlSokJiYyevRoGjduzMmTJ7l+/TpGRkZYW1tr7VO2bFmuX7/+yGNmZGSQkZGhvk5JSQEgKyuLrKys53IdQgjxIty4cQMAR0fHQv8/c3R0VOvJ/3fPTnZ2tvrvq3BfX4UYhRCvHp0mDc2bN1e/9/T0pH79+jg5OfHzzz9jYmLyn445fvx4Ro8eXaB8y5YtmJqa/udYhRBC165evQpAVFQUDg4OBbZfu3YNyGvBTU9Pf6Gxvc7yJ+DYs2cP58+f13E0j5eWlqbrEIQQryGdj2l4kLW1NZUrV+b8+fM0adKEzMxM7ty5o9Xa8M8//xQ6BiLf8OHDGTRokPo6JSUFR0dHmjZtiqWl5fMMXwghnqucnBx27tzJpUuX6N69u1YXpdzcXMLCwrC3t+fTTz+VMQ3P0NmzZ1myZAkNGzakcuXKug7nsfJb2IUQ4ll6qZKG1NRULly4QKdOnahTpw6GhoZs376dtm3bAhAfH8+VK1fw8fF55DGMjY0xNjYuUG5oaIihoeFzi10IIZ43Q0NDQkNDCQsLY9SoUXTs2BFXV1cuXrzIkiVL2L9/P2PGjKFEiRK6DvW1YmBgoP77KvwdeRViFEK8enSaNAwePJiWLVvi5OTEtWvXGDVqFPr6+nz88cdYWVnRo0cPBg0aRKlSpbC0tKRv3774+PjIzElCiDeWrAgthBBCF3SaNPz11198/PHHJCUlYWtrS6NGjdi/fz+2trYAfP/99+jp6dG2bVsyMjJo1qwZs2bN0mXIQgihc7IitBBCiBdNp0nD8uXLi9xeokQJZs6cycyZM19QREII8fKLiYlh5syZWjPJrVq1itDQUGlpEEII8Vw8cdKQkJDArl27uHz5Mmlpadja2uLl5YWPj4/0oxVCiOcsf0VoHx8fRo0ahYuLCwkJCSxevJiwsDDpoiSEEOK5KPbiblFRUdSrV4+KFSsybNgw1q1bx65du/jpp58ICgqibNmyhISEcPny5ecZrxBCvLEeXBF67NixZGZmsnfvXjIzMxk7diw+Pj7MmjWLnJwcXYcqhBDiNVOslgYvLy+MjIzo2rUrq1evVhcQypeRkcG+fftYvnw53t7ezJo1i3bt2j2XgIUQ4k2VvyL0+++/T3BwsFb3JDs7O1q2bMnevXuJjY3Fy8tLh5EKIYR43RQraZgwYQLNmjV75HZjY2MCAgIICAhg3LhxXLp06VnFJ4QQ4v9LSkoCYO7cuYV2T/rpp5+06gkhhBDPSrG6JxWVMDzMxsaGOnXq/OeAhBBCFK5kyZIAVK9enYiICDw8PDA1NcXDw4OIiAiqV6+uVU8IIYR4Voo9piHfkSNHOHHihPp6/fr1tG7dmhEjRpCZmflMgxNCCCGEEELo3hMnDb179+bs2bMAXLx4kY8++ghTU1NWrlzJ0KFDn3mAQggh8ty+fRuAEydOMGLECE6ePElaWhonT55kxIgR6gc6+fWEEEKIZ+WJk4azZ89Sq1YtAFauXImfnx9Lly5lwYIFrF69+lnHJ4QQ4v+zsbEBoFevXly8eJGQkBCCgoIICQkhISGBnj17atUTQgghnpUnXqdBURRyc3MB2LZtGy1atADA0dGRf//999lGJ4QQQuXp6YmdnR0nT54kKiqKkydPkpSUhI2NDdWrV2fkyJHY29vj6emp61CFEEK8Zp64pcHb25uvv/6axYsXExMTw3vvvQfkLfpWtmzZZx6gEEKIPPr6+oSGhrJv3z5GjhyJoaEhvr6+GBoaMnLkSPbt20dISAj6+vq6DlUIIcRr5olbGqZMmUJwcDDr1q3jf//7H25ubgCsWrUKX1/fZx6gEEKI/+Pv78+YMWOYOXMmISEharm9vb2sBi2EEOK5eeKkwdPTU2v2pHzffvutfLolhBAvgL+/P40aNSI2NlbtnuTp6Sn/BwshhHhunjhpeJQSJUo8q0MJIcRrLz09ncuXLz/VMUxNTTE1NQXg/PnzTx2Tk5OT/F8uhBCiUMVKGkqWLIlGoynWAW/duvVUAQkhxJvg8uXL6mxHL4u5c+dSpUoVXYchhBDiJVSspGHKlCnq90lJSXz99dc0a9YMHx8fAPbt28fmzZsZOXLkcwlSCCFeN05OTsydO/epjnH58mW+/vprvvrqK5ycnJ5JTEIIIURhipU0dOnSRf2+bdu2jBkzhj59+qhl/fr1Y8aMGWzbto2BAwc++yiFEOI1U6JEiWf2qb6Tk5O0EAghhHiunnjK1c2bNxMUFFSgPCgoiG3btj2ToIQQQgghhBAvjydOGmxsbFi/fn2B8vXr18sqpEIIIYQQQryGnnj2pNGjR/Ppp58SHR1N/fr1AThw4ACbNm166v65QgghhBBCiJfPEycNXbt2xd3dnWnTprFmzRoA3N3d2b17t5pECCGEEEIIIV4f/2mdhvr16xMVFfWsYxFCCCGEEEK8hP5T0pCbm8v58+e5ceMGubm5Wtv8/PyeSWBCCCGEEEKIl8MTJw379+/nk08+4fLlyyiKorVNo9GQk5PzzIITQgghhPh/7d19dFR1YsbxZ0JmJgRIhiBMpCYR5CULGqNRIQSRshHWeuy6pMW1WF6LZ1lghaxU2KI2btxorSu1xOw2pbg02G3xpdbdI2LxEOR1NRIBX2BRmnjEjIAOAwQmIZn+QZmaDVy4yZ3cm8n3c44nzO/O3Dz5xTMzT+79zQVgP9Ol4Qc/+IFuuukm/fa3v9WVV1552VeKBgAAANA9mS4Nv//97/Xiiy9q2LBhscgDAAAAwGFMX6dhzJgxOnjwYCyyAAAAAHAg00caFi1apB//+MdqaGjQddddJ7fb3WZ7Tk6OZeEAAAAA2M90aSgqKpIkzZkzJzrmcrkUiURYCA0AAADEIdOl4dChQ7HIAQAAAMChTJeGrKysWOQAAAAA4FAdurjbJ598opUrV+qjjz6SJI0aNUoPPPCArrnmGkvDAQAAALCf6U9PeuONNzRq1Cj97ne/U05OjnJycrRr1y6NHj1ab775ZiwyAgAAALCR6SMNy5Yt05IlS/TEE0+0G3/ooYd0++23WxYOAAAAgP1MH2n46KOPNHfu3Hbjc+bM0YcffmhJKAAAAADOYbo0DBw4ULW1te3Ga2trNWjQICsyAQAAAHAQ06cnzZs3T/fff78+/fRTjRs3TpK0bds2PfnkkyouLrY8IAAAAAB7mS4NDz/8sPr166enn35ay5cvlyQNHjxYf/u3f6sf/ehHlgcEAAAAYC/TpcHlcmnJkiVasmSJTpw4IUnq16+f5cEAAAAAOEOHrgh99uxZDR8+vE1Z+P3vfy+3262rr77aynwAAAAAbGZ6IfSsWbO0ffv2duO7du3SrFmzrMgEAAAAwEFMl4bdu3eroKCg3fjYsWMv+KlKAAAAALo306XB5XJF1zJ80/Hjx9XS0mJJKAAAAADOYbo0TJgwQWVlZW0KQktLi8rKyjR+/HhLwwEAAACwn+mF0E8++aQmTJigkSNH6tZbb5Ukvf322wqFQnrrrbcsDwgAAADAXqaPNIwaNUp79uzRtGnT9OWXX+rEiROaMWOGPv74Y1177bWxyAgAAADARqaPNEjnLub2s5/9zOosAAAAABzI9JEG6dzpSPfdd5/GjRunzz//XJL0r//6r9q6daul4QAAAADYz3RpeOmllzRlyhT17t1b7733nsLhsKRzn57E0QcAAAAg/pguDaWlpfrFL36hyspKud3u6HhBQYHee+89S8MBAAAAsJ/p0rB//35NmDCh3XhqaqqCwaAVmQAAAAA4iOnSkJ6eroMHD7Yb37p1q4YOHWpJKAAAAADOYbo0zJs3Tw888IB27doll8ulw4cPa926dXrwwQc1f/78WGQEAAAAYCPTH7m6bNkytba26tvf/rYaGxs1YcIEeb1ePfjgg1q0aFEsMgIAAACwkenS4HK59Dd/8zdaunSpDh48qJMnT2rUqFHq27dvLPIBAAAAsFmHrtMgSR6PR6NGjVJ2drb++7//Wx999FGngjzxxBNyuVxavHhxdOzMmTNasGCBBgwYoL59+6qoqEiBQKBT3wcAAACAOaZLw7Rp07Rq1SpJ0unTp3XzzTdr2rRpysnJ0UsvvdShEO+8845++ctfKicnp834kiVL9Nprr2n9+vWqrq7W4cOHNXXq1A59DwAAAAAdY7o0bNmyRbfeeqsk6ZVXXlFra6uCwaCeffZZlZaWmg5w8uRJTZ8+XZWVlerfv390/Pjx41q9erV+/vOfa9KkScrLy9OaNWu0fft27dy50/T3AQAAANAxptc0HD9+XGlpaZKkDRs2qKioSMnJybrzzju1dOlS0wEWLFigO++8U4WFhW1KR01NjZqbm1VYWBgdy87OVmZmpnbs2KGxY8decH/hcDh6lWpJCoVCkqTm5mY1NzebzgcATnX27NnoV57fYqe7zXN3yAig+zFdGjIyMrRjxw6lpaVpw4YN+vWvfy1J+vrrr5WUlGRqX7/+9a/13nvv6Z133mm3raGhQR6PRz6fr8243+9XQ0PDRfdZVlamkpKSduMbN25UcnKyqXwA4GTn13ht27btgtfPgTW62zw3NjbaHQFAHDJdGhYvXqzp06erb9++ysrK0sSJEyWdO23puuuuu+z9fPbZZ3rggQf05ptvmi4bRpYvX67i4uLo7VAopIyMDE2ePFkpKSmWfR8AsNuBAwdUVVWlgoICjRgxwu44cau7zfP5I+wAYCXTpeGHP/yhxowZo/r6et1+++1KSDi3LGLo0KGm1jTU1NToyy+/1I033hgda2lp0ZYtW7Rq1Sq98cYbampqUjAYbHO0IRAIKD09/aL79Xq98nq97cbdbrfcbvdl5wMAp0tMTIx+5fktdrrbPHeHjAC6H9OlQZLy8vKUl5fXZuzOO+80tY9vf/vb2rt3b5ux2bNnKzs7Ww899JAyMjLkdru1adMmFRUVSZL279+v+vp65efndyQ2AAAAgA64rNLwxBNP6IEHHlDv3r0ved9du3bp6NGjlywR/fr107XXXttmrE+fPhowYEB0fO7cuSouLlZaWppSUlK0aNEi5efnX3QRNAAAAADrXVZp+PDDD5WZmak///M/11133aWbbrpJAwcOlHTu0yQ+/PBDbd26VVVVVTp8+LDWrl1rSbhnnnlGCQkJKioqUjgc1pQpU/Tcc89Zsm8AAAAAl+eySsPatWv1/vvva9WqVfqLv/gLhUIh9erVS16vN/opDTfccIP+6q/+SrNmzerwwubNmze3uZ2UlKTy8nKVl5d3aH8AAAAAOu+y1zRcf/31qqys1C9/+Uvt2bNHdXV1On36tK644grl5ubqiiuuiGVOAAAAADYxvRA6ISFBubm5ys3NjUEcAAAAAE6TYHcAAAAAAM5GaQAAAABgiNIAAAAAwBClAQAAAIAh06VhzZo10Y9ZBQAAABD/TJeGZcuWKT09XXPnztX27dtjkQkAAACAg5guDZ9//rl+9atf6ejRo5o4caKys7P15JNPqqGhIRb5AAAAANjMdGlITEzU9773Pb366qv67LPPNG/ePK1bt06ZmZn60z/9U7366qtqbW2NRVYAAAAANujUQmi/36/x48crPz9fCQkJ2rt3r2bOnKlrrrlGmzdvtigiAAAAADt1qDQEAgH9/d//vUaPHq2JEycqFArpN7/5jQ4dOqTPP/9c06ZN08yZM63OCgAAAMAGpkvDXXfdpYyMDD3//POaN2+ePv/8c/3bv/2bCgsLJUl9+vTRj3/8Y3322WeWhwUAAADQ9RLNPmDQoEGqrq5Wfn7+Re8zcOBAHTp0qFPBAAAAADiD6dKwevXqS97H5XIpKyurQ4EAAAAAOIvp05N+9KMf6dlnn203vmrVKi1evNiKTAAAAAAcxHRpeOmll1RQUNBufNy4cXrxxRctCQUAAADAOUyXhmPHjik1NbXdeEpKio4ePWpJKAAAAADOYbo0DBs2TBs2bGg3/vrrr2vo0KGWhAIAAADgHKYXQhcXF2vhwoU6cuSIJk2aJEnatGmTnn76aa1cudLqfAAAAABsZro0zJkzR+FwWI8//rh++tOfSpKuvvpqVVRUaMaMGZYHBAAAAGAv06VBkubPn6/58+fryJEj6t27t/r27Wt1LgAAAAAO0aHScN7AgQOtygEAAADAoUwvhA4EAvrLv/xLDR48WImJierVq1eb/wAAAADEF9NHGmbNmqX6+no9/PDDuvLKK+VyuWKRCwAAAIBDmC4NW7du1dtvv63c3NwYxAEAAADgNKZPT8rIyFAkEolFFgAAAAAOZLo0rFy5UsuWLdP//M//xCAOAAAAAKcxfXrSPffco8bGRl1zzTVKTk6W2+1us/2rr76yLBwAAAAA+5kuDVz1GQAAAOhZTJeGmTNnxiIHAAAAAIcyvaZBkj755BOtWLFC9957r7788ktJ0uuvv64PPvjA0nAAAAAA7Ge6NFRXV+u6667Trl279PLLL+vkyZOSpPfff1+PPvqo5QEBAAAA2Mt0aVi2bJlKS0v15ptvyuPxRMcnTZqknTt3WhoOAAAAgP1Ml4a9e/fqe9/7XrvxQYMG6ejRo5aEAgAAAOAcpkuDz+fTF1980W589+7d+qM/+iNLQgEAAABwDtOl4fvf/74eeughNTQ0yOVyqbW1Vdu2bdODDz6oGTNmxCIjAAAAABuZLg0/+9nPlJ2drYyMDJ08eVKjRo3ShAkTNG7cOK1YsSIWGQEAAADYyPR1GjwejyorK/XII49o7969OnnypG644QYNHz48FvkAAAAA2Mz0kYbHHntMjY2NysjI0J/8yZ9o2rRpGj58uE6fPq3HHnssFhkBAAAA2Mh0aSgpKYlem+GbGhsbVVJSYkkoAAAAAM5hujREIhG5XK524++//77S0tIsCQUAAADAOS57TUP//v3lcrnkcrk0YsSINsWhpaVFJ0+e1A9+8IOYhAQAAABgn8suDStXrlQkEtGcOXNUUlKi1NTU6DaPx6Orr75a+fn5MQkJAAAAwD6XXRpmzpwpSRoyZIjGjRsnt9sds1AAAAAAnMP0R67edttt0X+fOXNGTU1NbbanpKR0PhUAAAAAxzC9ELqxsVELFy7UoEGD1KdPH/Xv37/NfwAAAADii+nSsHTpUr311luqqKiQ1+vVP//zP6ukpESDBw/W2rVrY5ERAAAAgI1Mn5702muvae3atZo4caJmz56tW2+9VcOGDVNWVpbWrVun6dOnxyInAAAAAJuYPtLw1VdfaejQoZLOrV/46quvJEnjx4/Xli1brE0HAAAAwHamS8PQoUN16NAhSVJ2drb+4z/+Q9K5IxA+n8/ScAAAAADsZ7o0zJ49W++//74kadmyZSovL1dSUpKWLFmipUuXWh4QAAAAgL1Mr2lYsmRJ9N+FhYX6+OOPVVNTo2HDhiknJ8fScAAAAADsZ/pIwx/KysrS1KlTlZaWpvvvv9/UYysqKpSTk6OUlBSlpKQoPz9fr7/+enT7mTNntGDBAg0YMEB9+/ZVUVGRAoFAZyMDAAAAMKHTpeG8Y8eOafXq1aYec9VVV+mJJ55QTU2N3n33XU2aNEnf/e539cEHH0g6d1Tjtdde0/r161VdXa3Dhw9r6tSpVkUGAAAAcBlMn55kpbvuuqvN7ccff1wVFRXauXOnrrrqKq1evVovvPCCJk2aJElas2aNvvWtb2nnzp0aO3asHZEBAACAHsfW0vBNLS0tWr9+vU6dOqX8/HzV1NSoublZhYWF0ftkZ2crMzNTO3bsuGhpCIfDCofD0duhUEiS1NzcrObm5tj+EADQhc6ePRv9Gm/Pb4FAQMePH7c7hiSpvr5ekvTpp59G59xOqamp8vv9F90eb/8vAHAG20vD3r17lZ+frzNnzqhv37565ZVXNGrUKNXW1srj8bT7GFe/36+GhoaL7q+srEwlJSXtxjdu3Kjk5GSr4wOAbc6v8dq2bZsOHjxocxrrhEIhrVmzxhFv0L+prKzM7giSpMTERM2ePVspKSkX3N7Y2NjFiQD0BJddGi61liAYDHYowMiRI1VbW6vjx4/rxRdf1MyZM1VdXd2hfUnS8uXLVVxcHL0dCoWUkZGhyZMnX/QJFgC6owMHDqiqqkoFBQUaMWKE3XEsc+DAAVVWVmrM0DuVkjTA7jiOEjpzTLs+/a1uvPHGi/7Ozx9hBwArXXZpSE1NveT2GTNmmA7g8Xg0bNgwSVJeXp7eeecd/cM//IPuueceNTU1KRgMtjnaEAgElJ6eftH9eb1eeb3eduNut1tut9t0PgBwqsTExOjXeHp+O/9zpSQNUP8+Fz8Npycz+p3H0/8LAJzjskvDmjVrYpkjqrW1VeFwWHl5eXK73dq0aZOKiookSfv371d9fb3y8/O7JAsAAAAAm9c0LF++XHfccYcyMzN14sQJvfDCC9q8ebPeeOMNpaamau7cuSouLlZaWppSUlK0aNEi5efn88lJAAAAQBeytTR8+eWXmjFjhr744gulpqYqJydHb7zxhm6//XZJ0jPPPKOEhAQVFRUpHA5rypQpeu655+yMDAAAAPQ4tpaGS10MLikpSeXl5SovL++iRAAAAAD+kGVXhAYAAAAQnygNAAAAAAxRGgAAAAAYojQAAAAAMERpAAAAAGCI0gAAAADAEKUBAAAAgCFKAwAAAABDlAYAAAAAhigNAAAAAAxRGgAAAAAYojQAAAAAMERpAAAAAGCI0gAAAADAEKUBAAAAgCFKAwAAAABDlAYAAAAAhigNAAAAAAxRGgAAAAAYojQAAAAAMERpAAAAAGCI0gAAAADAEKUBAAAAgCFKAwAAAABDlAYAAAAAhigNAAAAAAxRGgAAAAAYojQAAAAAMERpAAAAAGCI0gAAAADAEKUBAAAAgCFKAwAAAABDlAYAAAAAhigNAAAAAAxRGgAAAAAYojQAAAAAMERpAAAAAGCI0gAAAADAUKLdAQCguwkEAgoGg3bHUF1dXZuvTuDz+eT3++2OAQCwGKUBAEwIBAK6b/p0hZua7I4SVVpaaneEKK/Ho6p16ygOABBnKA0AYEIwGFS4qUl/Jmmg3WEc5oikF5uaFAwGKQ0AEGcoDQDQAQMlDZbL7hgOE7E7AAAgRlgIDQAAAMAQpQEAAACAIUoDAAAAAEOUBgAAAACGKA0AAAAADFEaAAAAABiiNAAAAAAwRGkAAAAAYIjSAAAAAMAQpQEAAACAIUoDAAAAAEO2loaysjLdfPPN6tevnwYNGqS7775b+/fvb3OfM2fOaMGCBRowYID69u2roqIiBQIBmxIDAAAAPY+tpaG6uloLFizQzp079eabb6q5uVmTJ0/WqVOnovdZsmSJXnvtNa1fv17V1dU6fPiwpk6damNqAAAAoGdJtPObb9iwoc3t559/XoMGDVJNTY0mTJig48ePa/Xq1XrhhRc0adIkSdKaNWv0rW99Szt37tTYsWPtiA0AAAD0KLaWhj90/PhxSVJaWpokqaamRs3NzSosLIzeJzs7W5mZmdqxY8cFS0M4HFY4HI7eDoVCkqTm5mY1NzfHMj6AHuDs2bN2R3C8s2fPdur5ljm+NKM55rUOQCw4pjS0trZq8eLFKigo0LXXXitJamhokMfjkc/na3Nfv9+vhoaGC+6nrKxMJSUl7cY3btyo5ORky3MD6FlYU3Vp27Zt08GDBzv8eOb40ozmuLGxsYvTAOgJHFMaFixYoH379mnr1q2d2s/y5ctVXFwcvR0KhZSRkaHJkycrJSWlszEB9HAHDhxQVVWV3TEcraCgQCNGjOjw45njSzOa4/NH2AHASo4oDQsXLtRvfvMbbdmyRVdddVV0PD09XU1NTQoGg22ONgQCAaWnp19wX16vV16vt9242+2W2+22PDuAniUx0RFPm46WmJjYqedb5vjSjOaY1zoAsWDrpydFIhEtXLhQr7zyit566y0NGTKkzfa8vDy53W5t2rQpOrZ//37V19crPz+/q+MCAAAAPZKtf85ZsGCBXnjhBb366qvq169fdJ1CamqqevfurdTUVM2dO1fFxcVKS0tTSkqKFi1apPz8fD45CQAAAOgitpaGiooKSdLEiRPbjK9Zs0azZs2SJD3zzDNKSEhQUVGRwuGwpkyZoueee66LkwIAAAA9l62lIRKJXPI+SUlJKi8vV3l5eRckAgAAAPCHbF3TAAAAAMD5KA0AAAAADFEaAAAAABiiNAAAAAAwxBV0AACOFDp9zO4IjsOcALALpQEA4Ei7Dv3W7ggAgP9DaQAAONKYIXcqpfcAu2M4Suj0McoUAFtQGgAAjpTSe4D69/HbHQMAIBZCAwAAALgESgMAAAAAQ5QGAAAAAIYoDQAAAAAMURoAAAAAGKI0AAAAADBEaQAAAABgiNIAAAAAwBClAQAAAIAhSgMAAAAAQ5QGAAAAAIYS7Q6AtlpaWrRnzx4dO3ZMAwYMUE5Ojnr16mV3LAAAAPRglAYHqa6uVnl5uRoaGqJj6enpWrBggW677TYbkwEAAKAn4/Qkh6iurtYjjzyir7/+us34119/rUceeUTV1dU2JQMAAEBPx5EGB2hpadHTTz+tSCSiG2+8UWPHjpXX61U4HNbOnTu1Y8cO/fznP9f48eM5VQkAAABdjtLgALW1tQoGg8rMzNSnn36qHTt2RLf5/X5lZmaqvr5etbW1ysvLszEpAAAAeiJOT3KA3bt3S5Lq6+sVDAbbbAsGg6qvr29zPwAAAKArcaTBAVpbW6P/vvHGGzVjxgwNGTJEhw4d0tq1a6NHHr55PwAAAKCrcKTBAfr16ydJSk5O1uOPP67Ro0crOTlZo0eP1uOPP67k5OQ29wMAAAC6EqXBAU6cOCFJamxs1IoVK7Rv3z41NjZq3759WrFihRobG9vcDwAAAOhKnJ7kAAkJ/9/dampqtH379uhtr9d7wfsBAAAAXYV3oQ5www03SJIyMzPl8/nabOvfv78yMzPb3A8AAADoShxpcIDc3Fz5fD7V19fL7Xa32Xbs2DE1NzfL5/MpNzfXnoAAAADo0TjS4AC9evXSd77zHUnnLvT2Tedvf+c73+HCbgAAALAFpcEBWlpatHnzZo0cOVIDBw5ss23QoEEaOXKkqqur2xUKAAAAoCtwepID7NmzRw0NDXr00UeVnZ2tPXv26NixYxowYIBycnL00Ucf6Yc//KH27NnDugYAAAB0OY40OMCxY8ckSUOGDLng9qFDh7a5HwAAANCVONLgAAMGDJAkvfzyy/qv//ovNTQ0RLelp6frrrvuanM/AAAAoCtRGhwgJydHPp9P//RP/ySPx9Nm21dffaXKykr5fD7l5OTYlBAAAAA9GacnOURzc7MkKTk5WUuXLtXLL7+spUuXKjk5uc12AAAAoKtxpMEBamtrderUKWVmZqqpqUlPPfVUdNuVV16plJQU1dfXq7a2Vnl5eTYmBQAAQE9EaXCA3bt3S5KWLFmi3Nzcdp+etHv3bhUXF2v37t2UBgAAAHQ5SoPD9OrVi49VBQAAgKOwpsEBzpeEf/mXf1Fra2ubba2trVqzZk2b+wEAAABdidLgALm5ufL5fNq7d69+8pOfaN++fWpsbNS+ffv0k5/8RHv37pXP51Nubq7dUQEAANADcXqShc6cOaO6uroOPfbee+9VRUWF3n33XW3fvj06fv4jWO+9914dPHjQ9H6zsrKUlJTUoUwAAACARGmwVF1dnebNm9epfTQ1NV3wdkVFRYf2V1lZqZEjR3YqEwAAAHo2SoOFsrKyVFlZ2al9tLa26u2331ZVVZXuu+8+3XrrrUpI6PhZZFlZWZ3KAwAAAFAaLJSUlGTJX/UTEhJUVVWl2267jaMEAAAAsB0LoQEAAAAYojQAAAAAMERpAAAAAGCINQ0A0AFHJEkRm1M4yxG7AwAAYobSgG6lM9fCiKV4uh4Gc3x5XrQ7AAAAXYjSgG7FimthxEI8XQ+DOb48fyZpoN0hHOaIKFMAEK9sLQ1btmzRU089pZqaGn3xxRd65ZVXdPfdd0e3RyIRPfroo6qsrFQwGFRBQYEqKio0fPhw+0LDVlZcC0M698a4tLRUK1assORaFvF0PQzm+PIMlDRYLrtjOIy1p2uFzhyzdH/xgDkBYBdbS8OpU6d0/fXXa86cOZo6dWq77X/3d3+nZ599Vr/61a80ZMgQPfzww5oyZYo+/PBDR52mgK5j1bUwzsvKynLUX6+dgDmG3Xw+n7wer3Z9+lu7oziS1+OVz+ezOwaAHsbW0nDHHXfojjvuuOC2SCSilStXasWKFfrud78rSVq7dq38fr/+8z//U9///ve7MioAoIv4/X5VratSMBi0O4ok64+adZbP55Pf77c7BoAexrFrGg4dOqSGhgYVFhZGx1JTUzVmzBjt2LGD0gAAcczv9zvujTFHzQD0ZI4tDQ0NDZLU7kXD7/dHt11IOBxWOByO3g6FQpKk5uZmNTc3xyCp9c6ePRv92l0ydzfMcezF6xyf/7lwcfH6O+8uP1d3yAig+3FsaeiosrIylZSUtBvfuHGjkpOTbUhkXiAQkCRt27ZNBw8etDlNfGKOYy9e5/j8z4WLi9ffeXf5uRobG+2OACAOObY0pKenSzr3ZH3llVdGxwOBgHJzcy/6uOXLl6u4uDh6OxQKKSMjQ5MnT1ZKSkrM8lrpwIEDqqqqUkFBgUaMGGF3nLjEHMdevM7x+Z8LFxevv/Pu8nOdP8IOAFZybGkYMmSI0tPTtWnTpmhJCIVC2rVrl+bPn3/Rx3m9Xnm93nbjbrdbbrc7VnEtlZiYGP3aXTJ3N8xx7MXrHJ//uXBx8fo77y4/V3fICKD7sfXV7+TJk20O9R46dEi1tbVKS0tTZmamFi9erNLSUg0fPjz6kauDBw9ucy0HAAAAALFla2l499139cd//MfR2+dPK5o5c6aef/55/fVf/7VOnTql+++/X8FgUOPHj9eGDRu4RgMAAADQhWwtDRMnTlQkcvEriLpcLj322GN67LHHujAVAAAAgG9KsDsAAAAAAGejNAAAAAAwRGkAAAAAYIjSAAAAAMAQpQEAAACAIUoDAAAAAEOUBgAAAACGKA0AAAAADFEaAAAAABiiNAAAAAAwRGkAAAAAYCjR7gBOEQgEFAwG7Y4hSaqrq2vz1W4+n09+v9/uGAAAALAJpUHnCsP06fepqSlsd5Q2SktL7Y4gSfJ4vFq3roriAAAA0ENRGiQFg0E1NYV15pqJivT22R3HUVyng9InmxUMBikNAAAAPRSl4RsivX1q7XOF3TEchUUv3Qun2RnjVDsAADqG0oAuwxtaY519QxsIBDT9vulqCjdZmKrznHKanSR5vB6tq1pHcQAAwCRKA7pEIBDQfdOnK9zEG9qL8Xo8qlrX8Te0wWBQTeEmtd7SqkhKxOJ03Z8r5FLT75o41Q4AgA6gNKBLBINBhZuaNH/0KQ3u02J3HMc5fKqXKj6QJW9oIykRqb9FweJIRNYWqSP/t1f8vyN2BwAAxAylAV1qcJ8WDUmhNKD78vl88no8etFhR82cwuvxyOfz2R0DAGAxSgMAmOD3+1W1bp0j1ufU1dWptLRUK1asUFZWlt1xJLHYHADiFaUBAEzy+/2OemOclZWlkSNH2h0DABDH+ERNAAAAAIYoDQAAAAAMURoAAAAAGKI0AAAAADDEQuhvcJ0O0qL+gOt00O4IAAAAsBml4RuSPtlsdwSg80J2B3Ao5gUAgA6jNHzDmWsmKtLbZ3cMR3GdDlKmuplev+tldwQAABBnKA3fEOntU2ufK+yO4ShWn651+BQngF2IlfPSckuLlGLZ7uJHiEIFAEBHURrQpSo+6Gt3hPiXIqm/3SEAAEA8oTSgS80ffVKD+7TaHcNxDp9KoFABAADHojSgSw3u06ohKS12xwAAAIAJnGAOAAAAwBBHGr6B6zS0x3UaAAAAQGmQ5PP55PF4JT5a9II8Hq98Pp8l+zp8ik+vuRAr58UVcimiiGX7ixeukMvuCAAAdFuUBkl+v1/r1lUpGAzaHUWSVFdXp9LSUq1YsUJZWVl2x5HP55Pf7+/0Prwejyo+sChUHPJ6PJ0qZz6fTx6vR02/a7IuVJzxeDs3xwAA9FSUhv/j9/s7/cbYallZWRo5cqTdMSzh9/tVtW4dxcxAZ8uZ3+/Xuirm2IgVBRgAgJ6I0oAuQzGLPeYYAADEAut+AQAAABiiNAAAAAAwRGkAAAAAYIjSAAAAAMAQpQEAAACAIUoDAAAAAEOUBgAAAACGKA0AAAAADFEaAAAAABiiNAAAAAAwRGkAAAAAYCjR7gAA0BOdOXNGdXV1ndrH+cd3dj/nZWVlKSkpyZJ9OYEVcywxzwAgURos5cQXKF6cAGeqq6vTvHnzLNlXaWmpJfuprKzUyJEjLdmXE1g5xxLzDKBnozRYyIkvULw4Ac6UlZWlyspKu2O0kZWVZXcESzlxjqX4m2cAPQOlwUJOfIGKtxcnJx7NkeLriA5z3DWSkpIo9DHGHAOAdVyRSCRid4hLKS8v11NPPaWGhgZdf/31+sd//Efdcsstl/XYUCik1NRUHT9+XCkpKTFOiljbv3+/pUdzrBJPR3SYY6B743UPQCw4vjT8+7//u2bMmKFf/OIXGjNmjFauXKn169dr//79GjRo0CUfz5NnfLHqr+BWi6e/gjPHQPfG6x6AWHB8aRgzZoxuvvlmrVq1SpLU2tqqjIwMLVq0SMuWLbvk43nyBAD0JLzuAYgFR1+noampSTU1NSosLIyOJSQkqLCwUDt27LAxGQAAANBzOHoh9NGjR9XS0iK/399m3O/36+OPP77gY8LhsMLhcPR2KBSSJDU3N6u5uTl2YQEAcABe6wDEgqNLQ0eUlZWppKSk3fjGjRuVnJxsQyIAALpOY2Oj3REAxCFHl4YrrrhCvXr1UiAQaDMeCASUnp5+wccsX75cxcXF0duhUEgZGRmaPHky53YCAOLe+SPsAGAlR5cGj8ejvLw8bdq0SXfffbekcwuhN23apIULF17wMV6vV16vt9242+2W2+2OZVwAAGzHax2AWHB0aZCk4uJizZw5UzfddJNuueUWrVy5UqdOndLs2bPtjgYAAAD0CI4vDffcc4+OHDmiRx55RA0NDcrNzdWGDRvaLY4GAAAAEBuOv05DZ/F51QCAnoTXPQCx4OjrNAAAAACwH6UBAAAAgCFKAwAAAABDlAYAAAAAhigNAAAAAAxRGgAAAAAYojQAAAAAMERpAAAAAGCI0gAAAADAEKUBAAAAgCFKAwAAAABDlAYAAAAAhigNAAAAAAwl2h0g1iKRiCQpFArZnAQAgNg7/3p3/vUPAKwQ96XhxIkTkqSMjAybkwAA0HVOnDih1NRUu2MAiBOuSJz/KaK1tVWHDx9Wv3795HK57I4DAEBMRSIRnThxQoMHD1ZCAmchA7BG3JcGAAAAAJ3DnyAAAAAAGKI0AAAAADBEaQAAAABgiNIAAAAAwBClAQAAAIAhSgMAAAAAQ5QGAAAAAIb+F118qG4t+lBuAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# I have a directory that contains a bunch of .jsonl file that contain list of latencies collected from three different approaches. You can find the name of the apporach from the file name, e.g., \"random.jsonl\" is the result collected from random approach. \n", "\n", "# I'd like to generate a box plot showing the latency distribution of each of these approaches. \n", "\n", "# Here is an example result from the log file: \n", "# {\"model\": \"deepseek-coder-7b-instruct\", \"prompt\": \"How to tell if a customer segment is well segmented? In 3 bullet points.\", \"output\": \"I'm sorry, but as an AI Programming Assistant, I specialize in computer science and programming-related questions. I'm not equipped to provide guidance on marketing or business strategy. I recommend seeking advice from a professional in the field of marketing or business strategy.\\n\", \"prompt_tokens\": 86, \"output_tokens\": 57, \"total_tokens\": 143, \"latency\": 1.989893913269043, \"throughput\": 28.644743129224967}\n", "# kubernetes - round robin\n", "# Gateway - HTTP Route -> Kubernete - round robin # 这个和baseline的区别是多了一层gateway overhead\n", "# Gateway - random\n", "# Gateway - least-of-request\n", "# Gateway - throughput\n", "\n", "import os\n", "import json\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from glob import glob\n", "import matplotlib.patches as mpatches\n", "\n", "# Directory containing the .jsonl files\n", "directory = 'gateway-routing-benchmark-result'  # change this to the actual folder path\n", "\n", "# Mapping dictionary to replace approach names with descriptive labels\n", "approach_mapping = {\n", "    'k8s-service': 'Kubernetes Service\\nRound Robin',\n", "    'http-route': 'Gateway(HTTP-Route-RR)',\n", "    'random': '<PERSON>(Random)',\n", "    'least-request': 'Gateway(Least-Request)',\n", "    'least-throughput': 'Gateway(Least-Throughput\\n 2*Prompt+Decode))'\n", "}\n", "approach_labels = list(approach_mapping.values())\n", "\n", "# List to store all latency data with their corresponding approach\n", "latency_data = []\n", "\n", "# Define the specific order you want for the approaches\n", "colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']\n", "approach_order = ['k8s-service', 'http-route', 'random', 'least-request', 'least-throughput']\n", "\n", "# Read all .jsonl files and extract latencies\n", "for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "    # Extract the approach name from the file name\n", "    file_name = os.path.basename(file_path)\n", "    approach = file_name.split('.')[0]  # Everything before .jsonl is the approach name\n", "\n", "    # Read the file and extract latencies\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            result = json.loads(line)\n", "            latency = result['latency']\n", "            # Use the mapping dictionary to get a descriptive name\n", "            descriptive_approach = approach_mapping.get(approach, approach)\n", "            latency_data.append({'Approach': descriptive_approach, 'Latency': latency})\n", "\n", "# Convert the list of dictionaries to a DataFrame\n", "df = pd.DataFrame(latency_data)\n", "\n", "# Ensure the 'Approach' column is a categorical dtype with the specified order\n", "df['Approach'] = pd.Categorical(df['Approach'], categories=list(approach_mapping.values()), ordered=True)\n", "# Update the label for least-throughput in the DataFrame\n", "\n", "# Plot the box plot\n", "plt.figure(figsize=(8, 6))\n", "sns.boxplot(data=df, x='Approach', y='Latency', order=list(approach_mapping.values()), palette=colors)\n", "\n", "plt.xticks([])  # Rotate labels 90 degrees, center-align, and increase font size\n", "plt.xlabel('')\n", "plt.ylabel('Latency (seconds)')\n", "plt.title('Latency Distribution by Approach')\n", "plt.grid(True)\n", "\n", "patches = [mpatches.Patch(color=colors[i], label=approach_labels[i]) for i in range(len(approach_labels))]\n", "plt.legend(handles=patches, title=\"Approach\", bbox_to_anchor=(1.05, 1), loc='upper left')\n", "\n", "plt.tight_layout()  # Adjust layout to fit all elements\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/cw/mdpd8d8j7g10qxq96v7_v64w0000gn/T/ipykernel_35414/3351022903.py:38: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  percentiles = df.groupby('Approach')['Latency'].quantile([0.95, 0.99]).unstack()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 400x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from glob import glob\n", "\n", "# Directory containing the .jsonl files\n", "directory = 'gateway-routing-benchmark-result'  # change this to the actual folder path\n", "\n", "# List to store all latency data with their corresponding approach\n", "latency_data = []\n", "\n", "# Define the specific order you want for the approaches\n", "approach_order = ['k8s-service', 'http-route', 'random', 'least-request', 'least-throughput']  # Modify this list based on your desired order\n", "\n", "# Read all .jsonl files and extract latencies\n", "for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "    # Extract the approach name from the file name\n", "    file_name = os.path.basename(file_path)\n", "    approach = file_name.split('.')[0]  # Everything before .jsonl is the approach name\n", "\n", "    # Read the file and extract latencies\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            result = json.loads(line)\n", "            latency = result['latency']\n", "            latency_data.append({'Approach': approach, 'Latency': latency})\n", "\n", "# Convert the list of dictionaries to a DataFrame\n", "df = pd.DataFrame(latency_data)\n", "\n", "# Ensure the 'Approach' column is a categorical dtype with the specified order\n", "df['Approach'] = pd.Categorical(df['Approach'], categories=approach_order, ordered=True)\n", "\n", "# Calculate the 95th and 99th percentiles for each approach\n", "percentiles = df.groupby('Approach')['Latency'].quantile([0.95, 0.99]).unstack()\n", "\n", "# Rename columns for clarity\n", "percentiles.columns = ['95th Percentile', '99th Percentile']\n", "\n", "# Reset the index to make 'Approach' a column again\n", "percentiles = percentiles.reset_index()\n", "\n", "# Melt the DataFrame for easier plotting with seaborn\n", "melted_percentiles = pd.melt(percentiles, id_vars=['Approach'], value_vars=['95th Percentile', '99th Percentile'],\n", "                             var_name='Percentile', value_name='Latency')\n", "\n", "# Order the melted DataFrame for grouped bar plot\n", "melted_percentiles['Percentile'] = pd.Categorical(melted_percentiles['Percentile'], categories=['95th Percentile', '99th Percentile'], ordered=True)\n", "melted_percentiles = melted_percentiles.sort_values(['Percentile', 'Approach'])\n", "\n", "# Plot the grouped bar plot\n", "plt.figure(figsize=(4, 5))\n", "ax = sns.barplot(data=melted_percentiles, x='Percentile', y='Latency', hue='Approach')\n", "\n", "# Add the actual values on each bar\n", "for p in ax.patches:\n", "    if p.get_height() == 0:\n", "        continue\n", "    ax.annotate(f\"{p.get_height():.2f}\", \n", "                (p.get_x() + p.get_width() / 2., p.get_height()), \n", "                ha='center', va='baseline', \n", "                fontsize=8, color='black', xytext=(0, 5), textcoords='offset points')\n", "    \n", "plt.xlabel('Percentile')\n", "plt.ylabel('Latency (seconds)')\n", "plt.title('Comparison of 95th and 99th Percentile Latency by Approach')\n", "plt.grid(True)\n", "plt.legend(title='Approach', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/cw/mdpd8d8j7g10qxq96v7_v64w0000gn/T/ipykernel_35414/2194213145.py:38: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  percentiles = df.groupby('Approach')['Latency'].quantile([0.95, 0.99]).unstack()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from glob import glob\n", "\n", "# Directory containing the .jsonl files\n", "directory = 'gateway-routing-benchmark-result'  # change this to the actual folder path\n", "\n", "# List to store all latency data with their corresponding approach\n", "latency_data = []\n", "\n", "# Define the specific order you want for the approaches\n", "approach_order = ['k8s-service', 'http-route', 'random', 'least-request', 'throughput']  # Modify this list based on your desired order\n", "\n", "# Read all .jsonl files and extract latencies\n", "for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "    # Extract the approach name from the file name\n", "    file_name = os.path.basename(file_path)\n", "    approach = file_name.split('.')[0]  # Everything before .jsonl is the approach name\n", "\n", "    # Read the file and extract latencies\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            result = json.loads(line)\n", "            latency = result['latency']\n", "            latency_data.append({'Approach': approach, 'Latency': latency})\n", "\n", "# Convert the list of dictionaries to a DataFrame\n", "df = pd.DataFrame(latency_data)\n", "\n", "# Ensure the 'Approach' column is a categorical dtype with the specified order\n", "df['Approach'] = pd.Categorical(df['Approach'], categories=approach_order, ordered=True)\n", "\n", "# Calculate the 95th and 99th percentiles for each approach\n", "percentiles = df.groupby('Approach')['Latency'].quantile([0.95, 0.99]).unstack()\n", "\n", "# Rename columns for clarity\n", "percentiles.columns = ['95th Percentile', '99th Percentile']\n", "\n", "# Reset the index to make 'Approach' a column again\n", "percentiles = percentiles.reset_index()\n", "\n", "# Melt the DataFrame for easier plotting with seaborn\n", "melted_percentiles = pd.melt(percentiles, id_vars=['Approach'], value_vars=['95th Percentile', '99th Percentile'],\n", "                             var_name='Percentile', value_name='Latency')\n", "\n", "# Order the melted DataFrame for grouped bar plot\n", "melted_percentiles['Percentile'] = pd.Categorical(melted_percentiles['Percentile'], categories=['95th Percentile', '99th Percentile'], ordered=True)\n", "melted_percentiles = melted_percentiles.sort_values(['Percentile', 'Approach'])\n", "\n", "# Plot the grouped bar plot\n", "plt.figure(figsize=(6, 3))\n", "ax = sns.barplot(data=melted_percentiles, x='Percentile', y='Latency', hue='Approach')\n", "\n", "# Add the actual values on each bar\n", "for p in ax.patches:\n", "    if p.get_height() == 0:\n", "        continue\n", "    ax.annotate(f\"{p.get_height():.2f}\", \n", "                (p.get_x() + p.get_width() / 2., p.get_height()), \n", "                ha='center', va='baseline', \n", "                fontsize=8, color='black', xytext=(0, 5), textcoords='offset points')\n", "    \n", "plt.xlabel('Percentile')\n", "plt.ylabel('Latency (seconds)')\n", "plt.title('Comparison of 95th and 99th Percentile Latency by Approach')\n", "plt.grid(True)\n", "plt.legend(title='Approach', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Combined Diagram"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/cw/mdpd8d8j7g10qxq96v7_v64w0000gn/T/ipykernel_35414/2137258312.py:45: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  percentiles = df.groupby('Approach')['Latency'].quantile([0.95, 0.99]).unstack()\n", "/var/folders/cw/mdpd8d8j7g10qxq96v7_v64w0000gn/T/ipykernel_35414/2137258312.py:61: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.boxplot(data=df, x='Approach', y='Latency', palette=colors, ax=ax1, legend=False)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from glob import glob\n", "import matplotlib.patches as mpatches\n", "\n", "# Directory containing the .jsonl files\n", "directory = 'gateway-routing-benchmark-result'  # change this to the actual folder path\n", "\n", "# Mapping dictionary to replace approach names with descriptive labels\n", "approach_mapping = {\n", "    'k8s-service': 'Kubernetes Service\\nRound Robin',\n", "    'http-route': 'Gateway(HTTP-Route-RR)',\n", "    'random': '<PERSON>(Random)',\n", "    'least-request': 'Gateway(Least-Request)',\n", "    'least-throughput': 'Gateway(Least-Throughput\\n 2*Prompt+Decode)'\n", "}\n", "approach_labels = list(approach_mapping.values())\n", "colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']\n", "approach_order = ['k8s-service', 'http-route', 'random', 'least-request', 'least-throughput']\n", "\n", "# List to store all latency data with their corresponding approach\n", "latency_data = []\n", "\n", "# Read all .jsonl files and extract latencies\n", "for file_path in glob(os.path.join(directory, '*.jsonl')):\n", "    file_name = os.path.basename(file_path)\n", "    approach = file_name.split('.')[0]  # Extract approach name from file name\n", "\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            result = json.loads(line)\n", "            latency = result['latency']\n", "            descriptive_approach = approach_mapping.get(approach, approach)\n", "            latency_data.append({'Approach': descriptive_approach, 'Latency': latency})\n", "\n", "# Convert to DataFrame\n", "df = pd.DataFrame(latency_data)\n", "df['Approach'] = pd.Categorical(df['Approach'], categories=approach_labels, ordered=True)\n", "\n", "# Calculate 95th and 99th percentiles for each approach\n", "percentiles = df.groupby('Approach')['Latency'].quantile([0.95, 0.99]).unstack()\n", "percentiles.columns = ['95th Percentile', '99th Percentile']\n", "percentiles = percentiles.reset_index()\n", "\n", "# Melt the DataFrame for easier plotting\n", "melted_percentiles = pd.melt(percentiles, id_vars=['Approach'], value_vars=['95th Percentile', '99th Percentile'],\n", "                             var_name='Percentile', value_name='Latency')\n", "melted_percentiles['Percentile'] = pd.Categorical(melted_percentiles['Percentile'], \n", "                                                  categories=['95th Percentile', '99th Percentile'], \n", "                                                  ordered=True)\n", "melted_percentiles = melted_percentiles.sort_values(['Percentile', 'Approach'])\n", "\n", "# Create figure with two subplots\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# Plot 1: Box plot for latency distribution\n", "sns.boxplot(data=df, x='Approach', y='Latency', palette=colors, ax=ax1, legend=False)\n", "ax1.set_xticks([])  # Hide x-axis labels as we have a legend\n", "ax1.set_xlabel('')\n", "ax1.set_ylabel('Latency (seconds)')\n", "ax1.set_title('Latency Distribution by Approach')\n", "ax1.grid(True)\n", "\n", "# Plot 2: Bar plot for 95th and 99th percentiles\n", "sns.barplot(data=melted_percentiles, x='Percentile', y='Latency', hue='Approach', palette=colors, ax=ax2, dodge=True, legend=False)\n", "for p in ax2.patches:\n", "    ax2.annotate(f\"{p.get_height():.2f}\", \n", "                 (p.get_x() + p.get_width() / 2., p.get_height()), \n", "                 ha='center', va='baseline', \n", "                 fontsize=8, color='black', xytext=(0, 5), textcoords='offset points')\n", "ax2.set_xlabel('Percentile')\n", "ax2.set_ylabel('Latency (seconds)')\n", "ax2.set_title('Comparison of 95th and 99th Percentile Latency by Approach')\n", "ax2.grid(True)\n", "\n", "# Add a single shared legend for the entire figure\n", "patches = [mpatches.Patch(color=colors[i], label=approach_labels[i]) for i in range(len(approach_labels))]\n", "fig.legend(handles=patches, title=\"Approach\", bbox_to_anchor=(1, 1), loc='upper left')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 4}