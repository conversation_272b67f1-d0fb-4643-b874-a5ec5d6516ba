apiVersion: v1
kind: Service
metadata:
  labels:
    model.aibrix.ai/name: deepseek-llm-7b-chat
    prometheus-discovery: "true"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
  name: deepseek-llm-7b-chat
  namespace: default
spec:
  ports:
    - name: serve
      port: 8000
      protocol: TCP
      targetPort: 8000
  selector:
    model.aibrix.ai/name: deepseek-llm-7b-chat
  type: LoadBalancer
