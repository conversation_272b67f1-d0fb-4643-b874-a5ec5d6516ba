apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    model.aibrix.ai/name: deepseek-coder-7b-instruct
    model.aibrix.ai/port: "8000"
  name: deepseek-coder-7b-instruct
  namespace: aibrix-system
spec:
  replicas: 8
  selector:
    matchLabels:
      model.aibrix.ai/name: deepseek-coder-7b-instruct
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
      labels:
        model.aibrix.ai/name: deepseek-coder-7b-instruct
    spec:
      terminationGracePeriodSeconds: 300
      containers:
        - command:
            - python3
            - -m
            - vllm.entrypoints.openai.api_server
            - --host
            - "0.0.0.0"
            - --port
            - "8000"
            - --model
            - /models/deepseek-coder-6.7b-instruct
            - --served-model-name
            - deepseek-coder-7b-instruct
            - --trust-remote-code
            - --max-model-len
            - "10240"
            - --api-key
            - sk-kFJ12nKsFVfVmGpj3QzX65s4RbN2xJqWzPYCjYu7wT3BlbLi
          image: aibrix-container-registry-cn-beijing.cr.volces.com/aibrix/vllm-openai:v0.6.2-distributed
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
          name: vllm-openai
          ports:
            - containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              nvidia.com/gpu: "1"
            requests:
              nvidia.com/gpu: "1"
          # We need to use dataset cache
          volumeMounts:
            - mountPath: /models
              name: model-hostpath
            - name: dshm
              mountPath: /dev/shm
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - |
                    while true; do
                      RUNNING=$(curl -s http://localhost:8000/metrics | grep 'vllm:num_requests_running' | grep -v '#' | awk '{print $2}')
                      WAITING=$(curl -s http://localhost:8000/metrics | grep 'vllm:num_requests_waiting' | grep -v '#' | awk '{print $2}')
                      if [ "$RUNNING" = "0.0" ] && [ "$WAITING" = "0.0" ]; then
                        echo "Terminating: No active or waiting requests, safe to terminate" >> /proc/1/fd/1
                        exit 0
                      else
                        echo "Terminating: Running: $RUNNING, Waiting: $WAITING" >> /proc/1/fd/1
                        sleep 5
                      fi
                    done
        - name: aibrix-runtime
          image: aibrix-container-registry-cn-beijing.cr.volces.com/aibrix/runtime:v0.3.0
          command:
            - aibrix_runtime
            - --port
            - "8080"
          env:
            - name: INFERENCE_ENGINE
              value: vllm
            - name: INFERENCE_ENGINE_ENDPOINT
              value: http://localhost:8000
          ports:
            - containerPort: 8080
              protocol: TCP
          volumeMounts:
            - mountPath: /models
              name: model-hostpath
          livenessProbe:
            httpGet:
              path: /healthz
              port: 8080
            initialDelaySeconds: 3
            periodSeconds: 2
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
      initContainers:
        - name: init-model
          image: aibrix-container-registry-cn-beijing.cr.volces.com/aibrix/runtime:v0.3.0
          command:
            - aibrix_download
            - --model-uri
            - tos://aibrix-artifact-testing/models/deepseek-ai/deepseek-coder-6.7b-instruct/
            - --local-dir
            - /models/
          env:
            - name: DOWNLOADER_MODEL_NAME
              value: deepseek-coder-6.7b-instruct
            - name: DOWNLOADER_NUM_THREADS
              value: "16"
            - name: DOWNLOADER_ALLOW_FILE_SUFFIX
              value: json, safetensors
            - name: TOS_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: tos-credential
                  key: TOS_ACCESS_KEY
            - name: TOS_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: tos-credential
                  key: TOS_SECRET_KEY
            - name: TOS_ENDPOINT
              value: tos-cn-beijing.ivolces.com
            - name: TOS_REGION
              value: cn-beijing
          volumeMounts:
            - mountPath: /models
              name: model-hostpath
      volumes:
        - name: model-hostpath
          hostPath:
            path: /root/models
            type: DirectoryOrCreate
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: "4Gi"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: machine.cluster.vke.volcengine.com/gpu-name
                    operator: In
                    values:
                      - NVIDIA-A10
---
apiVersion: v1
kind: Service
metadata:
  labels:
    model.aibrix.ai/name: deepseek-coder-7b-instruct
    prometheus-discovery: "true"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
  name: deepseek-coder-7b-instruct
  namespace: aibrix-system
spec:
  ports:
    - name: serve
      port: 8000
      protocol: TCP
      targetPort: 8000
  selector:
    model.aibrix.ai/name: deepseek-coder-7b-instruct
  type: LoadBalancer
