apiVersion: v1
kind: Pod
metadata:
  name: infinistore
  namespace: default
  labels:
    app: infinistore
  annotations:
    k8s.volcengine.com/pod-networks: |
        [
          {
            "cniConf":{
                "name":"rdma"
            }
          }
        ]
spec:
  containers:
  - name: infinistore
    image: aibrix-cn-beijing.cr.volces.com/aibrix/infinistore:v0.2.42-20250506
    command: ["infinistore"]
    args:
      - "--manage-port=8088"
      - "--dev-name=mlx5_0" # if mlx5_0 is not available, it will fallbacks to other devs
      - "--service-port=12345"
      - "--link-type=Ethernet"
      - "--hint-gid-index=7"
      - "--log-level=debug"
      - "--prealloc-size=950"
    ports:
      - containerPort: 8088
      - containerPort: 12345
    securityContext:
      capabilities:
        add:
        - IPC_LOCK
        - SYS_RESOURCE
    resources:
      requests:
        cpu: "10"
        memory: "960Gi"
        vke.volcengine.com/rdma: "1"
      limits:
        cpu: "10"
        memory: "960Gi"
        vke.volcengine.com/rdma: "1"
  restartPolicy: Never

---
apiVersion: v1
kind: Service
metadata:
  name: infinistore-svc
  namespace: default
spec:
  type: ClusterIP
  selector:
    app: infinistore
  ports:
    - name: manage
      port: 8088
      targetPort: 8088
    - name: service
      port: 12345
      targetPort: 12345
