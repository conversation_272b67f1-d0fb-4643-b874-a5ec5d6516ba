apiVersion: apps/v1
kind: Deployment
metadata:
  name: llama3-1-8b
  labels:
    model.aibrix.ai/name: llama3-1-8b
    model.aibrix.ai/port: "8000"
spec:
  replicas: 8
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  selector:
    matchLabels:
      model.aibrix.ai/name: llama3-1-8b
  template:
    metadata:
      labels:
        model.aibrix.ai/name: llama3-1-8b
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: machine.cluster.vke.volcengine.com/gpu-name
                    operator: In
                    values:
                      - Tesla-A100-80G
                      - NVIDIA-H20-SXM5-96GB
      initContainers:
        - command:
            - aibrix_download
            - --model-uri
            - tos://aibrix-artifact-testing/models/llama-3.1-8b-instruct/
            - --local-dir
            - /models/
          env:
            - name: DOWNLOADER_NUM_THREADS
              value: "16"
            - name: DOWNLOADER_ALLOW_FILE_SUFFIX
              value: json, safetensors
            - name: TOS_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  key: TOS_ACCESS_KEY
                  name: tos-credential
            - name: TOS_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  key: TOS_SECRET_KEY
                  name: tos-credential
            - name: TOS_ENDPOINT
              value: https://tos-s3-cn-beijing.ivolces.com
            - name: TOS_REGION
              value: cn-beijing
          image: aibrix-cn-beijing.cr.volces.com/aibrix/runtime:v0.3.0
          name: init-model
          volumeMounts:
            - mountPath: /models
              name: model-hostpath
      containers:
        - name: vllm-openai
          image: aibrix-cn-beijing.cr.volces.com/aibrix/vllm-openai:v0.8.5
          imagePullPolicy: Always
          command:
            - python3
            - -m
            - vllm.entrypoints.openai.api_server
            - --port
            - "8000"
            - --uvicorn-log-level
            - warning
            - --model
            - /models/llama-3.1-8b-instruct/
            - --served-model-name
            - llama3-1-8b
            - --max-model-len
            - "32000" # please modify this field if your gpu has more room
            - --enable-prefix-caching
            - --disable-fastapi-docs
            - --trust-remote-code
            - --no-enable-chunked-prefill
            - --disable-log-requests
            - --swap-space
            - "0"
          env:
            - name: VLLM_RPC_TIMEOUT
              value: "1000000"
          volumeMounts:
            - mountPath: /models
              name: model-hostpath
          resources:
            limits:
              nvidia.com/gpu: "1"
              cpu: "10"
              memory: "150G"
            requests:
              nvidia.com/gpu: "1"
              cpu: "10"
              memory: "150G"
      volumes:
        - name: model-hostpath
          hostPath:
            path: /root/models
            type: DirectoryOrCreate

---

apiVersion: v1
kind: Service
metadata:
  labels:
    model.aibrix.ai/name: llama3-1-8b
    prometheus-discovery: "true"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
  name: llama3-1-8b # Note: The Service name must match the label value `model.aibrix.ai/name` in the Deployment
  namespace: default
spec:
  ports:
    - name: serve
      port: 8000
      protocol: TCP
      targetPort: 8000
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    model.aibrix.ai/name: llama3-1-8b
  type: ClusterIP
