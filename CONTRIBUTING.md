## About AIBrix

For instructions on building, installing, deploying, and testing this project, 
please see the [Development Guide](development/README.md) in the `docs/development` directory.

## How to Contribute

### 0. Coding Guidelines

- **Style Guide**: The [Go style](https://google.github.io/styleguide/go/) guide is a good standard to follow.
- **Commenting Code**: Write clear comments that explain the "why" not just the "how".

### 1. Understanding the Repository Structure

You can refer to the [Design Doc](https://github.com/vllm-project/aibrix/tree/main/docs/tutorial) to quickly understand the layout of the AIBrix directory structure.

### 2. Picking an Issue

Start by picking an [issue](https://github.com/vllm-project/aibrix/issues) tagged with "good first issue" to get familiar with the project. Claim the issue by commenting to avoid duplicate efforts.

### 3. Submitting Codes

- **Creating Branches**: Use the following naming convention: `/<YOUR_NAME>/[feat|patch|bug_fix] description`
- **Making Commits**: Write detailed commit messages that explain your changes.
- **Pull Requests**:
  - Create a pull request on [PR Page](https://github.com/vllm-project/aibrix/pulls) against the master branch.
  - Use the provided PR template to describe your changes.
- **Code Review Process**: Your PR will be reviewed by related contributors. Address feedback to improve and finalize your contribution.

## Advanced Contributions

- **Feature Proposals and Bug Reporting**: Report bugs or propose new features by creating an issue on our GitHub page. Provide as much detail as possible to facilitate discussions.
- **Contributing to Documentation**: Help improve our documentation by proposing changes to issue list. Our documentation is at [AIBrix Doc](https://github.com/vllm-project/aibrix/tree/main/docs/tutorial)
- **Organizing Community Events**: If you are interested in organizing meetups or webinars, contact our community manager (TODO: contact information).

## Community and Communication

Join our community through various channels:

- **Slack Channl**: TODO
- **Wechat User Groups**: TODO
- **Email**: TODO
- **Forums**: TODO

## FAQ

TODO: Frequently asked questions and answers for new contributors.

## Contact

- **Maintainers**: TODO
