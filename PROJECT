# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: aibrix.ai
layout:
- go.kubebuilder.io/v4
multigroup: true
projectName: aibrix
repo: github.com/vllm-project/aibrix
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: aibrix.ai
  group: autoscaling
  kind: PodAutoscaler
  path: github.com/vllm-project/aibrix/api/autoscaling/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: aibrix.ai
  group: model
  kind: ModelAdapter
  path: github.com/vllm-project/aibrix/api/model/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: aibrix.ai
  group: orchestration
  kind: RayClusterReplicaSet
  path: github.com/vllm-project/aibrix/api/orchestration/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: aibrix.ai
  group: orchestration
  kind: RayClusterFleet
  path: github.com/vllm-project/aibrix/api/orchestration/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: aibrix.ai
  group: orchestration
  kind: KVCache
  path: github.com/vllm-project/aibrix/api/orchestration/v1alpha1
  version: v1alpha1
version: "3"
