# Pod autoscaler works with gpu-optimizer
apiVersion: autoscaling.aibrix.ai/v1alpha1
kind: PodAutoscaler
metadata:
  name: podautoscaler-mock-llama2-7b
  labels:
    app.kubernetes.io/name: aibrix
    app.kubernetes.io/managed-by: kustomize
    autoscaling.aibrix.ai/up-fluctuation-tolerance: "0.1"
    autoscaling.aibrix.ai/down-fluctuation-tolerance: "0.2"
    apa.autoscaling.aibrix.ai/window: "30s"
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mock-llama2-7b
  minReplicas: 0
  maxReplicas: 10
  metricsSources:
    - metricSourceType: pod
      protocolType: http
      port: "8000"
      path: metrics
      targetMetric: "avg_prompt_throughput_toks_per_s"
      targetValue: "60"
  scalingStrategy: "APA"