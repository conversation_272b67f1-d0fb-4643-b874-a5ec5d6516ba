apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-llama2-7b
  namespace: default
  labels:
    model.aibrix.ai/name: "llama2-7b"
    model.aibrix.ai/port: "8000"
    adapter.model.aibrix.ai/enabled: "true"
spec:
  replicas: 3
  selector:
    matchLabels:
      adapter.model.aibrix.ai/enabled: "true"
      model.aibrix.ai/name: "llama2-7b"
      app: "mock-llama2-7b"
  template:
    metadata:
      labels:
        adapter.model.aibrix.ai/enabled: "true"
        model.aibrix.ai/name: "llama2-7b"
        app: "mock-llama2-7b"
    spec:
      serviceAccountName: mocked-app-sa
      containers:
        - name: llm-engine
          image: aibrix/vllm-mock:nightly
          ports:
            - containerPort: 8000
          env:
            - name: DEPLOYMENT_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['app']
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
        - name: aibrix-runtime
          image: aibrix/runtime:nightly
          command:
            - aibrix_runtime
            - --port
            - "8080"
          env:
            - name: INFERENCE_ENGINE
              value: vllm
            - name: INFERENCE_ENGINE_ENDPOINT
              value: http://localhost:8000
          ports:
            - containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /healthz
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 1
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 1