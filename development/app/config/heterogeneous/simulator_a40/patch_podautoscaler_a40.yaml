# Pod autoscaler works with gpu-optimizer
apiVersion: autoscaling.aibrix.ai/v1alpha1
kind: PodAutoscaler
metadata:
  name: podautoscaler-simulator-llama2-7b-a40
  labels:
    kpa.autoscaling.aibrix.ai/scale-down-delay: 0s
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: simulator-llama2-7b-a40
  metricsSources: 
    - metricSourceType: domain
      protocolType: http
      endpoint: aibrix-gpu-optimizer.aibrix-system.svc.cluster.local:8080
      path: /metrics/default/simulator-llama2-7b-a40
      targetMetric: "vllm:deployment_replicas"
      targetValue: "100" # For stable workloads. Set to a fraction to tolerate bursts.