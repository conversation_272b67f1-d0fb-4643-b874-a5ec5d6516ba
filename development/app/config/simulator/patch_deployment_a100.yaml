apiVersion: apps/v1
kind: Deployment
metadata:
  name: simulator-llama2-7b-a100
  labels:
    model.aibrix.ai/name: "llama2-7b"
    model.aibrix.ai/min_replicas: "1" # min replica for gpu optimizer when no workloads.
spec:
  replicas: 1
  selector:
    matchLabels:
      model.aibrix.ai/name: "llama2-7b"
      app: "simulator-llama2-7b-a100"
  template:
    metadata:
      labels:
        model.aibrix.ai/name: "llama2-7b"
        app: "simulator-llama2-7b-a100"
    spec:
      containers:
        - name: llm-engine
          image: aibrix/vllm-simulator:nightly
          env:
            - name: MODEL_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['model.aibrix.ai/name']