apiVersion: ray.io/v1
kind: RayJob
metadata:
  name: vllm-server
spec:
  # submissionMode specifies how <PERSON><PERSON><PERSON> submits the Ray job to the RayCluster.
  # The default value is "K8sJobMode", meaning <PERSON><PERSON><PERSON> will submit the Ray job via a submitter <PERSON><PERSON><PERSON><PERSON> Job.
  # The alternative value is "HTTPMode", indicating that <PERSON><PERSON><PERSON><PERSON> will submit the Ray job by sending an HTTP request to the RayCluster.
  # submissionMode: "K8sJobMode"
  entrypoint: vllm serve facebook/opt-13b --tensor-parallel-size 2 --distributed-executor-backend ray
  # shutdownAfterJobFinishes specifies whether the RayCluster should be deleted after the RayJob finishes. Default is false.
  # shutdownAfterJobFinishes: false

  # ttlSecondsAfterFinished specifies the number of seconds after which the RayCluster will be deleted after the RayJob finishes.
  # ttlSecondsAfterFinished: 10

  # activeDeadlineSeconds is the duration in seconds that the RayJob may be active before
  # KubeRay actively tries to terminate the RayJob; value must be positive integer.
  # activeDeadlineSeconds: 120

  # RuntimeEnvYAML represents the runtime environment configuration provided as a multi-line YAML string.
  # See https://docs.ray.io/en/latest/ray-core/handling-dependencies.html for details.
  # (New in KubeRay version 1.0.)
  runtimeEnvYAML: |
    pip:
      - requests
    env_vars:
      counter_name: "test_counter"

  # Suspend specifies whether the RayJob controller should create a RayCluster instance.
  # If a job is applied with the suspend field set to true, the RayCluster will not be created and we will wait for the transition to false.
  # If the RayCluster is already created, it will be deleted. In the case of transition to false, a new RayCluste rwill be created.
  # suspend: false

  # rayClusterSpec specifies the RayCluster instance to be created by the RayJob controller.
  rayClusterSpec:
    rayVersion: '2.32.0'
    headGroupSpec:
      rayStartParams:
        dashboard-host: '0.0.0.0'
      template:
        spec:
          containers:
          - name: ray-head
            image: aibrix/vllm-openai:v0.5.2-distributed
            resources:
              limits:
                cpu: "8"
                nvidia.com/gpu: "1"
              requests:
                cpu: "8"
                nvidia.com/gpu: "1"
            volumeMounts:
            - mountPath: /tmp/ray
              name: ray-logs
            - mountPath: /root/.cache/huggingface
              name: models
          volumes:
          - name: ray-logs
            emptyDir: {}
          - name: models
            hostPath:
              path: /root/.cache/huggingface
              type: DirectoryOrCreate
    workerGroupSpecs:
    - replicas: 1
      minReplicas: 1
      maxReplicas: 1
      groupName: small-group
      rayStartParams: {}
      template:
        spec:
          containers:
          - name: ray-worker
            image: aibrix/vllm-openai:v0.5.2-distributed
            volumeMounts:
              - mountPath: /tmp/ray
                name: ray-logs
              - mountPath: /root/.cache/huggingface
                name: models
            resources:
              limits:
                cpu: "8"
                nvidia.com/gpu: "1"
              requests:
                cpu: "8"
                nvidia.com/gpu: "1"
          volumes:
            - name: ray-logs
              emptyDir: {}
            - name: models
              hostPath:
                path: /root/.cache/huggingface
                type: DirectoryOrCreate
