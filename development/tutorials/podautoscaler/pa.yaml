apiVersion: autoscaling.aibrix.ai/v1alpha1
kind: PodAutoscaler
metadata:
  name: llama2-70b-pa
  labels:
    app.kubernetes.io/name: aibrix
    app.kubernetes.io/managed-by: kustomize
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mock-llama2-7b
  minReplicas: 1
  maxReplicas: 10
  metricsSources:
  - metricSourceType: "pod"
    protocolType: "http"
    port: "8000"
    path: "/metrics"
    targetMetric: "gpu_cache_usage_perc"
    targetValue: "40"
  scalingStrategy: "HPA"
