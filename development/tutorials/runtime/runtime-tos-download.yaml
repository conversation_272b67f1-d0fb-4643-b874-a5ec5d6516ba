apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    model.aibrix.ai/name: deepseek-coder-7b-instruct
    model.aibrix.ai/port: "8000"
    adapter.model.aibrix.ai/enabled: "true"
  name: aibrix-model-deepseek-coder-7b-instruct
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      model.aibrix.ai/name: deepseek-coder-7b-instruct
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        model.aibrix.ai/name: deepseek-coder-7b-instruct
    spec:
      containers:
      - command:
        - python3
        - -m
        - vllm.entrypoints.openai.api_server
        - --host
        - "0.0.0.0"
        - --port
        - "8000"
        - --model
        - /models/deepseek-coder-7b-instruct
        - --served-model-name
        - deepseek-coder-7b-instruct
        - --distributed-executor-backend
        - ray
        - --trust-remote-code
        image: vllm/vllm-openai:v0.5.5
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 90
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 1
        name: vllm-openai
        ports:
        - containerPort: 8000
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 90
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            nvidia.com/gpu: "1"
          requests:
            nvidia.com/gpu: "1"
        # We need to use dataset cache
        volumeMounts:
        - mountPath: /models
          name: model-hostpath
        - name: dshm
          mountPath: /dev/shm
      - name: aibrix-runtime
        image: aibrix/runtime:latest
        command:
        - aibrix_runtime
        - --port
        - "8080"
        env:
          - name: INFERENCE_ENGINE
            value: vllm
          - name: INFERENCE_ENGINE_ENDPOINT
            value: http://localhost:8000
        ports:
        - containerPort: 8080
          protocol: TCP
        volumeMounts:
        - mountPath: /models
          name: model-hostpath
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 3
          periodSeconds: 2
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      initContainers:
      - name: model-init
        image: aibrix/runtime:latest
        command:
        - python
        - -m
        - aibrix.downloader
        - --model-uri
        - tos://<input your tos bucket name>/<input your tos bucket path>
        - --local-dir
        - /models/
        - --model-name
        - deepseek-coder-7b-instruct
        env:
        - name: DOWNLOADER_ALLOW_FILE_SUFFIX
          value: json, safetensors
        - name: TOS_ACCESS_KEY
          value: <input your tos access key>
        - name: TOS_SECRET_KEY
          value: <input your tos secret key>
        - name: TOS_ENDPOINT
          value: <input your tos endpoint>
        - name: TOS_REGION
          value: <input your tos region>
        volumeMounts:
        - mountPath: /models
          name: model-hostpath
      volumes:
      - emptyDir: {}
        name: model-hostpath
      - name: dshm
        emptyDir:
          medium: Memory
          sizeLimit: "10Gi"

---

apiVersion: v1
kind: Service
metadata:
  labels:
    model.aibrix.ai/name: deepseek-coder-7b-instruct
    prometheus-discovery: "true"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
  name: deepseek-coder-7b-instruct
  namespace: default
spec:
  ports:
  - name: serve
    port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    model.aibrix.ai/name: deepseek-coder-7b-instruct
  type: ClusterIP
